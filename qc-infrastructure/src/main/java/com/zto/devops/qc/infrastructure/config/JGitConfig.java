package com.zto.devops.qc.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.internal.storage.file.WindowCache;
import org.eclipse.jgit.internal.storage.file.WindowCacheConfig;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * JGit WindowCache 配置优化
 * 解决JGit WindowCache内存泄漏问题
 * 
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class JGitConfig {

    @PostConstruct
    public void configureJGitWindowCache() {
        try {
            WindowCacheConfig config = new WindowCacheConfig();
            
            // 设置WindowCache的最大内存限制为256MB（默认是10MB * CPU核数）
            // 这样可以防止WindowCache无限制增长
            config.setPackedGitLimit(256 * 1024 * 1024); // 256MB
            
            // 设置单个窗口的大小为8KB（默认8KB）
            config.setPackedGitWindowSize(8 * 1024);
            
            // 设置内存映射的限制为128MB
            config.setPackedGitMMAP(128 * 1024 * 1024);
            
            // 设置打开文件的最大数量
            config.setPackedGitOpenFiles(128);
            
            // 应用配置
            WindowCache.reconfigure(config);
            
            log.info("JGit WindowCache configured successfully - PackedGitLimit: {}MB, WindowSize: {}KB, MMAP: {}MB, OpenFiles: {}", 
                    config.getPackedGitLimit() / (1024 * 1024),
                    config.getPackedGitWindowSize() / 1024,
                    config.getPackedGitMMAP() / (1024 * 1024),
                    config.getPackedGitOpenFiles());
                    
        } catch (Exception e) {
            log.error("Failed to configure JGit WindowCache", e);
        }
    }
}
