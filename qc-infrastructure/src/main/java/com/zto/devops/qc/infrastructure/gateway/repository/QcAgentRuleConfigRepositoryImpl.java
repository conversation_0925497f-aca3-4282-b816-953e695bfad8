package com.zto.devops.qc.infrastructure.gateway.repository;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.agent.AgentCommandEnum;
import com.zto.devops.qc.client.enums.agent.ChaosExceptionTypeEnum;
import com.zto.devops.qc.client.enums.agent.ChaosRuleTypeEnum;
import com.zto.devops.qc.client.model.dto.QcAgentRuleConfigEntityDO;
import com.zto.devops.qc.client.service.agent.model.*;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.repository.QcAgentRuleConfigRepository;
import com.zto.devops.qc.infrastructure.converter.QcAgentRuleConfigEntityConverter;
import com.zto.devops.qc.infrastructure.dao.entity.QcAgentRuleConfigEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.QcAgentRuleConfigMapper;
import com.zto.devops.qc.infrastructure.gateway.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class QcAgentRuleConfigRepositoryImpl implements QcAgentRuleConfigRepository {

    private static final String AGENT_PREFIX = "AGENT_IP";

    private static ThreadPoolExecutor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(10, 50, 10, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(100),
            ThreadFactoryBuilder.create().setNamePrefix("EXECUTOR-AGENT-").build());

    @Autowired
    private RedisService redisService;
    @Autowired
    private QcAgentRuleConfigMapper qcAgentRuleConfigMapper;
    @Autowired
    private QcAgentRuleConfigEntityConverter qcAgentRuleConfigEntityConverter;

    @Override
    public Boolean newRuleConfig(RuleConfigReq req, User transactor) {
        QcAgentRuleConfigEntity configEntity = new QcAgentRuleConfigEntity();
        configEntity.setProductCode(req.getProductCode());
        configEntity.setInjectionRuleName(req.getInjectionRuleName());
        configEntity.setAppid(req.getAppid());
        configEntity.setVersionCode(req.getVersionCode());
        if (req.getRuleType() == ChaosRuleTypeEnum.DubboClient) {
            if (StringUtil.isEmpty(req.getMethodName())) {
                if (req.getClassName().contains("#")) {
                    String[] classNameAndMethod = req.getClassName().split("#");
                    if (classNameAndMethod.length == 2) {
                        req.setClassName(classNameAndMethod[0].trim());
                        req.setMethodName(classNameAndMethod[1].trim());
                    } else {
                        throw new ServiceException("注入项格式不正确，格式应该是【类名#方法名】");
                    }
                }
            }
        }
        configEntity.setHeuristicClassName(req.getClassName());
        configEntity.setMethodName(req.getMethodName());
        configEntity.setRuleType(req.getRuleType().getName());
        configEntity.setChaosExceptionType(req.getExceptionType().getName());
        configEntity.setInjection(setInjection(req));
        configEntity.setStatus(1);
        configEntity.setEnable(1);
        configEntity.setRuleBody(req.getRuleBody());
        configEntity.preCreate(transactor);
        duplicateNameVerify(configEntity);
        duplicateInjectionVerify(configEntity);
        qcAgentRuleConfigMapper.insert(configEntity);
        this.configExchange(req.getAppid(), req.getVersionCode());
        return true;
    }

    @Override
    public Boolean modifyRuleConfig(RuleConfigReq req, User transactor) {
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("id", req.getId());
        QcAgentRuleConfigEntity sourceEntity = qcAgentRuleConfigMapper.selectOneByExample(example);
        if (null == sourceEntity) {
            throw new ServiceException("规则不存在");
        }
        QcAgentRuleConfigEntity configEntity = new QcAgentRuleConfigEntity();
        configEntity.setProductCode(req.getProductCode());
        configEntity.setVersionCode(req.getVersionCode());
        configEntity.setId(req.getId());
        configEntity.setInjection(setInjection(req));
        configEntity.setInjectionRuleName(req.getInjectionRuleName());
        configEntity.setChaosExceptionType(req.getExceptionType().name());
        configEntity.preUpdate(transactor);
        configEntity.setRuleBody(req.getRuleBody());
        configEntity.setRuleType(req.getRuleType().getName());
        configEntity.setHeuristicClassName(req.getClassName());
        duplicateNameVerify(configEntity);
        duplicateInjectionVerify(configEntity);
        qcAgentRuleConfigMapper.updateByExampleSelective(configEntity, example);
        this.configExchange(req.getAppid(), req.getVersionCode());
        return true;
    }

    @Override
    public Boolean batchOptRuleConfig(BatchOptRuleConfigReq batchOptRuleConfigReq, User transactor) {
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria();
        if (CollectionUtil.isNotEmpty(batchOptRuleConfigReq.getIds())) {
            criteria.andIn("id", batchOptRuleConfigReq.getIds());
        }
        QcAgentRuleConfigEntity configEntity = new QcAgentRuleConfigEntity();
        if (batchOptRuleConfigReq.getEnable() != null) {
            configEntity.setEnable(batchOptRuleConfigReq.getEnable());
        }
        if (batchOptRuleConfigReq.getStatus() != null) {
            configEntity.setStatus(batchOptRuleConfigReq.getStatus());
        }
        configEntity.preUpdate(transactor);
        qcAgentRuleConfigMapper.updateByExampleSelective(configEntity, example);
        List<QcAgentRuleConfigEntity> entities = queryAppIdsByIds(batchOptRuleConfigReq.getIds());
        for (QcAgentRuleConfigEntity entity : entities) {
            this.configExchange(entity.getAppid(), entity.getVersionCode());
        }
        return true;
    }

    @Override
    public Boolean removeRuleConfig(RuleConfigReq req, User transactor) {
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", req.getId())
                .andEqualTo("appid", req.getAppid())
                .andEqualTo("versionCode", req.getVersionCode());
        QcAgentRuleConfigEntity configEntity = new QcAgentRuleConfigEntity();
        configEntity.setEnable(0);
        configEntity.preUpdate(transactor);
        qcAgentRuleConfigMapper.updateByExampleSelective(configEntity, example);
        this.configExchange(req.getAppid(), req.getVersionCode());
        return true;
    }

    @Override
    public void modifyRuleStatus(RuleConfigReq req, User transactor) {
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", req.getId())
                .andEqualTo("appid", req.getAppid())
                .andEqualTo("versionCode", req.getVersionCode());
        QcAgentRuleConfigEntity configEntity = new QcAgentRuleConfigEntity();
        if (null != req.getStatus()) {
            configEntity.setStatus(req.getStatus());
        }
        configEntity.preUpdate(transactor);
        qcAgentRuleConfigMapper.updateByExampleSelective(configEntity, example);
        this.configExchange(req.getAppid(), req.getVersionCode());
    }

    @Override
    public List<String> isContainChaosRule(IsIncludeChaosRuleReq req) {
        if (CollectionUtil.isEmpty(req.getVersionCodes())) {
            return Collections.EMPTY_LIST;
        }
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("versionCode", req.getVersionCodes());
        criteria.andEqualTo("enable", 1);
        criteria.andEqualTo("status", 1);
        example.setDistinct(true);
        example.selectProperties("versionCode");
        List<QcAgentRuleConfigEntity> entityList = qcAgentRuleConfigMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(entityList)) {
            return Collections.EMPTY_LIST;
        }
        return entityList.stream().map(QcAgentRuleConfigEntity::getVersionCode).collect(Collectors.toList());
    }

    @Override
    public List<QcAgentRuleConfigEntityDO> queryAgentRuleConfigPage(PageRuleConfigReq req) {
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", 1);
        if (StringUtil.isNotEmpty(req.getVersionCode())) {
            criteria.andEqualTo("versionCode", req.getVersionCode());
        }
        if (StringUtil.isNotEmpty(req.getProductCode())) {
            criteria.andEqualTo("productCode", req.getProductCode());
        }
        if (CollectionUtil.isNotEmpty(req.getAppIds())) {
            criteria.andIn("appid", req.getAppIds());
        }
        if (CollectionUtil.isNotEmpty(req.getStatusList())) {
            criteria.andIn("status", req.getStatusList());
        }
        if (CollectionUtil.isNotEmpty(req.getRuleTypes())) {
            criteria.andIn("ruleType", req.getRuleTypes());
        }
        if (CollectionUtil.isNotEmpty(req.getExceptionTypes())) {
            criteria.andIn("chaosExceptionType", req.getExceptionTypes());
        }
        if (StringUtil.isNotEmpty(req.getInjection())) {
            criteria.andLike("injection", "%" + req.getInjection() + "%");
        }
        if (StringUtil.isNotEmpty(req.getInjectionRuleName())) {
            criteria.andLike("injectionRuleName", "%" + req.getInjectionRuleName() + "%");
        }
        example.orderBy("id").desc();

        List<QcAgentRuleConfigEntity> qcAgentRuleConfigEntityList = qcAgentRuleConfigMapper.selectByExample(example);

        return qcAgentRuleConfigEntityConverter.convert2DOList(qcAgentRuleConfigEntityList);
    }

    @Override
    public List<QcAgentRuleConfigEntityDO> queryAgentRuleConfig(RuleConfigReq req) {
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", 1);
        criteria.andEqualTo("versionCode", req.getVersionCode());
        if (CollectionUtil.isNotEmpty(req.getStatusList())) {
            criteria.andIn("status", req.getStatusList());
        } else {
            criteria.andIn("status", Collections.singleton(1));
        }
        if (StringUtil.isNotEmpty(req.getAppid())) {
            criteria.andEqualTo("appid", req.getAppid());
        }
        if (req.getRuleType() != null) {
            criteria.andEqualTo("ruleType", req.getRuleType().name());
        }
        if (req.getExceptionType() != null) {
            criteria.andEqualTo("chaosExceptionType", req.getExceptionType().name());
        }
        if (StringUtil.isNotEmpty(req.getClassName())) {
            criteria.andLike("heuristicClassName", req.getClassName() + "%");
        }
        example.orderBy("id").desc();
        List<QcAgentRuleConfigEntity> qcAgentRuleConfigEntityList = qcAgentRuleConfigMapper.selectByExample(example);

        return qcAgentRuleConfigEntityConverter.convert2DOList(qcAgentRuleConfigEntityList);
    }

    @Override
    public QcAgentRuleConfigEntityDO queryAgentRuleConfig(Long id) {
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("id", id)
                .andEqualTo("enable", 1);
        QcAgentRuleConfigEntity agentRuleConfigEntity = qcAgentRuleConfigMapper.selectOneByExample(example);
        return qcAgentRuleConfigEntityConverter.convert2DO(agentRuleConfigEntity);
    }

    @Override
    public void batchRemoveByVersionCode(String versionCode) {
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("versionCode", versionCode);
        QcAgentRuleConfigEntity configEntity = new QcAgentRuleConfigEntity();
        configEntity.setEnable(0);
        qcAgentRuleConfigMapper.updateByExampleSelective(configEntity, example);
    }

    private void configExchange(String appId, String versionCode) {
        if (StringUtil.isEmpty(appId) || StringUtil.isEmpty(versionCode)) {
            return;
        }
        THREAD_POOL_EXECUTOR.execute(() -> {
            try {
                Map<String, String> command = new HashMap<>();
                command.put("command", AgentCommandEnum.EXCHANGE.name());
                String key = AGENT_PREFIX + "_" + appId + "_" + versionCode;
                Set<String> ipSet = redisService.opsForZSetRang(key, 0, -1);
                log.info("configExchange ips : {}", ipSet);
                for (String ip : ipSet) {
                    try {
                        HttpUtils.doPost(String.format("http://%s:29090/command", ip), JSON.toJSONString(command));
                        log.info("doPost success. ip : {}", ip);
                    } catch (ServiceException e) {
                        log.warn("configExchange-{} call agent is error:", ip, e);
                    } catch (Exception ex) {
                        log.warn("configExchange-{} call agent is error:", ip, ex);
                    }
                }
            } catch (Exception e) {
                log.error("configExchange error: ", e);
            }
        });
    }

    private String setInjection(RuleConfigReq req) {
        Map<String, Object> ruleBody = req.getRuleBody();
        if (req.getRuleType().equals(ChaosRuleTypeEnum.HttpClient) || req.getRuleType().equals(ChaosRuleTypeEnum.DubboClient)) {
            if (StringUtil.isNotEmpty(req.getMethodName())) {
                return req.getClassName() + "#" + req.getMethodName();
            } else {
                return req.getClassName();
            }
        } else {
            if (MapUtils.isNotEmpty(ruleBody) && ruleBody.containsKey("lineNumber")) {
                return req.getClassName() + ":" + ruleBody.get("lineNumber");
            }
        }
        return "";
    }

    private List<QcAgentRuleConfigEntity> queryAppIdsByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        example.setDistinct(true);
        example.selectProperties("appid");
        example.selectProperties("versionCode");
        List<QcAgentRuleConfigEntity> entityList = qcAgentRuleConfigMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return entityList;
    }

    /**
     * 重名校验
     *
     * @param entity
     */
    private void duplicateNameVerify(QcAgentRuleConfigEntity entity) {
        if (StringUtil.isEmpty(entity.getInjection())) {
            throw new ServiceException("注入项不能为空");
        }
        if (entity.getRuleType().equals(ChaosRuleTypeEnum.ServiceClass.name())) {
            return;
        }
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", 1)
                .andIn("status", Arrays.asList(0, 1))
                .andEqualTo("productCode", entity.getProductCode());

        if (null != entity.getId()) {
            criteria.andNotEqualTo("id", entity.getId());
        }
        criteria.andEqualTo("injectionRuleName", entity.getInjectionRuleName());
        int count = qcAgentRuleConfigMapper.selectCountByExample(example);
        if (count > 0) {
            throw new ServiceException("规则名称已存在");
        }
    }

    /**
     * 重复注入项校验
     *
     * @param entity
     */
    private void duplicateInjectionVerify(QcAgentRuleConfigEntity entity) {
        if (entity.getChaosExceptionType().equals(ChaosExceptionTypeEnum.ResultMock.name())) {
            return;
        }
        Example example = new Example(QcAgentRuleConfigEntity.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("enable", 1)
                .andIn("status", Arrays.asList(0, 1))
                .andEqualTo("productCode", entity.getProductCode());

        if (null != entity.getId()) {
            criteria.andNotEqualTo("id", entity.getId());
        }
        criteria.andEqualTo("appid", entity.getAppid());
        criteria.andEqualTo("versionCode", entity.getVersionCode());
        criteria.andEqualTo("injection", entity.getInjection());
        int count = qcAgentRuleConfigMapper.selectCountByExample(example);
        if (count > 0) {
            throw new ServiceException("规则已存在请修改（规则重复判断逻辑：注入应用、注入项、生效范围）");
        }
    }

}
