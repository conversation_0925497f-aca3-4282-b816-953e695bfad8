package com.zto.devops.qc.infrastructure.gateway.gitlab;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.gateway.gitlab.GitlabService;
import com.zto.devops.qc.domain.model.coverage.CommitInfo;
import com.zto.devops.qc.domain.util.DateUtil;
import com.zto.devops.qc.infrastructure.gateway.util.GitResourceManager;
import com.zto.titans.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.CloneCommand;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.blame.BlameResult;
import org.eclipse.jgit.internal.storage.file.FileRepository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.gitlab4j.api.CommitsApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.RepositoryApi;
import org.gitlab4j.api.models.Branch;
import org.gitlab4j.api.models.CommitRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class GitlabServiceImpl implements GitlabService {

    @Autowired
    private RepositoryApi repositoryApi;
    @Autowired
    private CommitsApi commitsApi;
    @Autowired
    private QcConfigBasicService config;

    /**
     * 1、先获取 源分支 最新commit
     * 2、获取 commit 的 ref 是否包含目标分支
     *
     * @param projectId
     * @param sourceBranch
     * @param targetBranch
     * @return
     */
    @Override
    public boolean targetContainSource(Integer projectId, String sourceBranch, String targetBranch) {
        if (projectId == null || StringUtil.isEmpty(sourceBranch)) {
            log.error("【Git】containBranch, {} - {}", projectId, sourceBranch);
            return false;
        }
        if (StringUtil.isEmpty(targetBranch)) {
            return true;
        }
        try {
            Branch branch = repositoryApi.getBranch(projectId, sourceBranch);
            List<CommitRef> commitRefs = commitsApi.getCommitRefs(projectId, branch.getCommit().getId(), CommitRef.RefType.BRANCH);
            for (CommitRef ref : commitRefs) {
                if (targetBranch.equals(ref.getName())) {
                    return true;
                }
            }
        } catch (GitLabApiException e) {
            log.error("【Git】containBranch, {} - {}", projectId, sourceBranch, e);
            return false;
        }
        return false;
    }

    @Override
    public String getLatestCommitId(Integer projectId, String branchName) {
        if (projectId == null || StringUtil.isEmpty(branchName)) {
            log.error("【Git】getLatestCommitId, {} - {}", projectId, branchName);
            return null;
        }
        try {
            Branch branch = repositoryApi.getBranch(projectId, branchName);
            if (null != branch && null != branch.getCommit()) {
                return branch.getCommit().getId();
            }
        } catch (GitLabApiException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public String getBranchCommitId(Integer gitProjectId, String branchName) {
        if (null == gitProjectId || StringUtils.isEmpty(branchName)) {
            return "";
        }
        try {
            Branch branch = repositoryApi.getBranch(gitProjectId, branchName);
            if (Objects.nonNull(branch) && Objects.nonNull(branch.getCommit())) {
                return branch.getCommit().getId();
            } else {
                return "";
            }
        } catch (GitLabApiException e) {
            log.error("获取分支异常. branchName：{}", branchName, e);
            return "";
        }
    }

    /**
     * 克隆源代码
     *
     * @param entity
     * @param sourcePath
     * @throws GitAPIException
     */
    @Override
    public void clone(CoverageRecordGenerateVO entity, String sourcePath) {
        File jarFile = new File(sourcePath);
        if (!jarFile.exists()) {
            log.info("开始克隆git远程库");
            UsernamePasswordCredentialsProvider usernamePasswordCredentialsProvider = new UsernamePasswordCredentialsProvider(config.getGitLabConfig().getUname(), config.getGitLabConfig().getPass());
            CloneCommand cloneCommand = Git.cloneRepository();
            Git git = null;
            try {
                git = cloneCommand.setURI(entity.getGitUrl()).setBranch(entity.getBranchName()).setDirectory(new File(sourcePath)).setCredentialsProvider(usernamePasswordCredentialsProvider).call();
            } catch (GitAPIException e) {
                log.error("克隆git远程库异常。", e);
                throw new RuntimeException(e);
            } finally {
                // 使用GitResourceManager确保Git对象被正确关闭，释放JGit WindowCache资源
                GitResourceManager.closeGitSafely(git);
            }
        }
        pull(entity, sourcePath);
    }

    /**
     * 拉取远程仓库内容到本地
     *
     * @param entity
     * @param sourcePath
     * @return
     */
    private void pull(CoverageRecordGenerateVO entity, String sourcePath) {
        log.info("开始拉取远程仓库内容到本地");
        UsernamePasswordCredentialsProvider usernamePasswordCredentialsProvider = new UsernamePasswordCredentialsProvider(config.getGitLabConfig().getUname(), config.getGitLabConfig().getPass());
        Git git = null;
        try {
            git = new Git(new FileRepository(sourcePath + "/.git"));
            git.pull().setRemoteBranchName(entity.getBranchName()).setCredentialsProvider(usernamePasswordCredentialsProvider).call();
        } catch (IOException e) {
            log.error("Git路径错误", e);
            throw new ServiceException("Git路径错误,请联系值班人员");
        } catch (GitAPIException e) {
            log.error("Git拉取源码异常", e);
            throw new ServiceException("Git拉取源码异常,请稍后再试");
        } finally {
            // 使用GitResourceManager确保Git对象被正确关闭，释放JGit WindowCache资源
            GitResourceManager.closeGitSafely(git);
        }
    }

    /**
     * 获取最后提交信息
     *
     * @param filePath
     * @param sourcePath
     * @return
     */
    @Override
    public List<CommitInfo> getLastCommitInfo(String filePath, String sourcePath) {
        List<CommitInfo> commitInfos = new ArrayList<>();
        if (StringUtil.isEmpty(filePath) || StringUtil.isEmpty(sourcePath)) {
            return commitInfos;
        }
        Git git = null;
        try {
            filePath = "/src/main/java/" + filePath.replace(".", "/") + ".java";
            git = new Git(new FileRepository(sourcePath + "/.git"));
            File[] repoDir = git.getRepository().getWorkTree().listFiles();
            for (File file : repoDir) {
                if (!file.isDirectory() || !new File(file.getAbsolutePath() + "/" + filePath).exists()) {
                    continue;
                }
                filePath = file.getAbsolutePath().substring(file.getAbsolutePath().lastIndexOf("/") + 1) + filePath;
                break;
            }
            BlameResult blameResult = git.blame().setFilePath(filePath).call();
            if (null == blameResult) {
                return commitInfos;
            }
            for (int i = 0; i < blameResult.getResultContents().size(); i++) {
                RevCommit commit = blameResult.getSourceCommit(i);
                if (null == commit || null == commit.getCommitterIdent()) {
                    log.warn("RevCommit is empty. filePath : {}", filePath);
                    continue;
                }
                CommitInfo commitInfo = new CommitInfo();
                commitInfo.setLine(i + 1);
                commitInfo.setAuthorName(commit.getCommitterIdent().getName());
                commitInfo.setAuthoredDate(DateUtil.formatDate(commit.getCommitterIdent().getWhen(), DateUtil.DATETIME_FORMAT));
                commitInfos.add(commitInfo);
            }
        } catch (Exception e) {
            log.error("GetLastCommitInfo error", e);
        } finally {
            // 使用GitResourceManager确保Git对象被正确关闭，释放JGit WindowCache资源
            GitResourceManager.closeGitSafely(git);
        }
        return commitInfos;
    }

}
