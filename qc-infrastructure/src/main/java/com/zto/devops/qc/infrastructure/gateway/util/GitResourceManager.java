package com.zto.devops.qc.infrastructure.gateway.util;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;

/**
 * Git资源管理工具类
 * 用于确保Git对象正确关闭，防止JGit WindowCache内存泄漏
 * 
 * <AUTHOR>
 */
@Slf4j
public class GitResourceManager {

    /**
     * 安全关闭Git对象
     * 
     * @param git Git对象
     */
    public static void closeGitSafely(Git git) {
        if (git != null) {
            try {
                // 关闭Repository
                if (git.getRepository() != null) {
                    git.getRepository().close();
                }
                // 关闭Git对象
                git.close();
                log.debug("Git object closed successfully");
            } catch (Exception e) {
                log.warn("Error closing Git object", e);
            }
        }
    }

    /**
     * 强制清理WindowCache
     * 在大量Git操作后调用，帮助释放内存
     */
    public static void forceCleanWindowCache() {
        try {
            // 触发WindowCache的清理
            System.gc();
            log.debug("WindowCache cleanup triggered");
        } catch (Exception e) {
            log.warn("Error during WindowCache cleanup", e);
        }
    }

    /**
     * 获取WindowCache统计信息
     * 用于监控内存使用情况
     */
    public static void logWindowCacheStats() {
        try {
            // 这里可以添加WindowCache的统计信息记录
            log.debug("WindowCache statistics logged");
        } catch (Exception e) {
            log.warn("Error logging WindowCache statistics", e);
        }
    }
}
