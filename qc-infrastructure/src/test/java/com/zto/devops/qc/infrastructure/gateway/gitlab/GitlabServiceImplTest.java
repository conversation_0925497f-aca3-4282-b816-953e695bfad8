package com.zto.devops.qc.infrastructure.gateway.gitlab;

import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordGenerateVO;
import com.zto.devops.qc.domain.gateway.apollo.QcConfigBasicService;
import com.zto.devops.qc.domain.model.coverage.CommitInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.gitlab4j.api.CommitsApi;
import org.gitlab4j.api.RepositoryApi;

import java.io.File;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GitlabServiceImpl 测试类
 * 主要测试Git资源管理和内存泄漏修复
 */
@ExtendWith(MockitoExtension.class)
class GitlabServiceImplTest {

    @Mock
    private RepositoryApi repositoryApi;

    @Mock
    private CommitsApi commitsApi;

    @Mock
    private QcConfigBasicService config;

    @InjectMocks
    private GitlabServiceImpl gitlabService;

    @Test
    void testGetLastCommitInfo_WithInvalidPath_ShouldReturnEmptyList() {
        // Given
        String filePath = "";
        String sourcePath = "";

        // When
        List<CommitInfo> result = gitlabService.getLastCommitInfo(filePath, sourcePath);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetLastCommitInfo_WithNullPath_ShouldReturnEmptyList() {
        // Given
        String filePath = null;
        String sourcePath = null;

        // When
        List<CommitInfo> result = gitlabService.getLastCommitInfo(filePath, sourcePath);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetLastCommitInfo_WithNonExistentGitRepo_ShouldReturnEmptyList() {
        // Given
        String filePath = "com.example.TestClass";
        String sourcePath = "/non/existent/path";

        // When
        List<CommitInfo> result = gitlabService.getLastCommitInfo(filePath, sourcePath);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试Git资源是否正确关闭
     * 这个测试主要验证在异常情况下Git对象也能被正确关闭
     */
    @Test
    void testGitResourceManagement_ShouldCloseResourcesOnException() {
        // Given
        String filePath = "com.example.TestClass";
        String sourcePath = "/invalid/git/path";

        // When & Then
        // 即使发生异常，也不应该抛出未处理的异常
        assertDoesNotThrow(() -> {
            List<CommitInfo> result = gitlabService.getLastCommitInfo(filePath, sourcePath);
            assertNotNull(result);
        });
    }
}
