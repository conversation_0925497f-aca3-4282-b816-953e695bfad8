# JGit WindowCache 内存泄漏修复方案

## 问题描述

根据内存泄漏报告分析，发现以下关键问题：

1. **JGit WindowCache** 占用了 **2.3GB (93.41%)** 的内存
2. **MySQL Statement Cancellation Timer** 线程持有对 `LaunchedURLClassLoader` 的引用
3. 这个引用链阻止了 JGit WindowCache 的垃圾回收，导致严重的内存泄漏

## 根本原因

在 `GitlabServiceImpl` 中，Git 对象没有被正确关闭：

1. `clone()` 方法中，Git 对象创建后未关闭
2. `getLastCommitInfo()` 方法中，Git 对象创建后未关闭  
3. `pull()` 方法中，Git 对象的关闭逻辑有问题

## 修复方案

### 1. 修复 Git 资源泄漏

**修复前的问题代码：**
```java
// clone方法 - Git对象未关闭
Git git = cloneCommand.setURI(...).call();
// 没有关闭git对象！

// getLastCommitInfo方法 - Git对象未关闭  
Git git = new Git(new FileRepository(sourcePath + "/.git"));
// 整个方法结束时没有关闭git对象！
```

**修复后的代码：**
```java
Git git = null;
try {
    git = cloneCommand.setURI(...).call();
    // 业务逻辑
} catch (Exception e) {
    // 异常处理
} finally {
    // 确保Git对象被正确关闭
    GitResourceManager.closeGitSafely(git);
}
```

### 2. 添加 JGit WindowCache 配置优化

创建了 `JGitConfig` 配置类：
- 设置 WindowCache 最大内存限制为 256MB
- 配置合理的窗口大小和文件句柄数量
- 防止 WindowCache 无限制增长

### 3. 创建 Git 资源管理工具类

创建了 `GitResourceManager` 工具类：
- 提供安全的 Git 对象关闭方法
- 统一管理 Git 资源的生命周期
- 提供 WindowCache 清理和监控功能

## 修复的文件

1. **qc-infrastructure/src/main/java/com/zto/devops/qc/infrastructure/gateway/gitlab/GitlabServiceImpl.java**
   - 修复了 `clone()` 方法的资源泄漏
   - 修复了 `pull()` 方法的资源泄漏  
   - 修复了 `getLastCommitInfo()` 方法的资源泄漏

2. **qc-infrastructure/src/main/java/com/zto/devops/qc/infrastructure/config/JGitConfig.java** (新增)
   - JGit WindowCache 配置优化

3. **qc-infrastructure/src/main/java/com/zto/devops/qc/infrastructure/gateway/util/GitResourceManager.java** (新增)
   - Git 资源管理工具类

4. **qc-infrastructure/src/test/java/com/zto/devops/qc/infrastructure/gateway/gitlab/GitlabServiceImplTest.java** (新增)
   - 单元测试验证修复效果

## 预期效果

1. **内存使用大幅降低**：JGit WindowCache 不再无限制增长
2. **防止内存泄漏**：Git 对象正确关闭，释放相关资源
3. **提高系统稳定性**：减少 Full GC 频率，提升性能
4. **可监控性**：提供 WindowCache 统计和监控功能

## 验证方法

1. **运行单元测试**：验证 Git 资源管理逻辑
2. **内存监控**：观察 JGit WindowCache 内存使用情况
3. **压力测试**：在大量 Git 操作场景下验证内存稳定性
4. **生产环境监控**：部署后持续监控内存使用趋势

## 注意事项

1. 这个修复主要针对 JGit WindowCache 内存泄漏问题
2. MySQL Statement Cancellation Timer 的引用链问题也会随着 Git 对象正确关闭而解决
3. 建议在生产环境部署后持续监控内存使用情况
4. 如果仍有内存问题，可能需要进一步调整 WindowCache 配置参数
