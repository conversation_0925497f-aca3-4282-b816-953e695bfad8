package com.zto.devops;

import com.zto.devops.qc.domain.service.AutomaticSourceRecordCommandDomainService;
import com.zto.devops.qc.domain.service.AutomaticTaskCommandDomainService;
import com.zto.devops.qc.domain.service.CoverageDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class JobTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "gaoyanqi2035");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5664313,\"cnName\":\"高延奇\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\",\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
    }

    @Resource
    private AutomaticSourceRecordCommandDomainService automaticSourceRecordCommandDomainService;

    @Resource
    private AutomaticTaskCommandDomainService automaticTaskCommandDomainService;

    @Resource
    private CoverageDomainService coverageDomainService;

    @Test
    public void analysisAutomaticAbort() {
        log.info("开始执行qc域解析库超时终止");
        automaticSourceRecordCommandDomainService.analysisAutomaticAbort();
        log.info("执行结束");
    }

    @Test
    public void autoTestAbort() {
        automaticTaskCommandDomainService.autoTestAbort();
    }

    @Test
    public void coverageTimeoutAbort() {
        log.info("覆盖率生成超时终止任务");
        coverageDomainService.coverageTimeoutAbort();
    }

    @Test
    public void cleanCoverageFile() {
        coverageDomainService.cleanCoverageFile();
    }

}
