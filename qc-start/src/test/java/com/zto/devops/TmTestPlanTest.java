package com.zto.devops;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanTipVO;
import com.zto.devops.qc.client.service.plan.ITestPlanService;
import com.zto.devops.qc.client.service.plan.model.TestPlanDetailReq;
import com.zto.devops.qc.domain.gateway.oss.ZtoOssService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.util.ApiDebugLogService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class TmTestPlanTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "gaoyanqi2035");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5664313,\"cnName\":\"高延奇\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\",\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
    }

    @Resource
    private ITestPlanService testPlanService;

    @Autowired
    private ZtoOssService ossService;

    @Autowired
    private ApiDebugLogService apiDebugLogService;

    @Autowired
    private RedisService redisService;

    /**
     * 获取测试计划的提示信息
     */
    @Test
    public void testPlanTip() {
        TestPlanDetailReq req = new TestPlanDetailReq();
        req.setCode("TP230309008004");
        Result<TestPlanTipVO> result = testPlanService.testPlanTip(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Test
    public void test() {
        try{
//            AmazonS3Utils.uploadObject("F:\\apitest\\PRO202109180009\\SNF870378327720853504", "autojmx");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("结果上传oss失败");
        }
    }

    @Test
    public void downloadFile() {
        try{
//            ossService.createObject("autojmx", "apitest/jmxFile/",
//                    "normal.jmx","C:/Users/<USER>/Downloads/normal.jmx");
            ossService.copyObject("autojmx", "apitest", "", "_test");
//            ossService.uploadObject("/data/apitest_test", "autojmx");
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static List<Map<String, Map<String, String>>> descartes(List<Map<String, Map<String, String>>>... lists) {
        List<Map<String, Map<String, String>>> tempList = new ArrayList<>();
        for (List<Map<String, Map<String, String>>> list : lists) {
            if (tempList.isEmpty()) {
                tempList = list;
            } else {
                tempList = tempList.stream().flatMap(termMap -> {
                    List<Map<String, Map<String, String>>> deCartList = new ArrayList<>();
                    for(Map<String, Map<String, String>> deCartMap : list) {
                        Map<String, Map<String, String>> map = new HashMap<>();
                        map.putAll(deCartMap);
                        map.putAll(termMap);
                        deCartList.add(map);
                    }
                    return deCartList.stream();
                }).collect(Collectors.toList());
            }
        }
        return tempList;
    }

    @Test
    public void doThing() {
        Map<String, String> dataMap1 = new HashMap<>();
        dataMap1.put("data1", "来是come");
        dataMap1.put("data2", "去是Go");

        Map<String, String> dataMap2 = new HashMap<>();
        dataMap2.put("data1", "点头Yes");
        dataMap2.put("data2", "摇头No");

        Map<String, String> dataMap3 = new HashMap<>();
        dataMap3.put("data1", "你好");
        dataMap3.put("data2", "Bob");

        Map<String, String> dataMap4 = new HashMap<>();
        dataMap4.put("data1", "嗨");
        dataMap4.put("data2", "Sari");

        Map<String, Map<String, String>> nodeMap1 = new HashMap<>();
        nodeMap1.put("2023071001", dataMap1);

        Map<String, Map<String, String>> nodeMap2 = new HashMap<>();
        nodeMap2.put("2023071001", dataMap2);

        Map<String, Map<String, String>> nodeMap3 = new HashMap<>();
        nodeMap3.put("2023071002", dataMap3);

        Map<String, Map<String, String>> nodeMap4 = new HashMap<>();
        nodeMap4.put("2023071002", dataMap4);

        List<Map<String, Map<String, String>>> nodeMapList1 = new ArrayList<>();
        nodeMapList1.add(nodeMap1);
        nodeMapList1.add(nodeMap2);

        List<Map<String, Map<String, String>>> nodeMapList2 = new ArrayList<>();
        nodeMapList2.add(nodeMap3);
        nodeMapList2.add(nodeMap4);

        List<Map<String, Map<String, String>>> descartesList = descartes(nodeMapList1, nodeMapList2);
        System.out.println(JSONUtils.toJSONString(descartesList));
    }

//    @Test
//    public void doCookie() throws Exception {
//        String cookie = getCookie("Secret nWCT+BwXNdV1NWztBJepHJzU+XqHzPZeuZ2wnB0ApzOoGZSyIUFOuzAHYlkJ2BjgwuTU/3hjVJmITUn7wjITQ+du6wo9jy64ik3boBl8aJ/IH995YqdH5pyZGbwRwMck3fHjTlJDMlv6YHOFmFiYVJP/rQGY0Jwc8aKxCZ3H3+/xzn2P6YGtppxWuZ1rBzuv93v7kU5FfOFAomKdZxrouxwXj3CUyiwf+h9WHg+bO0jw8xAs7jIK/AmI1bEQoKe54WcOlDzILg8iMqbNIoYjuXQcx0S+QL6eabJHOfITZiHbo+mw8N5vOoGh+Bgmu7/yHqUR0iblJcICx28GEUVS7g==");
//        System.out.println(cookie);
//    }

//    public String getCookie(String secret) throws Exception {
//        return Auth.getCookie(secret);
//    }

//    @Test
//    public void soCookie() throws Exception {
//        String cookie = AuthUtils.getCookieQc("Secret nWCT+BwXNdV1NWztBJepHJzU+XqHzPZeuZ2wnB0ApzOoGZSyIUFOuzAHYlkJ2BjgwuTU/3hjVJmITUn7wjITQ+du6wo9jy64ik3boBl8aJ/IH995YqdH5pyZGbwRwMck3fHjTlJDMlv6YHOFmFiYVJP/rQGY0Jwc8aKxCZ3H3+/xzn2P6YGtppxWuZ1rBzuv93v7kU5FfOFAomKdZxrouxwXj3CUyiwf+h9WHg+bO0jw8xAs7jIK/AmI1bEQoKe54WcOlDzILg8iMqbNIoYjuXQcx0S+QL6eabJHOfITZiHbo+mw8N5vOoGh+Bgmu7/yHqUR0iblJcICx28GEUVS7g==");
//        System.out.println(cookie);
//    }

//    @Test
//    public void doTest() {
//        try{
//            redisService.setKey("gaoyanqiTest-Thread", JSON.toJSONString(Thread.currentThread()));
////            redisService.hashSet("gaoyanqiTest-Thread", "hashKey", Thread.currentThread());
//            apiDebugLogService.abortApiDebugLog("gaoyanqiTest");
//        }catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    @Test
    public void regularTest() {
        String regular = "(?<=[1-9]\\.\\s|[1-9][0-9]\\.\\s)([\\d\\D]*?)(?=\\$)";
        String test = "1. 未覆盖部分跟本次改动无关$2. 异常场景无法覆盖$3. 特殊场景无法模拟$4. 自定义：11111111\n" +
                "1111111$";
        Pattern pattern = Pattern.compile(regular);

        Matcher m = pattern.matcher(test);
        while (m.find()) {
            int i = 1;
            System.out.println(m.group(i));
        }
    }
}
