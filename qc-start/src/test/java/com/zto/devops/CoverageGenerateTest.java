package com.zto.devops;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO;
import com.zto.devops.qc.domain.service.CoverageDomainService;
import com.zto.lbd.pipeline.flow.event.deployment.DeploymentDeployEndedEvent;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class CoverageGenerateTest {
    @Autowired
    private CoverageDomainService coverageDomainService;

    @Test
    public void getCoverageReportListPage() {
        DeploymentDeployEndedEvent event = JSONObject.toJavaObject(JSON.parseObject(str), DeploymentDeployEndedEvent.class);
        log.info("动态多环境部署结束事件：{}", JsonUtil.toJSON(event));
        List<JacocoApplicationVO> apps = coverageDomainService.checkApps(event.getCode(), event.getPlanCode(), "DeployEndedEvent");
        System.out.println("==================="+JSON.toJSONString(apps));
    }

    private static final String str = "{\"aggregateId\":\"DHOT241105009001\",\"branchType\":\"RELEASE\",\"code\":\"DHOT241105009001\",\"eventType\":\"\",\"lastDeployResult\":\"SUCCESS\",\"mergeCodeFail\":false,\"occurred\":*************,\"planCode\":\"EXE241120018002\",\"planStatus\":\"SUCCESS\",\"preStatus\":null,\"retryOrFire\":false,\"status\":\"REGRESSING\",\"tag\":\"NORMAL\",\"transactor\":{\"empNo\":\"\",\"openId\":\"\",\"permissions\":[],\"supperUser\":false,\"userId\":5952057,\"userName\":\"赵映心\",\"ztoAccount\":\"z00522590\"},\"type\":\"HOT_DEPLOYMENT\"}";
}
