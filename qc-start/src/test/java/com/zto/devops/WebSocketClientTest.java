package com.zto.devops;

/**
 * <AUTHOR>
 * @create 2023/8/23 15:45
 */
import javax.websocket.*;
import java.net.URI;

@ClientEndpoint
public class WebSocketClientTest {

//    @OnOpen
//    public void onOpen(Session session) {
//        System.out.println("WebSocket连接已建立");
//    }
//
//    @OnMessage
//    public void onMessage(String message) {
//        System.out.println("收到消息：" + message);
//    }
//
//    @OnClose
//    public void onClose() {
//        System.out.println("WebSocket连接已关闭");
//    }
//
//    @OnError
//    public void onError(Throwable error) {
//        System.out.println("WebSocket发生错误：" + error.getMessage());
//    }

    public static void main(String[] args) {
        WebSocketContainer container = null;
        Session session = null;
        try {
            // 创建WebSocketContainer对象
            container = ContainerProvider.getWebSocketContainer();
            // 连接WebSocket服务器
            session = container.connectToServer(WebSocketClientTest.class, URI.create("ws://10.165.213.174:8080/websocket/message"));

//            session = container.connectToServer(WebSocketClient.class, URI.create("ws://127.0.0.1:8080/websocket/scene/cda639fd09bf9fde23d8addcb1bebee1"));
            // 发送消息
            session.getBasicRemote().sendText("{\n" +
                    "  \"key\":\"SNF913105765907300352\",\n" +
                    "  \"userId\":\"123456\",\n" +
                    "  \"userName\":\"许建峰\",\n" +
                    "  \"channel\":\"\",\n" +
                    "  \"token\":\"d74b1dbc273d06b586f9ac5bac3c1354\",\n" +
                    "  \"type\":\"SCENE\"\n" +
                    "}");
            // 保持程序运行一段时间
            Thread.sleep(5000);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭WebSocket连接
            if (session != null) {
                try {
//                    session.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
