package com.zto.devops;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.devops.pipeline.client.model.flow.query.ListDeployApplicationQuery;
import com.zto.devops.pipeline.client.service.inner.IPipelineQcService;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.GenerateTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.rpc.pipeline.BaseApplicationDO;
import com.zto.devops.qc.client.model.rpc.pipeline.JacocoApplicationVO;
import com.zto.devops.qc.client.model.rpc.pipeline.JacocoInstanceVO;
import com.zto.devops.qc.client.model.rpc.pipeline.event.CreateBranchQcEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoveragePublishQuery;
import com.zto.devops.qc.client.service.coverage.CoverageService;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageRecordPageReq;
import com.zto.devops.qc.client.service.coverage.model.req.GenerateCoverageReq;
import com.zto.devops.qc.domain.gateway.gitlab.GitlabService;
import com.zto.devops.qc.domain.gateway.jacoco.JacocoService;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.service.CoverageDomainService;
import com.zto.titans.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @create 2022/9/18 10:52
 */
@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class CoverageTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("logging.level.com.zto", "debug");
        System.setProperty("titans.dubbo.tag", "env42180dev");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5431563,\"cnName\":\"许建峰\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\"," +
                "\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
        System.setProperty("javaagent", "D:\\fat-agent\\********\\agent.jar");
        System.setProperty("zss.worker.config.label", "xjf");
    }

    @Autowired
    private CoverageService coverageService;
    @Autowired
    private CoverageDomainService coverageDomainService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private GitlabService gitlabService;

    @Reference
    private IPipelineQcService pipelineQcService;

    @Autowired
    private IPipelineRpcService pipelineRpcService;
    @Autowired
    private JacocoService jacocoService;


    private static final Logger logger = Logger.getLogger(CoverageDomainService.class.getName());

    @Test
    public void getCoverageReportListPage() {
        String reqStr = "{\"gmtCreateStart\":\"\",\"gmtCreateEnd\":\"\"," +
                "\"versionCode\":\"VER2302168133\",\"appId\":\"\",\"size\":10,\"page\":1}";
        CoverageRecordPageReq req = JsonUtil.parse(reqStr, CoverageRecordPageReq.class);
        Result result =
                coverageService.getCoverageReportListPage(req);
        System.out.println(result);
    }

    @Test
    public void gatewayGenerateCoverageReport() {
        String reqStr = "{\"productCode\":\"PRO2207128000\",\"versionCode\":\"VER2302168133\"," +
                "\"appId\":\"zto-qamp\",\"recordType\":\"BRANCH\",\"versionName\":\"小峰哥产品测试V2.45" +
                ".0\"}";
        GenerateCoverageReq req = JsonUtil.parse(reqStr, GenerateCoverageReq.class);
//        Result result = coverageService.generateCoverageReport(req);
//        System.out.println(result);
    }

    @Test
    public void generateExec() {
        CoveragePublishQuery query = new CoveragePublishQuery();

        List<String> appIds = new ArrayList<>();
        appIds.add("lbd-qc");
        List<String> versionCodes = new ArrayList<>();
        versionCodes.add("VER2302218059");
        query.setVersionCodes(versionCodes);
        query.setBranchName("feature-v2.20.0");
        query.setAppIds(appIds);
        query.setCreator("许建峰");
        query.setCreatorId(123456L);

        coverageDomainService.generateCoverageExec(query, DiffTypeEnum.INCREMENT);
    }

//    @Test
//    public void saveCoveragePublish() {
//        List<JacocoApplicationVO> apps = new ArrayList<>();
//        JacocoApplicationVO vo = new JacocoApplicationVO();
//        vo.setCode("code");
//        vo.setAppId("appId");
//        vo.setBranchName("branchName");
//        vo.setCommitId("commitId");
//        vo.setGitUrl("gitUrl");
//        vo.setPort(6300);
//        vo.setDeployCode("deployCode");
//        vo.setPackageName("packageName");
//        List<JacocoInstanceVO> instances = new ArrayList<>();
//        JacocoInstanceVO jacocoInstanceVO = new JacocoInstanceVO();
//        jacocoInstanceVO.setIp("***********");
//        jacocoInstanceVO.setType(1);
//        instances.add(jacocoInstanceVO);
//        JacocoInstanceVO jacocoInstanceVO1 = new JacocoInstanceVO();
//        jacocoInstanceVO1.setIp("***********");
//        jacocoInstanceVO1.setType(1);
//        instances.add(jacocoInstanceVO1);
//        vo.setInstances(instances);
//        List<Version> versions = new ArrayList<>();
//        versions.add(new Version("versionCode1", "versionName1"));
//        versions.add(new Version("versionCode2", "versionName2"));
//        vo.setVersions(versions);
//        apps.add(vo);
//
//        User user = new User();
//        user.setUserId(123456L);
//        user.setUserName("xjf");
//
//        coverageDomainService.saveCoveragePublish(apps, user);
//    }
//
//    @Test
//    public void initCoverageRecords() {
//        String versionCode = "VER2303068034";
//        String branchName = "hotfix-V2.32.3";
//        List<BaseApplicationDO> appIds = new ArrayList<>();
//        BaseApplicationDO baseApplicationDO = new BaseApplicationDO();
//        baseApplicationDO.setAppId("zto-qamp");
//        baseApplicationDO.setProductCode("PRO2207128000");
//        baseApplicationDO.setProductName("小峰哥产品测试");
//        appIds.add(baseApplicationDO);
////        BaseApplicationDO baseApplicationDO1 = new BaseApplicationDO();
////        baseApplicationDO1.setAppId("zto-test");
////        appIds.add(baseApplicationDO1);
////        BaseApplicationDO baseApplicationDO1 = new BaseApplicationDO();
////        baseApplicationDO1.setAppId("zto-config");
////        appIds.add(baseApplicationDO1);
//        coverageManage.initCoverageRecords(appIds, versionCode, branchName);
//    }

    @Test
    public void generateCoverageReport() {
        String versionCode = "VER2306058015";
        List<String> appIds = new ArrayList<>();
        appIds.add("devops-modules-test");
        List<CoverageRecordGenerateParameter> parameters = new ArrayList<>();
//        CoverageRecordGenerateParameter parameter = new CoverageRecordGenerateParameter();
////        appIds.add("zto-test");
//        parameter.setProductCode("PRO2207128000");
//        parameter.setVersionCode(versionCode);
//        parameter.setAppIdList(appIds);
//        parameter.setGenerateType(GenerateTypeEnum.AUTO);
//        parameter.setRecordType(RecordTypeEnum.MASTER);
//        parameter.setCreator("许建峰");
//        parameter.setCreatorId(123456789L);
//        parameters.add(parameter);
//        appIds.add("zto-test");

        CoverageRecordGenerateParameter parameter1 = new CoverageRecordGenerateParameter();
        parameter1.setProductCode("PRO2207128000");
        parameter1.setVersionCode(versionCode);
        parameter1.setVersionName("feature-V1.2.0");
        parameter1.setAppIdList(appIds);
        parameter1.setGenerateType(GenerateTypeEnum.AUTO);
        parameter1.setRecordType(RecordTypeEnum.BRANCH);
//        parameter1.setDiffType(DiffTypeEnum.INCREMENT);
        parameter1.setCreator("许建峰");
        parameter1.setCreatorId(123456789L);
        parameter1.setTaskId(versionCode + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss"));
        parameters.add(parameter1);

        for (CoverageRecordGenerateParameter parameter2 : parameters) {
            coverageDomainService.generateCoverageReport(parameter2);
        }
    }


    @Test
    public void generateCoverageReportWitNotClass() throws InterruptedException {
        String versionCode = "VER2307228013";
        List<String> appIds = new ArrayList<>();
        appIds.add("glutton-web");
        List<CoverageRecordGenerateParameter> parameters = new ArrayList<>();

        CoverageRecordGenerateParameter parameter1 = new CoverageRecordGenerateParameter();
        parameter1.setProductCode("PRO_93");
        parameter1.setVersionCode(versionCode);
        parameter1.setVersionName("feature-V1.2.0");
        parameter1.setAppIdList(appIds);
        parameter1.setGenerateType(GenerateTypeEnum.AUTO);
        parameter1.setRecordType(RecordTypeEnum.BRANCH);
//        parameter1.setDiffType(DiffTypeEnum.INCREMENT);
        parameter1.setCreator("许建峰");
        parameter1.setCreatorId(123456789L);
        parameter1.setTaskId(versionCode + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss"));
        parameters.add(parameter1);

        for (CoverageRecordGenerateParameter parameter2 : parameters) {
            coverageDomainService.generateCoverageReport(parameter2);
        }

        TimeUnit.MINUTES.sleep(10);
    }

    //    @Test
//    public void findVersionInfoQuery() {
//        final FindVersionInfoQuery query = new FindVersionInfoQuery("VER2303068034");
//        VersionVO versionVO = queryGateway.query(query,
//                ResponseTypes.instanceOf(VersionVO.class)).join();
//        System.out.println(versionVO);
//    }
//
//    @Test
//    public void findOssFileUrlQuery() {
//        final FindOssFileUrlQuery query = new FindOssFileUrlQuery();
//        query.setFileName("");
//        final OssFileVO ossFileVO = queryGateway.query(query,
//                ResponseTypes.instanceOf(OssFileVO.class)).join();
//        System.out.println(ossFileVO);
//    }
//
//    @Test
//    public void findApplicationQuery() {
//        String appId = "test";
//        final PageApplicationQuery query = new PageApplicationQuery();
//        query.setAppId(appId);
//        final PageApplicationVO detailVO = queryGateway.query(query,
//                ResponseTypes.instanceOf(PageApplicationVO.class)).join();
//        List<ApplicationVO> applicationVOS = detailVO.getList();
//        applicationVOS.get(0).getWhiteList();
//        applicationVOS.get(0).getCoverageStandardValue();
//        applicationVOS.get(0).getWhiteListReason();
//        System.out.println(detailVO);
//    }
//
//    @Test
//    public void getObjectText() {
//        try {
//            long start = System.currentTimeMillis();
//            log.info("start");
//            String text = amazonS3Utils.getObjectText("coverage-log-bucket",
//                    "/VER2209148019/feature-V1.0.0/zto-test/log/20221024041849128.log");
//            log.info("end.time : {}", System.currentTimeMillis() - start);
////            log.info(text);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//    }
//
    @Test
    public void createBranchQcEvent() {
        CreateBranchQcEvent event = new CreateBranchQcEvent();
        event.setBranchName("feature-2.92.0");
        event.setBasicBranchName("master");
        event.setReleaseBranchName("release-2.92.0");
        List<CreateBranchQcEvent.Version> versions = new ArrayList<>();
        CreateBranchQcEvent.Version version = new CreateBranchQcEvent.Version();
        version.setCode("VER2209288099");
        version.setName("小峰哥产品测试V2.92.0");
        List<BaseApplicationDO> apps = new ArrayList<>();
        BaseApplicationDO applicationDO = new BaseApplicationDO();
        applicationDO.setAppId("zto-qamp");
        applicationDO.setTypeCode("1");
        apps.add(applicationDO);
        BaseApplicationDO applicationDO1 = new BaseApplicationDO();
        applicationDO1.setAppId("zto-qamp-web");
        applicationDO1.setTypeCode("1");
        apps.add(applicationDO1);

        version.setApps(apps);
        versions.add(version);
        event.setVersions(versions);

        User user = new User();
        user.setUserId(1212312L);
        user.setUserName("许建峰");
        event.setTransactor(user);

        coverageDomainService.createBranch(event);
    }

//    @Test
//    public void listFlowVersionJacocoQuery() {
//        final ListFlowVersionJacocoQuery query = new ListFlowVersionJacocoQuery();
//        query.setFlowCode("INT221011008007");
//        final JacocoVersionVO jacocoVersion = queryGateway.query(query,
//                ResponseTypes.instanceOf(JacocoVersionVO.class)).join();
//        log.info(JsonUtil.toJSON(jacocoVersion));
//    }
//
//    @Test
//    public void findProductByIdQuery() {
//        FindProductByIdQuery productQuery = new FindProductByIdQuery();
//        productQuery.setCode("399");
//        ProductVO productVO = queryGateway
//                .query(productQuery, ResponseTypes.instanceOf(ProductVO.class))
//                .join();
//        log.info(">>>>>>productVO : {}", productVO);
//    }
//
//    @Test
//    public void coverageAbort() {
//        coverageExecuteService.coverageTimeoutAbort();
//    }
//
//    @Test
//    public void automaticGenerateCoverageReport() {
//        CoverageRecordGenerateParameter parameter = new CoverageRecordGenerateParameter();
//        parameter.setTaskId("TASK" + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss"));
//        parameter.setEnvName("wxc测试专用空间");
//        parameter.setDiffType(DiffTypeEnum.FULL);
//        parameter.setProductCode("PRO202109180009");
//        coverageDomainService.automaticGenerateCoverageReport(parameter);
//    }

    @Test
    public void listDeployApplicationQuery() {
        ListDeployApplicationQuery query = new ListDeployApplicationQuery();
        query.setFlowCode("INT230615008000");
        query.setPlanCode("EPN230616008000");
        Result<List<com.zto.devops.pipeline.client.model.application.entity.JacocoApplicationVO>> result =
                pipelineQcService.listDeployApplicationQuery(query);
        log.info(">>>>>>result : {}", result);
    }

    @Test
    public void deployApplicationQuery() {
        String flowCode = "DINT240820002002";
        String planCode = "EXE241112004004";
        List<JacocoApplicationVO> apps =
                pipelineRpcService.listDeployApplicationQuery(flowCode, planCode);
        log.info(">>>>>>result : {}", apps);
        User user = new User();
        user.setUserName("xjf");
        user.setUserId(1234L);
        coverageDomainService.saveCoveragePublish(apps, user,false);

//        JacocoApplicationVO vo = new JacocoApplicationVO();
//        vo.setCode("APP2210288002");
//        vo.setAppId("zto-qamp");
//        vo.setBranchName("release-V2.92.0");
//        vo.setCommitId("005a7278efd5db46100d6b317d8a7379e667145c");
//        vo.setPackageName("zto-qamp-9abc582350c3a012f4350370765903dd.tar.gz");
//        vo.setGitUrl("http://git.test.ztosys.com/quality/qamp-server");
//        vo.setPort(6300);
//        vo.setNamespaceName("test");
//        vo.setDeployCode("EXE231009008215");
//        vo.setOutputFileName("all_zto-qamp__1696840781.zip");
//        com.zto.devops.framework.client.simple.Version version = new Version();
//        version.setCode("VER2308098037");
//        version.setName("V2.92.0 test");
//        vo.setVersions(Arrays.asList(version));
//        JacocoInstanceVO jacocoInstanceVO = new JacocoInstanceVO();
//        jacocoInstanceVO.setIp("***********");
//        jacocoInstanceVO.setName("***********");
//        jacocoInstanceVO.setType(1);
//        vo.setInstances(Arrays.asList(jacocoInstanceVO));
//        vo.setFlowLaneType(FlowLaneTypeEnum.FLOW_TEST);
//        apps.add(vo);
//
//        Set<JacocoApplicationVO> setWithoutDuplicates = new HashSet<>();
//        for (JacocoApplicationVO obj : apps) {
//            if (!setWithoutDuplicates.contains(obj)) {
//                setWithoutDuplicates.add(obj);
//            }
//        }
//        List<JacocoApplicationVO> listWithoutDuplicates = new ArrayList<>(setWithoutDuplicates);
//        log.info("去重后的apps : {}", listWithoutDuplicates);
    }

//    @Test
//    public void simpleQuery() {
//        SimpleQuery query = new SimpleQuery();
//        query.setProductCode("PRO_303");
//        SimpleQueryVO productVO = queryGateway
//                .query(query, ResponseTypes.instanceOf(SimpleQueryVO.class))
//                .join();
//        log.info(">>>>>>productVO : {}", JsonUtil.toJSON(productVO));
//    }

    @Test
    public void clearRedis() {
//        List<String> keys = Arrays.asList("SNF902927880730705920");
        List<String> keys = Arrays.asList("wxc-java-demo.VER24072900127.com.zto.qamp.service.impl.DubboTestServiceImpl:16",
                "wxc-java-demo.VER24072900127.com.zto.qamp.service.impl.DubboTestServiceImpl:17",
                "wxc-java-demo.VER24072900127.com.zto.qamp.service.impl.DubboTestServiceImpl:18");

        for (String key : keys) {
            if (redisService.hasKey(key)) {
                redisService.delete(key);
            }
        }
    }

    @Test
    public void gitLabTest() {
        Integer projectId = 4674;
        String sourceBranch = "master";
        String targetBranch = "feature-V2.91.0";
        gitlabService.targetContainSource(projectId, sourceBranch, targetBranch);
    }

    @Test
    public void copySrc() {
        String src = "[\"/data/VER24070100428-release-V3.927.0-devops-pipeline/release-V3.927.0/bd4247dd9ccff0d0e6b3f03e618ca4b9269a651f/sources/pipeline-domain\",\"/data/VER24070100428-release-V3.927.0-devops-pipeline/release-V3.927.0/bd4247dd9ccff0d0e6b3f03e618ca4b9269a651f/sources/pipeline-client\",\"/data/VER24070100428-release-V3.927.0-devops-pipeline/release-V3.927.0/bd4247dd9ccff0d0e6b3f03e618ca4b9269a651f/sources/pipeline-adapter\",\"/data/VER24070100428-release-V3.927.0-devops-pipeline/release-V3.927.0/bd4247dd9ccff0d0e6b3f03e618ca4b9269a651f/sources/pipeline-infrastructure\",\"/data/VER24070100428-release-V3.927.0-devops-pipeline/release-V3.927.0/bd4247dd9ccff0d0e6b3f03e618ca4b9269a651f/sources/pipeline-start\",\"/data/VER24070100428-release-V3.927.0-devops-pipeline/release-V3.927.0/bd4247dd9ccff0d0e6b3f03e618ca4b9269a651f/sources/pipeline-application\"]";
        List<String> allSrcList = JSONObject.parseArray(src, String.class);

        String locatedSrc = "{\"com/zto/devops/pipeline/domain/service/flow\":[\"DeploymentCommandDomainService.java\",\"AbstractFlowDomainService.java\"],\"com/zto/devops/pipeline/infrastructure/config\":[\"EngineInstanceConfig.java\"],\"com/zto/devops/pipeline/application/service\":[\"FlowServiceImpl.java\",\"ApplicationServiceImpl.java\",\"WebHookServiceImpl.java\",\"CorrectorServiceImpl.java\",\"PlanServiceImpl.java\",\"ClusterServiceImpl.java\",\"PipelineQcServiceImpl.java\"],\"com/zto/devops/pipeline/infrastructure/gateway/config\":[\"HarborConfigImpl.java\"],\"com/zto/devops/pipeline/domain/pipe/pipeline\":[\"AutoEvnBasePipeline.java\",\"InstanceDeletePipeline.java\",\"CoreApplicationPipeline.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/instance/delete\":[\"InstanceDeleteStep.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/test\":[\"BaseAutoTestStep.java\"],\"com/zto/devops/pipeline/domain/pipeline\":[\"NoticeCommandHandlerProcessor.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/instance/deploy\":[\"InstanceDeployStep.java\",\"DeployBaseStep.java\",\"InstanceDeployAppletStep.java\",\"InstanceDeployOssStep.java\"],\"com/zto/devops/pipeline/infrastructure/message/auditing/flow\":[\"AcceptMessage.java\"],\"com/zto/devops/pipeline/client/model/plan/command\":[\"AbortNodeCommand.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/application\":[\"BuildStep.java\"],\"com/zto/devops/pipeline/application/handler/mq\":[\"XiaoJiMqHandler.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/instance/create\":[\"CommonInstanceCreateStep.java\",\"InstanceCreateStep.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/instance\":[\"InstanceBaseStep.java\"],\"com/zto/devops/pipeline/application/service/refresh\":[\"RefreshZkeServiceImpl.java\"],\"com/zto/devops/pipeline/domain/model/instance\":[\"OssInstance.java\",\"AppletInstance.java\",\"ContainerInstance.java\",\"WithoutInstance.java\",\"VmInstance.java\"],\"com/zto/devops/pipeline/infrastructure/gateway/gitlab\":[\"ApplicationGitlabServiceImpl.java\"],\"com/zto/devops/pipeline/domain/util\":[\"JvmParamUtil.java\"],\"com/zto/devops/pipeline/domain/model/application\":[\"ApplicationPipeline.java\"],\"com/zto/devops/pipeline/infrastructure/gateway/flow\":[\"PublishServiceImpl.java\"],\"com/zto/devops/pipeline/infrastructure/gateway/zke\":[\"ContainerGatewayImpl.java\"],\"com/zto/devops/pipeline/infrastructure/dao/typehandler\":[\"ApplicationTypeInputHandler.java\"],\"com/zto/devops/pipeline/infrastructure/gateway/jenkins\":[\"JenkinsServiceImpl.java\",\"AbstractJenkinsService.java\",\"JenkinsVmServiceImpl.java\"],\"com/zto/devops/pipeline/domain/pipe\":[\"BaseNode.java\"],\"com/zto/devops/pipeline/infrastructure/gateway/oss\":[\"OssGatewayImpl.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/instance/fire\":[\"InstanceHealthCheckStep.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/instance/copy\":[\"InstanceCopyStep.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/scan\":[\"AbstractCodeScanStep.java\"],\"com/zto/devops/pipeline/infrastructure/pipeline/engine/jeasy\":[\"HttpUtility.java\"],\"com/zto/devops/pipeline/handler\":[\"VersionDeploymentHandler.java\",\"ProductHandler.java\"],\"com/zto/devops/pipeline/client/model/flow/command/deployment\":[\"EndCreateBranchDeploymentCommand.java\"],\"com/zto/devops/pipeline/domain/service\":[\"ApplicationQueryDomainService.java\",\"FlowAuditQueryDomainService.java\",\"PlanQueryDomainService.java\",\"InstanceQueryDomainService.java\",\"ClusterCommandDomainService.java\",\"AutoEnvQueryDomainService.java\",\"FlowQueryDomainService.java\",\"GrayscaleCommandDomainService.java\",\"PipelineExecutionQueryDomainService.java\",\"PipelineCockpitQueryService.java\",\"AbstractPlanAdapter.java\",\"InstanceCommandDomainService.java\",\"PipelineExecutionCommandDomainService.java\",\"FlowCommandDomainService.java\",\"ClusterQueryDomainService.java\",\"NamespaceCommandDomainService.java\",\"NamespaceQueryDomainService.java\",\"ApplicationCommandDomainService.java\",\"FlowAuditCommandDomainService.java\",\"PlanConfigDomainService.java\",\"PipelineToQcCommandDomainService.java\"],\"com/zto/devops/pipeline/application/handler\":[\"DeploymentHandler.java\",\"JenkinsLogHandler.java\"],\"com/zto/devops/pipeline/domain/service/deployer\":[\"AbstractDeployManager.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/instance/stop\":[\"InstanceStopStep.java\"],\"com/zto/devops/pipeline/infrastructure/util\":[\"JobResultLogUtil.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/instance/restart\":[\"InstanceRestartStep.java\"],\"com/zto/devops/pipeline/domain/pipe/utils\":[\"NoticeCommandUtils.java\"],\"com/zto/devops/pipeline/infrastructure/gateway/vm\":[\"SaltServiceImpl.java\"],\"com/zto/devops/pipeline/infrastructure/gateway/repository\":[\"UserFlowRepositoryImpl.java\",\"PlanRepositoryImpl.java\",\"MergeRequestRepositoryImpl.java\",\"PipelineExecutionRepositoryImpl.java\",\"FlowRepositoryImpl.java\",\"ApplicationRepositoryImpl.java\"],\"com/zto/devops/pipeline/domain/pipe/steps/flow\":[\"SmokePassStep.java\",\"BaseTestStep.java\",\"TestPassStep.java\",\"SubmitTestStep.java\"]}";
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            // Convert JSON string to Map<String, List<String>>
            Map<String, List<String>> packageClassMap = objectMapper.readValue(locatedSrc, Map.class);

            jacocoService.copyJavaSrc(allSrcList, packageClassMap, new File("E:\\data\\VER24070100428-release-V3.927.0-devops-pipeline", "javaSrc"));
            // Print the map contents
//            for (Map.Entry<String, List<String>> entry : packageClassMap.entrySet()) {
//                System.out.println("Package: " + entry.getKey());
//                System.out.println("Classes: " + entry.getValue());
//                System.out.println();
//            }

        } catch (Exception e) {
            e.printStackTrace();
        }
//        Map<String, List<String>> locatedSrc = new HashMap<>();
//        locatedSrc.put("com/zto/devops/middleware/domain/service", Arrays.asList("ResourceQueryService.java"));
//        locatedSrc.put("com/zto/devops/middleware/application/service", Arrays.asList("ResourceSearchServiceImpl.java"));
//        jacocoService.copyJavaSrc(allSrcList, locatedSrc, new File("E:\\data\\VER24072200117-release-V3.976.0-devops-middleware"));
    }

    @Test
    public void saveCoveragePublish() {
        String flowCode = "DINT241112003001";
        String planCode = "EXE241128005001";  // EXE241125008005 EXE241126002033 EXE241126005009 EXE241128005001
        User user = new User(123456L, "xjf");
        List<JacocoApplicationVO> apps = coverageDomainService.checkApps(flowCode, planCode, "DeployEndedEvent");
        coverageDomainService.saveCoveragePublish(apps, user);
    }


}
