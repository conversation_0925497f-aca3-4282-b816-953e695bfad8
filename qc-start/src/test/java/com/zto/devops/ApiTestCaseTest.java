package com.zto.devops;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.infrastructure.converter.ApiTestEntityConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class ApiTestCaseTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "xjf");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5431563,\"cnName\":\"许建峰\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\"," +
                "\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
        System.setProperty("javaagent", "D:\\fat-agent\\2.9.11.2\\agent.jar");
        System.setProperty("zss.worker.config.label", "xjf");
    }

    @Autowired
    private ApiTestEntityConverter apiTestEntityConverter;

    public static void addExcel(List<LinkedHashMap> apiMaps) {
        try (Workbook workbook = new HSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Result");
            Row headerRow = sheet.createRow(0);
            Cell headerCell = headerRow.createCell(0);
            headerCell.setCellValue("APP_ID");
            Cell headerCell1 = headerRow.createCell(1);
            headerCell1.setCellValue("API_ADDRESS");
            Cell headerCell2 = headerRow.createCell(2);
            headerCell2.setCellValue("是否覆盖");
            for (int i = 0; i < apiMaps.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Cell cell = row.createCell(0);
                cell.setCellValue((String) apiMaps.get(i).get("app_id"));
                Cell cell1 = row.createCell(1);
                cell1.setCellValue((String) apiMaps.get(i).get("api_address"));
                Cell cell2 = row.createCell(2);
                cell2.setCellValue((String) apiMaps.get(i).get("isCover"));
            }
            try (FileOutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Downloads\\output.xls")) {
                workbook.write(outputStream);
                System.out.println("Excel文件已成功输出！");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static Set<String> extractPathUrls(String input) {
        Set<String> resultList = new HashSet<>();

        // 使用正则表达式匹配"pathUrl"和引号之间的内容
        Pattern pattern = Pattern.compile("\"pathUrl\":\\s*\"(.*?)\"");
        Matcher matcher = pattern.matcher(input);

        // 遍历所有匹配结果，将内容添加到结果列表中
        while (matcher.find()) {
            String pathUrl = matcher.group(1);
            resultList.add(pathUrl);
        }

        return resultList;
    }

    public static Set<String> extractPathUrls(String input, String patten) {
        Set<String> resultList = new HashSet<>();

        // 使用正则表达式匹配"pathUrl"和引号之间的内容
        Pattern pattern = Pattern.compile(patten);
        Matcher matcher = pattern.matcher(input);

        // 遍历所有匹配结果，将内容添加到结果列表中
        while (matcher.find()) {
            String pathUrl = matcher.group(1);
            resultList.add(pathUrl);
        }

        return resultList;
    }

    @Test
    public void getDbId() {
        String databaseUrl = "*******************************************";
        User user = new User();
        user.setUserId(123456L);
        user.setUserName("test");
//        Integer id = apiTestCommandDomainService.getPhysicsDbId(databaseUrl, "PRO_152", user, UseCaseFactoryTypeEnum.SCENE, "SNF000000000001");
//        System.out.println(id);
    }


}
