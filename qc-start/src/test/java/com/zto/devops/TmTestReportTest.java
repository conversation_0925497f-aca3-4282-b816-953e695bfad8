package com.zto.devops;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.devops.qc.client.service.report.ITestReportService;
import com.zto.devops.qc.client.service.report.model.*;
import com.zto.devops.qc.client.service.testmanager.cases.ITestcaseService;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseModuleReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ListTestcaseModuleResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class TmTestReportTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "gaoyanqi2035");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5664313,\"cnName\":\"高延奇\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\",\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
    }

    @Resource
    private ITestReportService testReportService;
    @Resource
    private ITestcaseService testcaseService;

    /**
     * listModule
     */
    @Test
    public void listModule() {
        ListTestcaseModuleReq req = new ListTestcaseModuleReq();
        req.setType(TestcaseTypeEnum.AUTO);
        req.setProductCode("399");
        Result<List<ListTestcaseModuleResp>> result = testcaseService.listModule(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

    /**
     * 准入报告详情
     */
    @Test
    public void detailAccessReport() {
        QueryTestReportDetailReq req = new QueryTestReportDetailReq();
        req.setReportCode("TR230301008054");
        req.setPlanCode("TP230301008230");
        Result<TmAccessReportDetailResp> result = testReportService.detailAccessReport(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

    /**
     * 准出报告详情
     */
    @Test
    public void detailPermitReport() {
        QueryTestReportDetailReq req = new QueryTestReportDetailReq();
        req.setReportCode("TR230301008054");
        req.setPlanCode("TP230301008230");
        Result<TmPermitReportDetailResp> result = testReportService.detailPermitReport(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

    /**
     * 线上冒烟报告详情
     */
    @Test
    public void detailOnlineSmokeReport() {
        QueryTestReportDetailReq req = new QueryTestReportDetailReq();
        req.setReportCode("TR230301008054");
        req.setPlanCode("TP230301008230");
        Result<TmOnlineSmokeReportDetailResp> result = testReportService.detailOnlineSmokeReport(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

    /**
     * 用例评审报告-详情
     */
    @Test
    public void detailReviewReport() {
        QueryReviewReportReq req = new QueryReviewReportReq();
        req.setReportCode("TR230420008000");
        req.setPlanCode("TP230301008230");
        req.setReportUserId(5664313L);
        req.setProductCode("PRO2302078000");
        Result<ReviewReportDetailResp> result = testReportService.detailReviewReport(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

    /**
     * 测试报告--查询测试信息
     */
    @Test
    public void queryCaseExecuteResult() {
        QueryCaseExecuteResultReq req = new QueryCaseExecuteResultReq();
        req.setPlanCode("TP230301008230");
        req.setReportType(TestReportTypeEnum.TEST_PERMIT);
        Result<CaseExecuteResultVO> result = testReportService.queryCaseExecuteResult(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

}
