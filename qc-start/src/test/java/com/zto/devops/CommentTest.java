package com.zto.devops;

import com.alibaba.fastjson.JSON;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.service.comment.CommentService;
import com.zto.devops.qc.client.service.comment.model.AddCommentReq;
import com.zto.devops.qc.client.service.comment.model.ListCommentsByBusinessCodeReq;
import com.zto.devops.qc.client.service.comment.model.RemoveCommentReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class CommentTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "gaoyanqi2035");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
    }

    @Resource
    private CommentService commentService;

    /**
     * 查询评价
     */
    @Test
    public void queryComment() {
        ListCommentsByBusinessCodeReq req = new ListCommentsByBusinessCodeReq();
        req.setBusinessCode("ISS230303008063");
        Result<List<CommentVO>> result = commentService.listCommentsByBusinessCode(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

    /**
     * 新增评价
     */
    @Test
    public void addComment() {
        AddCommentReq req = new AddCommentReq();
        req.setBusinessCode("ISS230303008063");
        req.setContent(new Date().toString());
        Result<Void> result = commentService.addComment(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

    /**
     * 删除评价
     */
    @Test
    public void removeComment() {
        RemoveCommentReq req = new RemoveCommentReq();
        req.setBusinessCode("ISS230303008063");
        req.setCode("SNF852125784779784192");
        Result<Void> result = commentService.removeComment(req);
        log.info("result:{}", JSON.toJSONString(result));
    }

}
