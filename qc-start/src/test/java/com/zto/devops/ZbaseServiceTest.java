package com.zto.devops;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.domain.gateway.zbase.ZbaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class ZbaseServiceTest {

    @Autowired
    private ZbaseService zbaseService;
    @Autowired
    private HttpService httpService;

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "xjf");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5431563,\"cnName\":\"许建峰\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\"," +
                "\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
        System.setProperty("javaagent", "D:\\fat-agent\\2.9.11.2\\agent.jar");
        System.setProperty("zss.worker.config.label", "xjf");
    }

    @Test
    public void getDbByCode() {
        JSONArray productDbs = zbaseService.queryProductDb("PRO_236");
    }

    @Test
    public void getProductCode() {
        String url = "https://luban.gw.zt-express.com/product/page";
        String param = "{\"testUserIds\":null,\"productLineCode\":null,\"categorys\":null,\"testData\":null,\"code\":null,\"userNumberLevels\":null,\"projectUserIds\":null,\"useFrequencys\":null," +
                "\"deptIds\":null,\"description\":null,\"userTypes\":null,\"lifeCycles\":null,\"developUserIds\":null,\"productUserIds\":null,\"userSources\":null,\"freezes\":null,\"size\":1000," +
                "\"productLineName\":null,\"name\":null,\"page\":1,\"levels\":null,\"productSources\":null,\"isMiddleware\":null,\"isTest\":null,\"productLevelList\":null,\"tenantCodes\":null}";
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("cookie", "useJava=1; wyzdzjxhdnh=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; x_ys_dt=c12a5019c5c34ffbb89a30754dad1f3f_4qLoINPw/hFEFkEEEVOEp0e6GDW+t9ty; wyandyy=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
        String resp = httpService.doPostWithHeader(url, param, headerMap);
        JSONObject object = JSON.parseObject(resp);
        JSONArray array = object.getJSONArray("data");
        if (null == array) {
            return;
        }

        List<JSONObject> dbList = new ArrayList<>();

        for (int i = 0; i < array.size(); i++) {
            JSONObject object1 = array.getJSONObject(i);
            String productCode = object1.getString("code");
            if (StringUtil.isEmpty(productCode)) {
                continue;
            }
            JSONArray productDbs = zbaseService.queryProductDb(productCode);
            if (null == productDbs) {
                continue;
            }
            for (int j = 0; j <productDbs.size() ; j++) {
                JSONObject productDbObj = productDbs.getJSONObject(j);
                String id = productDbObj.getString("id");
                JSONObject physicalDbInfo = zbaseService.queryPhysicalDbInfo(id);
                if (Objects.isNull(physicalDbInfo)) {
                    log.error("获取数据库物理信息异常.{}", id);
                    throw new ServiceException("获取数据库物理信息为空。请确认数据库是否正确");
                }
                JSONArray physicsDbs = physicalDbInfo.getJSONArray("dblresourceRelatePhysicsDbList");
                if (CollectionUtil.isEmpty(physicsDbs)) {
                    continue;
                }
                JSONObject dbObject = physicalDbInfo.getJSONArray("dblresourceRelatePhysicsDbList").getJSONObject(0);
                if (StringUtil.isEmpty(dbObject.getString("env")) || StringUtil.isEmpty(dbObject.getString("masterDb"))
                        || StringUtil.isEmpty(dbObject.getString("schema"))) {
                    continue;
                }

                JSONObject dbAccountInfo = zbaseService.queryDbAccountInfo(physicalDbInfo);
                if (Objects.isNull(dbAccountInfo) || !"SYS000".equals(dbAccountInfo.getString("statusCode"))) {
                    log.error("获取数据库账户信息异常.{}, {}", physicalDbInfo, dbAccountInfo);
                    throw new ServiceException("获取数据库账户信息异常。请确认数据库是否正确");
                }
                log.info("dbAccountInfo >>> {}", dbAccountInfo.toJSONString());
                JSONObject dbJson = new JSONObject();
                dbJson.put("productCode", productDbObj.getString("productCode"));
                dbJson.put("productName", productDbObj.getString("productName"));
                dbJson.put("env", dbObject.getString("env"));
                dbJson.put("ip", dbObject.getString("masterDb"));
                dbJson.put("schema", dbObject.getString("schema"));
                String username = "";
                String password = "";

                if (null == dbAccountInfo.getJSONObject("result")) {
                    log.error(">>>>>>>>>>>>>>>>>账户信息不存在。env : {}, ip : {}, schema : {}", dbObject.getString("env"), dbObject.getString("masterDb"), dbObject.getString("schema"));
                } else {
                    username = dbAccountInfo.getJSONObject("result").getString("username");
                    password = dbAccountInfo.getJSONObject("result").getString("password");
                }
//                dbJson.put("username", username);
//                dbJson.put("password", password);
                if (StringUtil.isEmpty(username) || StringUtil.isEmpty(password)) {
                    dbList.add(dbJson);
                }
            }
        }
        addExcel(dbList);

    }

    public static void addExcel(List<JSONObject> dbList) {
        try (Workbook workbook = new HSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Result");
            Row headerRow = sheet.createRow(0);
            Cell headerCell = headerRow.createCell(0);
            headerCell.setCellValue("env");
            Cell headerCell1 = headerRow.createCell(1);
            headerCell1.setCellValue("ip");
            Cell headerCell2 = headerRow.createCell(2);
            headerCell2.setCellValue("schema");
//            Cell headerCell3 = headerRow.createCell(3);
//            headerCell3.setCellValue("username");
//            Cell headerCell4 = headerRow.createCell(4);
//            headerCell4.setCellValue("password");
            Cell headerCell5 = headerRow.createCell(5);
            headerCell5.setCellValue("productCode");
            Cell headerCell6 = headerRow.createCell(6);
            headerCell6.setCellValue("productName");
            for (int i = 0; i < dbList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Cell cell = row.createCell(0);
                cell.setCellValue((String) dbList.get(i).get("env"));
                Cell cell1 = row.createCell(1);
                cell1.setCellValue((String) dbList.get(i).get("ip"));
                Cell cell2 = row.createCell(2);
                cell2.setCellValue((String) dbList.get(i).get("schema"));
//                Cell cell3 = row.createCell(3);
//                cell3.setCellValue((String) dbList.get(i).get("username"));
//                Cell cell4 = row.createCell(4);
//                cell4.setCellValue((String) dbList.get(i).get("password"));
                Cell cell5 = row.createCell(5);
                cell5.setCellValue((String) dbList.get(i).get("productCode"));
                Cell cell6 = row.createCell(6);
                cell6.setCellValue((String) dbList.get(i).get("productName"));
            }
            try (FileOutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Downloads\\db-result.xls")) {
                workbook.write(outputStream);
                System.out.println("Excel文件已成功输出！");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
