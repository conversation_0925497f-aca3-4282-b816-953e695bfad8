package com.zto.devops;

import com.zto.devops.qc.domain.gateway.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class RedisTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "xjf");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5431563,\"cnName\":\"许建峰\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\"," +
                "\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
        System.setProperty("javaagent", "D:\\fat-agent\\********\\agent.jar");
        System.setProperty("zss.worker.config.label", "xjf");
    }

    @Autowired
    private RedisService redisService;

    private static final String AGENT_PREFIX = "AGENT_IP";
    private static final String APP_ID = "devops-qc";
    private static final String VERSION_CODE = "VER24080800153";


    @Test
    public void clearRedis() {
//        List<String> keys = Arrays.asList("SNF902927880730705920");
        List<String> keys = Arrays.asList("wxc-java-demo.VER24072900127.com.zto.qamp.service.impl.DubboTestServiceImpl:16",
                "wxc-java-demo.VER24072900127.com.zto.qamp.service.impl.DubboTestServiceImpl:17",
                "wxc-java-demo.VER24072900127.com.zto.qamp.service.impl.DubboTestServiceImpl:18");

        for (String key : keys) {
            if (redisService.hasKey(key)) {
                redisService.delete(key);
            }
        }
    }

    @Test
    public void opsForZSetAddFirst() {
        List<String> values = new ArrayList<>();
        values.add("***********");
        values.add("***********");
        values.add("***********");
        String key = AGENT_PREFIX + "_" + APP_ID + "_" + VERSION_CODE;
        clear(key);
        for (String value : values) {
            redisService.opsForZSetAddFirst(key, value);
        }
        log.info(redisService.opsForZSetRang(key, 0, -1).toString());
    }

    @Test
    public void opsForZSetAddLast() {
        String key = AGENT_PREFIX + "_" + APP_ID + "_" + VERSION_CODE;
        clear(key);
        List<String> values = new ArrayList<>();
        values.add("***********");
        values.add("***********");
        values.add("***********");
        for (String value : values) {
            redisService.opsForZSetAddLast(key, value);
        }
        log.info(redisService.opsForZSetRang(key, 0, -1).toString());
    }

    @Test
    public void opsForZSetRang() {
        String key = AGENT_PREFIX + "_" + APP_ID + "_" + VERSION_CODE;
        Set<String> ips = redisService.opsForZSetRang(key, 0, -1);
        log.info(ips.toString());
    }

    @Test
    public void opsForZSetRemove() {
        String key = AGENT_PREFIX + "_" + APP_ID + "_" + VERSION_CODE;
        redisService.opsForZSetRemove(key, "***********");
        log.info(redisService.opsForZSetRang(key, 0, -1).toString());
    }

    private void clear(String key) {
        if (redisService.hasKey(key)) {
            redisService.delete(key);
        }
    }


}
