package com.zto.devops;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.rpc.MemberTypeEnum;
import com.zto.devops.qc.client.enums.rpc.VersionStatus;
import com.zto.devops.qc.client.model.issue.query.ListIssueForVersionQuery;
import com.zto.devops.qc.client.model.parameter.IssueQueryParameter;
import com.zto.devops.qc.client.model.rpc.pipeline.query.FindOssFileUrlQuery;
import com.zto.devops.qc.client.model.rpc.product.ProductAll;
import com.zto.devops.qc.client.model.rpc.product.ProductAllVO;
import com.zto.devops.qc.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.qc.client.model.rpc.product.query.AllProductsQuery;
import com.zto.devops.qc.client.model.rpc.product.query.ListProductMemberByPIdQuery;
import com.zto.devops.qc.client.model.rpc.project.VersionInfoVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiCaseDocVO;
import com.zto.devops.qc.client.service.issue.IIssueService;
import com.zto.devops.qc.client.service.testmanager.apitest.ApiTestService;
import com.zto.devops.qc.domain.gateway.http.HttpService;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProductRpcService;
import com.zto.devops.qc.domain.gateway.rpc.IProjectRpcService;
import com.zto.devops.qc.infrastructure.dao.entity.KnowledgeBaseEntity;
import com.zto.devops.qc.infrastructure.dao.mapper.IssueMapper;
import com.zto.devops.qc.infrastructure.dao.mapper.KnowledgeBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Profile("fat")
@Slf4j
public class Test {

    @Autowired
    private IIssueService iIssueService;

    @Autowired
    private IPipelineRpcService pipelineRpcService;

    @Autowired
    private IssueMapper issueMapper;

    @Autowired
    private IProjectRpcService projectRpcService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;
    @Autowired
    private HttpService httpService;

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "xjf");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5431563,\"cnName\":\"许建峰\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\"," +
                "\"userCode\":\"02100.3910\",\"empNo\":\"NC00760553\"}");
        System.setProperty("javaagent", "D:\\fat-agent\\********\\agent.jar");
        System.setProperty("zss.worker.config.label", "xjf");
    }


    @org.junit.Test
    public void testListIssueForVersionQuery() {
        ListIssueForVersionQuery query = new ListIssueForVersionQuery();
        query.setProductCode("PRO2302018001");
        query.setFixVersionCode("VER2303308137");
        query.setTransactor(new User(4732501L, "滴滴滴"));
        iIssueService.listIssueForVersionQuery(query);
    }

    @org.junit.Test
    public void testSelectByDefinedQuery() {
        IssueQueryParameter query = new IssueQueryParameter();
    }

    @org.junit.Test
    public void testFindOssFileUrlQuery() {
        FindOssFileUrlQuery query = new FindOssFileUrlQuery();
        query.setFileName("all_devops-qc_qc-start_9cb626bd604d4c4590f9176f2a0a351b.zip");
        String downloadUrl = pipelineRpcService.downloadUrl(query);
        log.info(">>>>>>downloadUrl = {}", downloadUrl);
        httpService.downloadFromUrl(downloadUrl, "E:\\data");
    }

    @org.junit.Test
    public void testFindVersionBaseInfoQuery() {
        VersionInfoVO versionInfoVO = projectRpcService.findVersionBaseInfoQuery("VER2211118009");
        if (versionInfoVO != null && (VersionStatus.RELEASING.name().equals(versionInfoVO.getStatus())
                || VersionStatus.ACCEPTING.name().equals(versionInfoVO.getStatus())
                || VersionStatus.ACCEPTED.name().equals(versionInfoVO.getStatus())
                || VersionStatus.CLOSED.name().equals(versionInfoVO.getStatus()))) {
            throw new ServiceException("当前版本状态为[发布中、验收中、已验收或已关闭]，不能生成覆盖率。");
        }
    }


    @org.junit.Test
    public void test() {
        String jsonStr = "{\n" + "      \"userId\": \"5878415\",\n" + "      \"name\": \"涂德恩·测试知识QUAN1\",\n" + "      \"description\": \"测试数据\"\n" + "    }";
        String s = httpService.doPost("https://zim.gw-test.ztosys.com/docs/space/create", jsonStr);
        System.out.println(s);
    }

    @org.junit.Test
    public void testAuthority() {
        String json = "{" + "\"spaceId\":\"rO5pXBWgNV1Qm7Zv\"," + "\"userId\":\"devops\"," + "\"members\":[" + "{\"memberType\":\"USER\",\"memberId\":\"5878415\",\"roleType\":\"EDITOR\"}]" + "}";
        System.out.println(json);

//        HashMap<String, Object> result = new HashMap<>();
//        List<Map> objects = new ArrayList<>();
//
//        HashMap<String, String> members = new HashMap<>();
//        HashMap<String, String> members2 = new HashMap<>();
//        members2.put("memberType", "USER");
//        members2.put("memberId", "6441659");
//        members2.put("roleType", "EDITOR");
////        members.put("memberType", "USER");
////        members.put("memberId", "5020099");
////        members.put("roleType", "EDITOR");
//        objects.add(members);
//        objects.add(members2);
//        result.put("userId", "5431563");
//        result.put("spaceId", "rO5pXBW4Qovom7Zv");
//        result.put("members", objects);
//        JSONObject object = new JSONObject(result);
//        String json = object.toString();
        String s = httpService.doPost("https://zim.gw-test.ztosys.com/docs/space/members/add", json);
        System.out.println(s);
    }


    @org.junit.Test
    public void testBatchKnowledge() {
        AllProductsQuery query = new AllProductsQuery();
        query.setPage(1);
        query.setSize(99999);
        ProductAllVO vo = productRpcService.allProductsQuery(query);
        List<KnowledgeBaseEntity> list = new ArrayList<>();

        HashMap<String, Object> membersPermissions = new HashMap<>();
        List<Map> members = new ArrayList<>();

        for (ProductAll productVO : vo.getList()) {
            HashMap<String, Object> req = new HashMap<>();
            req.put("userId", productVO.getProductUserId());
            req.put("name", productVO.getName() + "知识库");
            req.put("description", "该知识库信息为全量同步信息，如有不同，请自行改之！！！");
            Map<String, Object> resultData = getResultData(httpService.doPost("https://zim.gw-test.ztosys.com/docs/space/create", new JSONObject(req).toString()));
            System.out.println("请求" + new JSONObject(req).toString());
            if (resultData == null) {
                log.info("该产品信息创建知识库失败！！！{}", productVO);
                continue;
            }
            Map<String, Object> knowledgeData = (Map<String, Object>) resultData.get("result");
            System.out.println("返回" + knowledgeData);

            if (knowledgeData != null) {
                KnowledgeBaseEntity entity = new KnowledgeBaseEntity();
                entity.setSpaceId((String) knowledgeData.get("id"));
                entity.setUrl((String) knowledgeData.get("url"));
                entity.setProductCode(productVO.getCode());
                entity.setCreatorId(Long.valueOf(productVO.getProductUserId()));
                entity.setCreator(productVO.getProductUserName());
                entity.setModifierId(Long.valueOf(productVO.getProductUserId()));
                entity.setModifier(productVO.getProductUserName());
                entity.setGmtCreate(new Date());
                entity.setGmtModified(new Date());
                list.add(entity);

                ListProductMemberByPIdQuery listProductMemberByPIdQuery = new ListProductMemberByPIdQuery();
                listProductMemberByPIdQuery.setProductCode(productVO.getCode());
                List<ProductMemberVO> productMemberVOS = productRpcService.listProductMemberByPIdQuery(listProductMemberByPIdQuery);

                if (CollectionUtil.isNotEmpty(productMemberVOS)) {
                    for (ProductMemberVO productMemberVO : productMemberVOS) {
                        HashMap<String, String> member = new HashMap<>();
                        if (productMemberVO.getMemberType().equals(MemberTypeEnum.PRODUCTER_OWNER)) {
                            member.put("roleType", "MANAGER");
                        } else {
                            member.put("roleType", "EDITOR");
                        }
                        member.put("memberType", "USER");
                        member.put("memberId", String.valueOf(productMemberVO.getUserId()));
                        member.put("roleType", "EDITOR");
                        members.add(member);
                    }
                    if (CollectionUtil.isNotEmpty(members)) {
                        membersPermissions.put("userId", productVO.getProductUserId());
                        membersPermissions.put("spaceId", knowledgeData.get("id").toString());
                        membersPermissions.put("members", members);
                        //添加产品下成员权限
                        addMemberPermissions(membersPermissions);
                    }
                }
            }
        }
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        knowledgeBaseMapper.insertBatch(list);
    }


    public Map<String, Object> getResultData(String data) {
        if (StringUtil.isEmpty(data)) {
            return null;
        }
        return JSONObject.parseObject(data, Feature.OrderedField);
    }


    public void addMemberPermissions(Map<String, Object> memberInfos) {
        if (memberInfos != null) {
            JSONObject object = new JSONObject(memberInfos);
            String json = object.toString();
            String s = httpService.doPost("https://zim.gw-test.ztosys.com/docs/space/members/add", json);
            System.out.println(s);
        }
    }

    @Autowired
    private ApiTestService apiTestService;
    @org.junit.Test
    public void test11(){
        List<Long> list = new ArrayList<>();
        list.add(918908L);
        list.add(919327L);
        list.add(416808L);

        List<ApiCaseDocVO> apiCaseDocVOS = apiTestService.listApiCaseByDocIds("399", list);
        System.out.println(apiCaseDocVOS);
    }

}
