package com.zto.devops;

import com.zto.devops.pipeline.client.enums.FlowStatusEnum;
import com.zto.devops.pipeline.client.model.flow.event.VersionStatusChangedEvent;
import com.zto.devops.qc.adapter.handler.PipelineHandler;
import com.zto.devops.qc.client.model.rpc.pipeline.NamespaceResp;
import com.zto.devops.qc.domain.gateway.rpc.IPipelineRpcService;
import com.zto.devops.qc.domain.service.CoverageDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @create 2022/9/18 10:52
 */
@ActiveProfiles("fat")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class PipeineApiTest {

    static {
        System.setProperty("console.log", "true");
        System.setProperty("titans.dubbo.tag", "env39790fat");
        System.setProperty("env", "fat");
        System.setProperty("app.secret", "5caef4076a9b40238549c6d48019f30f");
        System.setProperty("app.id", "devops-qc");
        System.setProperty("gateway.http.enable", "true");
        System.setProperty("gateway.http.userinfo", "{\"ssoUserId\":5431563,\"cnName\":\"许建峰\",\"deptId\":817355,\"deptNo\":\"716186.509317.595739.817252.817355\",\"deptName\":\"研发效能部\"," +
                "\"userCode\":\"02100.3910\",\"empNo\":\"**********\"}");
        System.setProperty("javaagent", "D:\\fat-agent\\********\\agent.jar");
        System.setProperty("zss.worker.config.label", "xjf");
    }

    @Autowired
    private IPipelineRpcService pipelineRpcService;
    @Autowired
    private PipelineHandler pipelineHandler;


    private static final Logger logger = Logger.getLogger(CoverageDomainService.class.getName());

    @Test
    public void getCoverageReportListPage() {
        String appId = "devops-qc";
        String tag = "env39790fat";
        String versionCode =
                pipelineRpcService.getVersionSimpleVO(appId, tag);
        logger.info(versionCode);
    }

    @Test
    public void findBaseNamespace() {
        NamespaceResp resp = pipelineRpcService.findBaseNamespace("399");
        logger.info(String.valueOf(resp));
    }

    @Test
    public void handleVersionStatusChangedEvent() {
        VersionStatusChangedEvent event = new VersionStatusChangedEvent();
        event.setVersionCodes(Arrays.asList("VER24111200116"));
        event.setStatus(FlowStatusEnum.ACCEPTING);
        pipelineHandler.handleVersionStatusChangedEvent(event);
    }


}
