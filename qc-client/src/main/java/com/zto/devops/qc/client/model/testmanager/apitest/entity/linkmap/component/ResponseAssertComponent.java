package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.Locale;

@Data
public class ResponseAssertComponent extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    String testField="Assertion.response_data"; //Assertion.response_headers;Assertion.response_code;Assertion.response_message;Assertion.request_headers;Assertion.request_data
    String testType="CONTAINS";//CONTAINS;MATCH;EQUALS;SUBSTRING;
    boolean assumeSuccess = false; //忽略状态
    boolean ifNot = false;
    boolean ifOr = false;
    ArrayList<String> testStrings = null;
    String customMessage;

    public Integer countTestType(){
        int MATCH = 1 << 0;

        int CONTAINS = 1 << 1;

        int NOT = 1 << 2;

        int EQUALS = 1 << 3;

        int SUBSTRING = 1 << 4;

        int OR = 1 << 5;

        int testTypeIneter = CONTAINS;
        if("MATCH".equals(testType.toUpperCase(Locale.ROOT).trim())){
            testTypeIneter = MATCH;
        }
        if("EQUALS".equals(testType.toUpperCase(Locale.ROOT).trim())){
            testTypeIneter = EQUALS;
        }
        if("SUBSTRING".equals(testType.toUpperCase(Locale.ROOT).trim())){
            testTypeIneter = SUBSTRING;
        }

        if(ifNot){
            testTypeIneter = testTypeIneter | NOT;
        }

        if(ifOr){
            testTypeIneter = testTypeIneter | OR;
        }

        return Integer.valueOf(testTypeIneter);

    }
}
