package com.zto.devops.qc.client.service.coverage.model.req;


import com.zto.devops.qc.client.enums.testmanager.coverage.MethodTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ZcatMqBodyReq implements Serializable {

    private static final long serialVersionUID = -6880156873561985078L;

    @ZModelProperty(description = "接口类型", required = true, sample = "DUBBO/HTTP/MQ/JOB")
    private MethodTypeEnum interfaceType;

    @ZModelProperty(description = "应用名", required = true, sample = "zto-qamp")
    private String appId;

    @ZModelProperty(description = "应用ip", required = true, sample = "*************")
    private String location;

    @ZModelProperty(description = "版本")
    private String versionCode;

    @ZModelProperty(description = "接口", required = true, sample = "/transfer/task/detail")
    private String interfaceName;

    @ZModelProperty(description = "调用时间", required = true, sample = "/transfer/task/detail")
    private LocalDateTime timestamp;

}
