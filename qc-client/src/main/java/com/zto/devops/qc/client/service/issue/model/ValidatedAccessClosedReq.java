package com.zto.devops.qc.client.service.issue.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@GatewayModel(description = "验证通过并关闭")
@Data
public class ValidatedAccessClosedReq implements Serializable{

    @ZModelProperty(description = "编号", required = true, sample = "ISS230303008063")
    private String code;
    @ZModelProperty(description = "修复的版本编码", required = true, sample = "VER2302168133")
    private String fixVersionCode;
    @ZModelProperty(description = "修复的版本", required = true, sample = "V1.0.0")
    private String fixVersion;
    @ZModelProperty(description = "    备注 (JSON)", required = false, sample = "备注")
    private String content;

}
