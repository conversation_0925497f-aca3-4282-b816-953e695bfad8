package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddTmPermitReportCommand extends TmPermitReportCommand {

    @GatewayModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    public AddTmPermitReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
