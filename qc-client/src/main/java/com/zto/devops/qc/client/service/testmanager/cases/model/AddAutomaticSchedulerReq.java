package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "新增自动化定时任务请求模型")
public class AddAutomaticSchedulerReq implements Serializable {
    private static final long serialVersionUID = -136403628657126634L;

    @ZModelProperty(description = "任务名称", required = false, sample = "任务名称")
    private String schedulerName;

    @ZModelProperty(description = "所属产品", required = false, sample = "PRO2301318001")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "crontab表达式", required = false, sample = "0 0/30 * * * ?")
    private String crontab;

    @ZModelProperty(description = "运行空间", required = false, sample = "测试环境")
    private String executeEnv;

    @ZModelProperty(description = "运行空间(tag)", required = false, sample = "fat1")
    private String executeTag;

    @ZModelProperty(description = "运行空间(code)", required = false, sample = "运行空间(code)")
    private String executeSpaceCode;

    @ZModelProperty(description = "是否消息通知", required = false, sample = "1")
    private Boolean messageFlag;

    @ZModelProperty(description = "抄送人列表", required = false, sample = "[]")
    private List<User> ccList;

    public Boolean getCoverageFlag() {
        return Boolean.FALSE;
    }
}
