package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.model.testmanager.plan.entity.AddBatchCaseInBatchTestPlanVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddBatchCaseInBatchTestPlanReq implements Serializable {

    @ZModelProperty(description = "caseList", sample = "[]")
    List<AddBatchCaseInBatchTestPlanVO> caseList;

}
