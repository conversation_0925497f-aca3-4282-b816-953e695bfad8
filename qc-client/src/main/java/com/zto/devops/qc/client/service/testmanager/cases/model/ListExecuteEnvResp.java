package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteTagVO;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@ZsmpModel(description = "自动化任务执行空间响应模型")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ListExecuteEnvResp implements Serializable {
    private static final long serialVersionUID = 6142211217004506995L;

    List<ExecuteTagVO> list;

    /**
     * 数据组装
     *
     * @param tagVOS
     * @return
     */
    public static ListExecuteEnvResp buildSelf(List<ExecuteTagVO> tagVOS) {
        return ListExecuteEnvResp
                .builder()
                .list(CollectionUtil.isEmpty(tagVOS) ? new ArrayList<>() : tagVOS)
                .build();
    }
}
