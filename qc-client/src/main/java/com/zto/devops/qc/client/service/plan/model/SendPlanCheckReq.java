package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanPriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SendPlanCheckReq implements Serializable {

    @ZModelProperty(description = "所属产品id", required = false, sample = "399")
    private String productCode;

    @ZModelProperty(description = "产品名称", required = false, sample = "一站式研发平台")
    private String productName;

    @ZModelProperty(description = "关联版本id", required = false, sample = "VER2306058015")
    private String versionCode;

    @ZModelProperty(description = "版本名称", required = false, sample = "V1.0.0")
    private String versionName;

    @ZModelProperty(description = "计划编码", required = false, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "计划名称", required = false, sample = "V1.0.0测试计划")
    private String planName;

    @ZModelProperty(description = "测试策略", required = false, sample = "EXPLORE_TEST")
    private TestPlanStrategyEnum testStrategy;

    @ZModelProperty(description = "移动专项测试", required = false, sample = "1")
    private Boolean mobileSpecialTest;

    @ZModelProperty(description = "安全扫描", required = false, sample = "1")
    private Boolean securityScan;

    @ZModelProperty(description = "静态分析", required = false, sample = "0")
    private Boolean staticAnalysis;

    @ZModelProperty(description = "探索性测试", required = false, sample = "0")
    private Boolean exploratoryTest;

    @ZModelProperty(description = "计划负责人id", required = false, sample = "5984549")
    private Long planDirectorId;

    @ZModelProperty(description = "计划负责人名", required = false, sample = "计划负责人")
    private String planDirectorName;

    @ZModelProperty(description = "优先级", required = false, sample = "MEDIUM")
    private TestPlanPriorityEnum priority;

    @ZModelProperty(description = "最晚测试日期", required = false, sample = "1711987200000")
    private Date lastTestDate;

    @ZModelProperty(description = "权限测试", required = false, sample = "0")
    private Boolean permissionsTest;

    @ZModelProperty(description = "测试信息", required = false, sample = "测试信息")
    private String testInformation;

    @ZModelProperty(description = "权限测试信息", required = false, sample = "权限测试信息")
    private String permissionsTestInformation;

    @ZModelProperty(description = "关联计划code", required = false, sample = "TP240305003075")
    private String relationPlanCode;

    @ZModelProperty(description = "开发人数", required = false, sample = "1")
    private Integer developerNum;

    @ZModelProperty(description = "测试人数", required = false, sample = "1")
    private Integer testerNum;

    @ZModelProperty(description = "计划提测/准入时间", required = false, sample = "1711987200000")
    private Date accessDate;

    @ZModelProperty(description = "计划提测时间:上午-AM | 下午-PM", required = false, sample = "AM")
    private TestPlanDatePartitionEnum accessDatePartition;

    @ZModelProperty(description = "计划准出时间", required = false, sample = "1711987200000")
    private Date permitDate;

    @ZModelProperty(description = "计划提测时间:上午-AM | 下午-PM", required = false, sample = "AM")
    private TestPlanDatePartitionEnum permitDatePartition;

    @ZModelProperty(description = "计划类型", required = true, sample = "TEST_PLAN")
    private TestPlanNewTypeEnum planType;

}
