package com.zto.devops.qc.client.service.testmanager.cases;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.rpc.pipeline.AutomaticTaskCDExecutedEvent;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticAndTestcaseNoVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticSourceLogTestcaseListVO;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;

public interface IAutomaticSourceRecordService {

    Result<String> addAutomaticRecord(AddAutomaticRecordReq req);

    Result<Void> editAutomaticRecord(EditAutomaticRecordReq req);

    Result<Void> editAutomaticPersonLiable(EditAutomaticPersonLiableReq req);

    Result<String> startAutomaticRecord(String code);

    Result<Void> analysisAutomaticRecord(EditAutomaticRecordReq req);

    Result<Void> submitAnalysisAutomaticRecord(SubmitAnalysisAutomaticReq req);

    Result<Void> cancelAnalysisAutomaticRecord(SubmitAnalysisAutomaticReq req);

    Result<Void> deleteAutomaticRecord(AutomaticRecordSimpleReq req);

    Result<Void> addPyTest(AddAutomaticRecordReq req);

    Result<Void> editPyTest(EditAutomaticRecordReq req);

    Result<Void> analysisPyTest(String code);

    Result<Void> analysisPyTestCallback(AnalysisAutomaticRecordCallbackReq req);

    Result<Void> testNgAutomaticInsert(AddAutomaticRecordReq req);

    Result<Void> testNgAutomaticEdit(EditAutomaticRecordReq req);

    Result<Void> analysisAutomaticTestng(String code, String path);

    Result<Void> testNgAnalysisCallback(TestNgAnalysisCallbackReq req);

    Result<AutomaticAndTestcaseNoVO> countAutomaticAndTestcase(String code, TestcaseTypeEnum type, String productCode);

    Result<AutomaticRecordResp> queryAutomaticRecord(AutomaticRecordSimpleReq req);

    Result<AutomaticSourceLogTestcaseListVO> queryAutomaticRecordLogCase(String code);

    Result<Boolean> checkFileSize(String url);

    Result<Void> deleteTempFileName(String code);

    Result<String> downloadAutomatic(AutomaticRecordSimpleReq req);

    Result<Void> addGitJmeter(AddAutomaticRecordReq req);

    Result<Void> editGitJmeter(EditAutomaticRecordReq req);

    Result<Void> analysisGitJmeter(String code);

    Result<Void> analysisGitJmeterCallback(AnalysisAutomaticRecordCallbackReq req);

    void handleAutomaticTaskCDExecutedEvent(AutomaticTaskCDExecutedEvent event);
}
