package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.qc.client.enums.issue.Reason;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DelayFixIssueReq implements Serializable{

    @ZModelProperty(description = "编号", required = true, sample = "ISS230303008063")
    private String code;
    @ZModelProperty(description = "延期原因", required = true, sample = "EXTERNAL_REASONS")
    private Reason reason;
    @ZModelProperty(description = "备注 (JSON)", required = false, sample = "备注")
    private String content;
}
