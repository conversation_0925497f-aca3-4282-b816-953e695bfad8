package com.zto.devops.qc.client.service.testmanager.basedata;

import com.alibaba.fastjson.JSONArray;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreBrandVO;
import com.zto.devops.qc.client.model.rpc.huiyan.StoreNameVO;
import com.zto.devops.qc.client.model.rpc.outlet.ItemTypeVO;
import com.zto.devops.qc.client.model.rpc.waybill.LabelNameVO;
import com.zto.devops.qc.client.service.testmanager.basedata.model.*;

import java.util.List;

public interface BaseDataService {

    Result<JSONArray> queryUserInfo(UserQueryReq req);

    Result<List<ItemTypeVO>> itemType(ItemTypeReq req);

    Result<List<LabelNameVO>> queryTagList(TagListQueryReq req);

    Result<List<ItemTypeVO>> queryClassList(ClassListQueryReq req);

    Result<List<StoreBrandVO>> queryStoreBrand(StoreBrandQueryReq req);

    Result<List<StoreNameVO>> queryStoreName(ClassListQueryReq req);

}
