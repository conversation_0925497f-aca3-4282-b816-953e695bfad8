package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "用例移除附件模型")
public class RemoveTestcaseAttachmentReq implements Serializable {

    @ZModelProperty(description = "附件code", required = true, sample = "SNF967020220159885312")
    private String code;

    @ZModelProperty(description = "计划用例操作日志code", sample = "SNF967020220159885312")
    private String operateCaseCode;
}
