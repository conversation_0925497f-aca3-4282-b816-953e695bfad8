package com.zto.devops.qc.client.service.report.model;

import java.io.Serializable;

import lombok.Data;

@Data
public class AllReportDateResp implements Serializable {

    private static final long serialVersionUID = 1L;

    //版本编号
    private String versionCode;
    //计划名
    private String name;

    //计划冒烟用例数
    private Integer smokeTestCase;
    //首次通过冒烟用例数
    private Integer smokeTestCasePassed;
    //提测延期天数
    private Double delayForTest;
    //用例总数
    private Integer testCaseCount;
    //准入报告创建人
    private String createUser;
    //准入报告创建时间
    private String createTime;

    //产品负责人
    private String prdLeader;
    //测试结果
    private String testResult;
    //是否延期
    private Integer delayFlag;
    //来源
    private String sourceType;
    //验证测试结果时间
    private String checkResultDate;
    //是否按计划范围上线
    private Integer planFlag;

    //开发人数
    private Integer developerNum;
    //测试人数
    private Integer testerNum;

    //准出延期天数
    private Double AccessDelayForTest;

    //计划提测时段
    private Integer planTestHalf;

    //计划准出时段
    private Integer planAccessHalf;

    //实际准出时段
    private Integer actualAccessHalf;

    //实际提测时段
    private Integer actualTestHalf;

    //实际提测时间
    private String actualTestStartDate;

    //实际准出时间
    private String actualTestEndDate;
    //实际上线时间
    private String actualVersionEndDate;

    //计划提测时间
    private String planTestStartDate;

    //计划准出时间
    private String planTestEndDate;

    //计划上线时间
    private String planTestVersionEndDate;

    //类型
    private Integer type;
}
