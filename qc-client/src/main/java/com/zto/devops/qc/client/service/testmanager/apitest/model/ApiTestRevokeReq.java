package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ApiTestRevokeReq implements Serializable {

    private static final long serialVersionUID = 6277888134423864377L;

    @ZsmpModelProperty(description = "当前产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "授权产品code", required = true)
    private String authorizeProductCode;

}
