package com.zto.devops.qc.client.model.testmanager.scheduler.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PageSchedulerQuery extends PageQueryBase {

    private String productCode;

    @ZsmpModelProperty(description = "任务名")
    private String schedulerName;

    @ZsmpModelProperty(description = "运行结果")
    private List<AutomaticStatusEnum> resultList;

    @ZsmpModelProperty(description = "是否开启定时任务")
    private List<Boolean> switchFlagList;

    @ZsmpModelProperty(description = "运行环境")
    private List<String> envList;

    @ZsmpModelProperty(description = "运行环境Code")
    private List<String> executeSpaceCodeList;

    @ZsmpModelProperty(description = "创建人")
    private List<Long> creatorIdList;

    @ZsmpModelProperty(description = "运行开始时间")
    private Date executeStartTime;

    @ZsmpModelProperty(description = "运行结束时间")
    private Date executeEndTime;
}
