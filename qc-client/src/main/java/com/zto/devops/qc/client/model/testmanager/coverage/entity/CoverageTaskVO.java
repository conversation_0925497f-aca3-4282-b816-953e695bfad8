package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CoverageTaskVO implements Serializable {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * AppId
     */
    private String appId;

    /**
     * 标准值
     */
    private BigDecimal standardRate;

    /**
     * 覆盖率值
     */
    private BigDecimal recordRate;

    /**
     * 生成状态
     */
    private RecordStatusEnum recordStatus;

    /**
     * 生成状态描述
     */
    private String recordStatusDesc;

    /**
     * 应用数
     */
    private Integer appNums;

    /**
     * 覆盖率类型
     */
    private RecordTypeEnum recordType;

    /**
     * 覆盖率类型描述
     */
    private String recordTypeDesc;

    /**
     * 差异类型
     */
    private DiffTypeEnum diffType;

    /**
     * 差异类型描述
     */
    private String diffTypeDesc;

    /**
     * 失败原因
     */
    private String recordErrorMsg;

    /**
     * 版本code
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 空间
     */
    private String envName;

    /**
     * 生成人
     */
    private String recordCreator;

    /**
     * 生成时间
     */
    private Date recordCreate;

    /**
     * 更新时间
     */
    private Date recordModified;

    /**
     * 备注
     */
    private String comment;

    /**
     * 子节点覆盖率
     */
    private List<CoverageTaskVO> children;

    /**
     * 已覆盖行数
     */
    private Integer codeCoverNum;

    /**
     * 总行数
     */
    private Integer codeSum;

    /**
     * 报告链接
     */
    private String recordUrl;

    /**
     * commitId
     */
    private String commitId;

    /**
     * git比较地址
     */
    private String gitCompareUrl;

    /**
     * 存储桶名
     */
    private String bucketName;

    /**
     * 文件名
     */
    private String fileName;

}
