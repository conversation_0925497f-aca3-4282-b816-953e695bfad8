package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class EditSceneAuthenticationReq implements Serializable {

    @GatewayModelProperty(description = "产品code")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @GatewayModelProperty(description = "场景code")
    private String sceneCode;

}
