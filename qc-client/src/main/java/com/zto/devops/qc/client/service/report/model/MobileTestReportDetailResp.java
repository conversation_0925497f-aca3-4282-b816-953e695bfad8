package com.zto.devops.qc.client.service.report.model;

import cn.hutool.core.date.DateUtil;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MobileTestReportDetailResp extends BasicInfoResp {

    @ZModelProperty(description = "报告类型", required = false)
    private ReportType reportType;

    @ZModelProperty(description = "报告类型描述", required = false)
    private String reportTypeDesc;

    @ZModelProperty(description = "实际测试开始时间", required = false)
    private Date actualTestStart;

    @ZModelProperty(description = "实际测试开始时间--上下午", required = false)
    private String actualTestStartDay;

    @ZModelProperty(description = "实际测试结束时间", required = false)
    private Date actualTestEnd;

    @ZModelProperty(description = "实际测试结束时间-- 上下午", required = false)
    private String actualTestEndDay;

    @ZModelProperty(description = "关联计划编号", required = false)
    private String relationPlanCode;

    @ZModelProperty(description = "模块测试结果", required = false)
    private List<TmModuleTestVO> moduleTestVOS;

    public String getActualTestStartDay() {
        return Objects.isNull(this.actualTestStart) ?
                "09:00:00" : DateUtil.isPM(this.actualTestStart) ? "18:00:00" : "09:00:00";
    }

    public String getActualTestEndDay() {
        return Objects.isNull(this.actualTestEnd) ?
                "09:00:00" : DateUtil.isPM(this.actualTestEnd) ? "18:00:00" : "09:00:00";
    }
}
