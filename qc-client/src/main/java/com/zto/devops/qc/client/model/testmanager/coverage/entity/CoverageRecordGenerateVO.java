package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.entity.BaseModel;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.GenerateTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.testmanager.coverage.query.ReportDto;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022/10/21 0:18
 */
@Data
public class CoverageRecordGenerateVO extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * appId
     */
    private String appId;

    /**
     * 报告类型(BRANCH-分支,MASTER-主干)
     */
    private RecordTypeEnum recordType;

    /**
     * 生成类型(AUTO-自动生成,MANUAL-手动触发)
     */
    private GenerateTypeEnum generateType;

    /**
     * 分支名称
     */
    private String branchName;

    /**
     * git提交id
     */
    private String commitId;

    /**
     * git地址
     */
    private String gitUrl;

    /**
     * 基准分支名称
     */
    private String basicBranchName;

    /**
     * 基准commitId
     */
    private String basicCommitId;

    /**
     * 下载包名
     */
    private String packageName;

    /**
     * 下载包地址
     */
    private String downloadUrl;

    /**
     * 分支第一次部署记录(获取exec文件)
     */
    private String firstBranchCommitId;

    /**
     * 是否需要合并
     */
    private Boolean mergeDump;

    /**
     * 通过报告查看具体到行的源码的目录
     */
    private List<String> parent;

    /**
     * 本地保存的路径
     */
    private String localClassesPath;

    /**
     * 两个版本中间版本
     */
    private Set<String> middleCommitIdList;

    /**
     * 上一次发布的commitId
     */
    private String lastCommitId;

    /**
     * 历史报告
     */
    private ReportDto reportDto;

    /**
     * exec名字
     */
    private String execName;

    /**
     * 本地报告保存的路径
     */
    private String htmlPath;

    /**
     * 项目名
     */
    private String pName;

    /**
     * 差异类型
     */
    private DiffTypeEnum diffType;

    /**
     * 空间名称
     */
    private String envName;

    /**
     * 项目gitId
     */
    private Long gitProjectId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 合并文件名
     */
    private String outputFileName;

    /**
     * 发布泳道
     */
    private String flowLaneType;

    /**
     * 存储桶名
     */
    private String bucketName;

    /**
     * 文件名（全路径）
     */
    private String fileName;
}
