package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddMobileTestReportReq extends BasicInfoReq implements Serializable {

    @ZModelProperty(description = "实际测试开始时间", required = true, sample = "1711987200000")
    private Date actualTestStart;

    @ZModelProperty(description = "实际测试开始时间--上下午", required = true, sample = "上午")
    private String actualTestStartDay;

    @ZModelProperty(description = "实际测试结束时间", required = true, sample = "1711987200000")
    private Date actualTestEnd;

    @ZModelProperty(description = "实际测试结束时间-- 上下午", required = true, sample = "下午")
    private String actualTestEndDay;

    @ZModelProperty(description = "模块测试结果", required = false, sample = "[]")
    private List<TmModuleTestVO> moduleTestVOS;
}
