package com.zto.devops.qc.client.service.plan.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AssociatedTestPlanListReq implements Serializable {

    @ZModelProperty(description = "计划名称", required = false, sample = "TP240305003075")
    private String planName;

    @ZModelProperty(description = "产品Code", required = false, sample = "399")
    private String productCode;

}
