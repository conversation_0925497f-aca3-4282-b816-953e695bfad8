package com.zto.devops.qc.client.service.testmanager.email.model;

import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DetailEmailReq implements Serializable {
    private static final long serialVersionUID = 7813677959687450553L;

    @ZModelProperty(description = "邮件记录code", required = false, sample = "SNF780011266079981568")
    private String emailCode;

    @ZModelProperty(description = "计划/报告code", required = false, sample = "TP240305003075")
    private String businessCode;

    @ZModelProperty(description = "关联计划code", required = false, sample = "TP240305003075")
    private String relatePlanCode;

    @ZModelProperty(description = "邮件类型", required = false, sample = "TEST_PLAN")
    private EmailTypeEnum emailType;

}
