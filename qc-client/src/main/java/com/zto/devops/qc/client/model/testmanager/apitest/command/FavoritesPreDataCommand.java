package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FavoritesPreDataCommand  extends BaseCommand {


    private String productCode;

    private String type;

    private Boolean favoritesStatus;

    public FavoritesPreDataCommand(String aggregateId) {
        super(aggregateId);
    }
}
