package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@GatewayModel(description = "api文档(精简版)VO")
@Data
public class ApiLiteInfoVO implements Serializable {
    private static final long serialVersionUID = -564152078667096321L;

    @GatewayModelProperty(description = "接口编码")
    private String apiCode;

    @GatewayModelProperty(description = "接口名称")
    private String apiName;

    @GatewayModelProperty(description = "接口地址")
    private String apiAddress;

}
