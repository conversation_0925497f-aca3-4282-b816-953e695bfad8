package com.zto.devops.qc.client.model.testmanager.coverage.query;

import java.io.Serializable;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022/10/25 20:19
 */
@Data
public class ReportMethodDto implements Serializable {

    private static final long serialVersionUID = 1L;


    private String name;

    private String desc;

    private Integer startLine;

    private List<ReportLineDto> reportLineDtoList;

    private List<ReportCoveredDto> reportCoveredDtoList;

    public String key() {
        String safeName = name != null ? name : "";
        String safeDesc = desc != null ? desc : "";
        return safeName + safeDesc;
    }
}
