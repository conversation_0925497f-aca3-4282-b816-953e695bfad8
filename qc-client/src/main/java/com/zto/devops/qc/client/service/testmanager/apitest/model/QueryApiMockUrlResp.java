package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "获取接口mock地址响应模型")
public class QueryApiMockUrlResp implements Serializable {

    @ZsmpModelProperty(description = "http/https")
    private String mockProtocol;

    @ZsmpModelProperty(description = "域名/ip")
    private String mockServerNameOrIp;

    @ZsmpModelProperty(description = "请求路径")
    private String mockPathUrl;

    @ZsmpModelProperty(description = "端口号")
    private String mockPortNumber;
}