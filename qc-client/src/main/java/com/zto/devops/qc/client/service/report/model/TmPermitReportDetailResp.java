package com.zto.devops.qc.client.service.report.model;

import cn.hutool.core.date.DateUtil;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
public class TmPermitReportDetailResp extends BasicInfoResp {
    private static final long serialVersionUID = -7304566690985525995L;

    @ZModelProperty(description = "是否包含zui应用（true：包含；false：不包含）", required = true)
    private Boolean zuiFlag;

    @ZModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "报告类型", required = false)
    private ReportType reportType;

    @ZModelProperty(description = "报告类型描述", required = false)
    private String reportTypeDesc;

    @ZModelProperty(description = "功能用例模块测试结果", required = true)
    private List<TmModuleTestVO> moduleTestVOS;

    @ZModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    @ZModelProperty(description = "计划准出时间-上下午", required = false)
    private String approvalExitDay;

    @ZModelProperty(description = "计划准出时间-上下午", required = false)
    private TestPlanDatePartitionEnum permitDatePartition;

    @ZModelProperty(description = "实际准出时间", required = false)
    private Date actualApprovalExitDate;

    @ZModelProperty(description = "实际准出时间-上下午", required = false)
    private String actualApprovalExitDay;

    @ZModelProperty(description = "安全计划code", required = false)
    private String safePlanCode;


    public String getApprovalExitDay() {
        return Objects.isNull(this.permitDatePartition) ? "" : TestPlanDatePartitionEnum.PM.equals(this.permitDatePartition) ? "18:00:00" : "09:00:00";
    }

    public String getActualApprovalExitDay() {
        return Objects.isNull(this.actualApprovalExitDate) ? null
                : DateUtil.isPM(this.actualApprovalExitDate) ? "18:00:00" : "09:00:00";
    }

    public String getReportTypeDesc() {
        return Objects.isNull(this.reportType) ? "" : this.reportType.getValue();
    }
}
