package com.zto.devops.qc.client.service.issue.model;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
public class IssueLegacyListResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "编号", required = false)
    private String code;

    @GatewayModelProperty(description = "缺陷编号", required = false)
    private String issueCode;

    public String getIssueCode() {
        this.issueCode = StringUtils.isNotBlank(this.code) ? this.code : "";
        return this.issueCode;
    }

    @GatewayModelProperty(description = "缺陷名称", required = false)
    private String title;

    @GatewayModelProperty(description = "缺陷状态", required = false)
    private IssueStatus status;

    @GatewayModelProperty(description = "缺陷状态说明", required = false)
    private String statusDesc;

    public String getStatusDesc() {
        this.statusDesc = Objects.nonNull(this.getStatus()) ? this.getStatus().getValue() : "";
        return this.statusDesc;
    }

    @GatewayModelProperty(description = "当前处理人ID", required = false)
    private Long handleUserId;

    @GatewayModelProperty(description = "当前处理人名称", required = false)
    private String handleUserName;

    @GatewayModelProperty(description = "优先级", required = false)
    private IssuePriority priority;

    @GatewayModelProperty(description = "优先级说明", required = false)
    private String priorityDesc;

    public String getPriorityDesc() {
        return Objects.nonNull(this.getPriority()) ? this.getPriority().getValue() : null;
    }

    @GatewayModelProperty(description = "开发人员ID", required = false)
    private Long developUserId;

    @GatewayModelProperty(description = "开发人员名称", required = false)
    private String developUserName;

    @GatewayModelProperty(description = "报告人ID", required = false)
    private Long findUserId;

    @GatewayModelProperty(description = "报告人", required = false)
    private String findUserName;

    @GatewayModelProperty(description = "发现版本编码", required = false)
    private String findVersionCode;

    @GatewayModelProperty(description = "发现版本", required = false)
    private String findVersionName;

}
