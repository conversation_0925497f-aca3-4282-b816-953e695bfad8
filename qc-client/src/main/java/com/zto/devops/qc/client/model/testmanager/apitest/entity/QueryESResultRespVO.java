package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class QueryESResultRespVO implements Serializable {
    private static final long serialVersionUID = -172793857102934899L;

    private boolean status;
    private String message;
    private QueryESResultVO result;

    public List<ListESQueryVO> buildResult() {
        if (this.status && null != result && CollectionUtil.isNotEmpty(result.getRecords())) {
            return result.getRecords();
        }
        return new ArrayList<>();
    }
}
