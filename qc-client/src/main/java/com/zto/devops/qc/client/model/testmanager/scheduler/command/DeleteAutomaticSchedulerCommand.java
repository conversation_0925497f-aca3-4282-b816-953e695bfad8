package com.zto.devops.qc.client.model.testmanager.scheduler.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DeleteAutomaticSchedulerCommand extends BaseCommand {

    public DeleteAutomaticSchedulerCommand(String aggregateId) {
        super(aggregateId);
    }
}
