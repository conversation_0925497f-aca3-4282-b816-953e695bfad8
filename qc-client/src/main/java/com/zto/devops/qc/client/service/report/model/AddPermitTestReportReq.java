package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddPermitTestReportReq extends BasicInfoReq implements Serializable {

    @ZModelProperty(description = "zui测试结果", required = false, sample = "PASS")
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "计划准出时间", required = true, sample = "1711987200000")
    private Date approvalExitDate;

    @ZModelProperty(description = "计划准出时间-上下午", required = true, sample = "下午")
    private String approvalExitDay;

    @ZModelProperty(description = "实际准出时间", required = false, sample = "1711987200000")
    private Date actualApprovalExitDate;

    @ZModelProperty(description = "实际准出时间-上下午", required = false, sample = "下午")
    private String actualApprovalExitDay;

    @ZModelProperty(description = "准出延期天数", required = false, sample = "1")
    private Integer delayDays;

    @ZModelProperty(description = "测试信息", required = true, sample = "{}")
    private CaseExecuteResultVO caseExecuteResultVO;

    @ZModelProperty(description = "安全测试人ID", required = true, sample = "5984549")
    private Long securityUserId;

    @ZModelProperty(description = "安全测试人名称", required = true, sample = "luban")
    private String securityUserName;

    @ZModelProperty(description = "安全测试结果code", required = false, sample = "SNF987293442717515776")
    private String securityTestResultCode;

    @ZModelProperty(description = "安全测试结果描述", required = false, sample = "安全测试结果描述")
    private String securityTestResultDesc;

    @ZModelProperty(description = "安全测试结果描述", required = true, sample = "PASS")
    private SecurityTestResult securityTestResult;

    @ZModelProperty(description = "功能用例模块测试结果", required = false, sample = "[]")
    private List<TmModuleTestVO> moduleTestVOS;

    @ZModelProperty(description = "覆盖率结果", required = false, sample = "SUBSTANDARD")
    private CodeCoverResult codeCoverResult;

    @ZModelProperty(description = "代码覆盖率不达标原因", required = false, sample = "[]")
    private List<CoverageReasonVO> coverageReasonVOS;

    @ZModelProperty(description = "ui测试结果", required = false, sample = "NO_PASS")
    private UiTestResultEnum uiTestResult;
}
