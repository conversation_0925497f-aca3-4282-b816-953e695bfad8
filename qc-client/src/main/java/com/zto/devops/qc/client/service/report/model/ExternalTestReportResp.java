package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.rpc.ProductTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ExternalTestReportResp extends BasicInfoResp {

    @ZModelProperty(description = "是否包含zui应用（true：包含；false：不包含）", required = true)
    private Boolean zuiFlag;

    @ZModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "报告类型", required = false)
    private ReportType reportType;

    @ZModelProperty(description = "报告类型描述", required = false)
    private String reportTypeDesc;

    @ZModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;

    @ZModelProperty(description = "计划提测时间上午/下午", required = false)
    private String presentationDay;

    @ZModelProperty(description = "计划上线时间", required = false)
    private Date publishDate;

    @ZModelProperty(description = "计划上线时间--上下午", required = false)
    private String publishDay;

    @ZModelProperty(description = "产品类型", required = false)
    private ProductTypeEnum productType;

    @ZModelProperty(description = "验收开始时间", required = false)
    private Date checkStartDate;

    @ZModelProperty(description = "验收结束时间", required = false)
    private Date checkEndDate;

    @ZModelProperty(description = "实际上线时间", required = false)
    private Date actualPublishDate;

    @ZModelProperty(description = "版本开始时间", required = false)
    private Date startDate;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false)
    private Integer delay;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false)
    private String delayDesc;

    @ZModelProperty(description = "产品类型", required = false)
    private String productSource;

    @ZModelProperty(description = "产品类型", required = false)
    private String productSourceDesc;

    @ZModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    @ZModelProperty(description = "安全计划code", required = false)
    private String safePlanCode;
}
