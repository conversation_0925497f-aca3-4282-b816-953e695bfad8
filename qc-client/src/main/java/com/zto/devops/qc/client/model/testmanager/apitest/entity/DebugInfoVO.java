package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.command.SaveDebugInfoCommand;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
public class DebugInfoVO implements Serializable {
    private static final long serialVersionUID = 9064389866663434120L;
    /**
     * 本次任务最终状态
     */
    private String status;

    /**
     * 调试日志
     */
    private String debugLog;

    /**
     * 调试节点信息
     */
    private List<DebugNodeInfoVO> linkBaseInfo;

    public static DebugInfoVO buildNodeInfo(SaveDebugInfoCommand command, List<String> logList, List<DebugNodeInfoVO> linkBaseInfo) {
        boolean nullFlag = true;
        DebugInfoVO result = new DebugInfoVO();
        if (StringUtils.isNotBlank(command.getStatus())) {
            result.setStatus(command.getStatus());
            nullFlag = false;
        }
        if (CollectionUtils.isNotEmpty(logList)) {
            result.setDebugLog(buildLog(command, logList));
            nullFlag = false;
        }
        if (CollectionUtils.isNotEmpty(linkBaseInfo)) {
            result.setLinkBaseInfo(linkBaseInfo);
            nullFlag = false;
        }
        return nullFlag ? null : result;
    }

    public static String buildLog(SaveDebugInfoCommand command, List<String> logList) {
        if (logList.get(0).equals("以下为调试历史： ")) {
            return String.join("\n", logList);
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss,SSS");
        String debugTime = format.format(Objects.nonNull(command.getGmtCreate()) ? command.getGmtCreate() : new Date());
        String debugUser = Objects.nonNull(command.getTransactor()) ? command.getTransactor().getUserName() : command.getCreator();
        String debugType = DebugTypeEnum.NODE.name().equals(command.getDebugType()) ? " （单点调试）"
                : DebugTypeEnum.FIRSTLY.name().equals(command.getDebugType()) ? " （链路调试）" : Strings.EMPTY;
        String executeInfo = debugTime + ": " + debugUser + " 调试结束 " + debugType;
        logList.add(0, "以下为调试历史： ");
        logList.add(1, executeInfo);
        return String.join("\n", logList);
    }

}
