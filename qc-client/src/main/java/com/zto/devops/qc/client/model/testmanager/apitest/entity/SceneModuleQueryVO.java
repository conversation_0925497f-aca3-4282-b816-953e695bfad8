package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class  SceneModuleQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    private String code;

    private String name;

    private String desc;

    private Integer sceneCount;

    private SceneIndexTypeEnum type;

    private String parentCode;

    private Date gmtCreate;

    private Date gmtModified;

    private Boolean enable;

    private Integer sceneEnable;

    private SceneInfoEnableEnum sceneEnableDesc;

    private List<SceneModuleQueryVO> children;

    private Boolean isLeaf;

    private Boolean isDisplay;

    private List<Button> permissionList;

    private Boolean shareStatus;

    public SceneInfoEnableEnum getSceneEnableDesc() {
        if(null != this.sceneEnable) {
            return SceneInfoEnableEnum.codeOf(this.sceneEnable);
        }
        return null;
    }

}
