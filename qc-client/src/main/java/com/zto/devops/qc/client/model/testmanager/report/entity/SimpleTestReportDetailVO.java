package com.zto.devops.qc.client.model.testmanager.report.entity;

import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SimpleTestReportDetailVO extends BaseReportInfoVO {

    @GatewayModelProperty(description = "测试策略", required = false)
    private String testStrategy;

    @GatewayModelProperty(description = "是否包含zui应用（true：包含；false：不包含）")
    private Boolean zuiFlag;

    @GatewayModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    public ZUITestResultEnum getZuiTestResult() {
        if (null == this.zuiFlag || !zuiFlag) {
            return null;
        }
        return (null == this.zuiTestResult || ZUITestResultEnum.UNKNOWN.equals(this.zuiTestResult))
                ? ZUITestResultEnum.PASS
                : this.zuiTestResult;
    }

    @GatewayModelProperty(description = "实际上线时间", required = false)
    private Date actualOnlineDate;

    @GatewayModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @GatewayModelProperty(description = "是否延期", required = false)
    private Integer delay;

    @GatewayModelProperty(description = "测试信息", required = false)
    private CaseExecuteResultVO caseExecuteResultVO;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    @GatewayModelProperty(description = "延期验收期间发版记录", required = false)
    private List<PublishVersionVO> publishRecordList;
}
