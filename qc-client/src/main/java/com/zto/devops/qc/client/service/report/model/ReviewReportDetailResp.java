package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoUserHandleVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewRenewalVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReviewReportDetailResp extends BasicInfoResp {

    @ZModelProperty(description = "报告类型", required = false)
    private ReportType reportType;

    @ZModelProperty(description = "报告类型描述", required = false)
    private String reportTypeDesc;

    @ZModelProperty(description = "评审信息", required = false)
    private ReviewInfoUserHandleVO reviewInfo;

    @ZModelProperty(description = "评审观点", required = false)
    private List<ReviewOpinionVO> reviewOpinions;

    @ZModelProperty(description = "评审更新信息", required = false)
    private List<ReviewRenewalVO> reviewRenewals;

    @ZModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

}
