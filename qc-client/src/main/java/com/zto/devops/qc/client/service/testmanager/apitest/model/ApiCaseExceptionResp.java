package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiCaseExceptionVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZsmpModel(description = "查询异常用例响应模型")
public class ApiCaseExceptionResp implements Serializable {
    private static final long serialVersionUID = 4107540346196514172L;

    @ZModelProperty(description = "生成中：true/false")
    private boolean processing;

    @ZModelProperty(description = "用例异常数据")
    private List<ApiCaseExceptionVO> caseExceptionData;

    @ZModelProperty(description = "总数", hidden = true)
    private Long total;

    public static ApiCaseExceptionResp init(boolean processing) {
        return ApiCaseExceptionResp.builder().processing(processing).caseExceptionData(new ArrayList<>()).total(0L).build();
    }

    public static ApiCaseExceptionResp buildSelf(List<ApiCaseExceptionVO> list, long total) {
        return ApiCaseExceptionResp.builder().processing(false).caseExceptionData(list).total(total).build();
    }
}
