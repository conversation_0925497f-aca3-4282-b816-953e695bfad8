package com.zto.devops.qc.client.model.testmanager.apitest.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTagEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
public class PageApiTestCaseChildrenQuery extends PageQueryBase {
    private static final long serialVersionUID = 5742844806999825950L;

    /**
     * 产品code
     */
    private String productCode;

    /**
     * 父用例code
     */
    private String parentCaseCode;

    /**
     * 用例类别（多选）
     */
    private List<ApiCaseTypeEnum> typeList;

    public List<ApiCaseTypeEnum> getTypeList() {
        if (CollectionUtil.isEmpty(this.typeList)) {
            this.typeList = Arrays.asList(ApiCaseTypeEnum.EXCEPTION_CASE, ApiCaseTypeEnum.NORMAL_CASE);
        }
        return this.typeList;
    }

    private List<Integer> typeNumList;

    public List<Integer> getTypeNumList() {
        return CollectionUtil.isNotEmpty(this.getTypeList())
                ? this.getTypeList().stream().map(item -> (item.getCode())).collect(Collectors.toList()) : null;
    }

    /**
     * 最近一次执行结果（多选）
     */
    private List<TestPlanCaseStatusEnum> testResultList;

    /**
     * 用例名(模糊搜索)
     */
    private String caseName;

    /**
     * 查询开始时间(最近一次执行)
     */
    private Date startTime;

    /**
     * 查询结束时间(最近一次执行)
     */
    private Date endTime;

    /**
     * 文档版本
     */
    private List<String> docVersionList;

    /**
     * 标识
     */
    private List<ApiCaseTagEnum> tagList;
}
