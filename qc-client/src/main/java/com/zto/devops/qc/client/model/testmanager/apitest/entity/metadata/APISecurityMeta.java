package com.zto.devops.qc.client.model.testmanager.apitest.entity.metadata;

import lombok.Data;

import java.io.Serializable;

@Data
public class APISecurityMeta implements Serializable {
    private RequestMeta request;
    private ResponseMeta response;
    private AuthMeta auth;
    private ApiInvokeIpLimit apiInvokeIpLimit;
    private Boolean notOpenapi;
    private boolean canMock;
    private APIIamMeta iam;
    private Boolean needZopAuth;
    private Boolean zsm;

    @Data
    public static class ApiInvokeIpLimit implements Serializable {
        private String whiteIpList;
        private String blackIpList;
    }

    @Data
    public static class AuthMeta implements Serializable {
        private boolean needAuth;
        private boolean needOauth2;
        private boolean oauth2NotCache;
        private boolean needJwt;
        private boolean needOpenid;
        private boolean enableAuth;
        private String authorities;
        private String jwtPublicKey;
        private String[] resourceScopes;
        private String ownerScope;
    }

    @Data
    public static class ResponseMeta implements Serializable {
    }

    @Data
    public static class RequestMeta implements Serializable {
        private boolean needSignature;
        private boolean needFileUpload;
        private TimestampMeta timestamp;
        private String signatureKey;
        private String fileSuffix;
    }

    @Data
    public static class TimestampMeta implements Serializable {
        private boolean necessary;
        private int threshold;
    }

    @Data
    public static class APIIamMeta implements Serializable {
        private Boolean enabled;
        private Boolean cache;
    }
}
