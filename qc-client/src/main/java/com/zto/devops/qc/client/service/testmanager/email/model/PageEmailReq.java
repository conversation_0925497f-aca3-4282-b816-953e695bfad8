package com.zto.devops.qc.client.service.testmanager.email.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.email.EmailSourceEnum;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PageEmailReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = -4990444623650020659L;

    @ZModelProperty(description = "邮件类型", required = false, sample = "[]")
    private List<String> typeList;

    @ZModelProperty(description = "发件人id（ssoUserId）", required = false, sample = "[5878415]")
    private List<Long> sendUserIdList;

    @ZModelProperty(description = "版本code", required = false, sample = "['VER2302168133']")
    private List<String> versionCodeList;

    @ZModelProperty(description = "关联计划code", required = false, sample = "['TP240305003075']")
    private List<String> planCodeList;

    @ZModelProperty(description = "发送开始时间", required = false, sample = "1711987200000")
    private Date sendTimeStart;

    @ZModelProperty(description = "发送结束时间", required = false, sample = "1711987200000")
    private Date sendTimeEnd;

    @ZModelProperty(description = "邮件名或者报告/计划code", required = false, sample = "111")
    private String nameOrCode;

    @ZModelProperty(description = "邮件数据源枚举", required = false, sample = "TEST_PLAN")
    private EmailSourceEnum emailSourceEnum;

    @ZModelProperty(description = "是否只查个人发送（true：是；false：否）", required = true, sample = "0")
    private Boolean isPersonal;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

}
