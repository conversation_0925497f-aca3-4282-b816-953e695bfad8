package com.zto.devops.qc.client.service.issue.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindIssueByCodeReq implements Serializable {
    private static final long serialVersionUID = -4383245870756945786L;

    @ZModelProperty(description = "编号", required = true, sample = "ISS230303008063")
    private String code;

    @ZModelProperty(description = "来源 true 为缺陷  false为审查", required = false, sample = "true")
    private Boolean issue;

}
