package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "移动造数分组请求模型")
public class MovePreDataModuleReq implements Serializable {

    @ZsmpModelProperty(description = "产品code", required = true)
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZsmpModelProperty(description = "分组或场景code", required = true)
    private String code;

    @ZsmpModelProperty(description = "父分组code", required = true)
    private String parentCode;
}
