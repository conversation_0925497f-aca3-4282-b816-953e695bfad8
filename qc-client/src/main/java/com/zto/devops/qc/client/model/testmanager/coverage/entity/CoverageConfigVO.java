package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @create 2022/10/8 9:33
 */
@Data
public class CoverageConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String zkePodIpUrl;

    private String zkeApikey;

    /**
     * 代码覆盖率不达标原因
     */
    private String codeCoverageReason;

    /**
     * 取最新master分支commitId产品
     */
    private String products;

    /**
     * 多模块产品
     */
    private String modules;

    /**
     * 排除的应用
     */
    private String excludeApps;

    /**
     * 过滤不需要比较的产品
     */
    private String filterUnComparedProducts;

    /**
     * 覆盖率报告灰度产品
     */
    private String newReportUrlProducts;

    /**
     * 覆盖率报告地址
     */
    private String reportUrl;
    
    /**
     * 文档跳转地址
     */
    private String docAddress;
}
