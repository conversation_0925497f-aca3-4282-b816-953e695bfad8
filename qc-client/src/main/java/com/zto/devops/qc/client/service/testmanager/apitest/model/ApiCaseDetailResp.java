package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTagEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@ZModel(description = "接口用例详情响应模型")
public class ApiCaseDetailResp implements Serializable {
    private static final long serialVersionUID = 3777393018073252441L;

    @ZModelProperty(description = "用例code")
    private String caseCode;

    @ZModelProperty(description = "用例名称")
    private String caseName;

    @ZModelProperty(description = "产品code")
    private String productCode;

    @ZModelProperty(description = "接口code")
    private String apiCode;

    @ZModelProperty(description = "用例状态")
    private ApiCaseStatusEnum status;

    @ZModelProperty(description = "用例类型")
    private ApiCaseTypeEnum caseType;

    @ZModelProperty(description = "用例类型")
    private String caseTypeDesc;

    @ZModelProperty(description = "用例请求数据")
    private JSONObject caseReqData;

    @ZModelProperty(description = "是否接口变更")
    private Boolean apiUpdated;

    public Boolean getApiUpdated() {
        return StringUtils.isNotBlank(this.tagValue) && this.tagValue.contains(ApiCaseTagEnum.API_UPDATE.name());
    }

    @ZModelProperty(description = "标签内容")
    private String tagValue;

    public String getCaseTypeDesc() {
        return null != caseType ? caseType.getDesc() : caseTypeDesc;
    }

    public void setCaseReqData(String caseReqData) {
        if (null != caseReqData) {
            this.caseReqData = JSONObject.parseObject(caseReqData);
        }
    }

    public void setCaseType(Integer caseTypeCode) {
        if (null != caseTypeCode) {
            this.caseType = ApiCaseTypeEnum.codeOf(caseTypeCode);
        }
    }
}
