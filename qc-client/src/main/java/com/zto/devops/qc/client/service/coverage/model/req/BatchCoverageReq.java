package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.VerifyGenerateVO;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ZModel(description = "批量代码覆盖率报告生成接口入参")
public class BatchCoverageReq implements Serializable {
    private static final long serialVersionUID = 1870509636783316888L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", required = true, sample = "PRO2207128000")
    private String productCode;

    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "版本名称", required = true, sample = "V2.92.0 test")
    private String versionName;

    @ZModelProperty(description = "校验结果列表", required = true, sample = "")
    private List<VerifyGenerateVO> dataList;

    @ZModelProperty(description = "报告类型", required = true, sample = "BRANCH")
    private RecordTypeEnum recordType;

    @ZModelProperty(description = "差异类型", sample = "INCREMENT")
    private DiffTypeEnum diffType;

    /**
     * 符合生成条件的app
     *
     * @return
     */
    public List<String> getAppIdList() {
        if (CollectionUtils.isEmpty(this.dataList)) {
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        this.dataList.stream().filter(data -> (data.getStatus().equals(RecordStatusEnum.SUCCEED))).forEach(data -> {
            data.getReasonList().forEach(reason -> {
                resultList.addAll(reason.getAppIdList());
            });
        });
        return resultList;
    }

    /**
     * 当前批次所有appId
     *
     * @return
     */
    public List<String> getAllAppIdList() {
        if (CollectionUtils.isEmpty(this.dataList)) {
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        this.dataList.forEach(data -> {
            data.getReasonList().forEach(reason -> {
                resultList.addAll(reason.getAppIdList());
            });
        });
        return resultList;
    }

}
