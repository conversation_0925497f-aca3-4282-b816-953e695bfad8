package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ButtonVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.FieldVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@GatewayModel(description = "用例详情响应模型")
@Data
public class TestcaseResp implements Serializable {
    private static final long serialVersionUID = 4437305682900272746L;

    @GatewayModelProperty(description = "code")
    private String code;

    @GatewayModelProperty(description = "所属产品code")
    private String productCode;

    @GatewayModelProperty(description = "所属模块code")
    private String parentCode;

    @GatewayModelProperty(description = "所属模块名称")
    private List<String> parentFullName;

    @GatewayModelProperty(description = "名称")
    private String name;

    @GatewayModelProperty(description = "属性")
    private TestcaseAttributeEnum attribute;

    @GatewayModelProperty(description = "类型")
    private TestcaseTypeEnum type;

    @GatewayModelProperty(description = "类型描述")
    private String typeDesc;

    @GatewayModelProperty(description = "等级")
    private TestcasePriorityEnum priority;

    @GatewayModelProperty(description = "等级描述")
    private String priorityDesc;

    @GatewayModelProperty(description = "状态")
    private TestcaseStatusEnum status;

    @GatewayModelProperty(description = "状态描述")
    private String statusDesc;

    @GatewayModelProperty(description = "前置条件")
    private String precondition;

    @GatewayModelProperty(description = "责任人编码")
    private Long dutyUserId;

    @GatewayModelProperty(description = "责任人")
    private String dutyUser;

    @GatewayModelProperty(description = "备注")
    private String comment;

    @GatewayModelProperty(description = "排序")
    private Integer sort;

    @GatewayModelProperty(description = "自动化用例节点类型")
    private AutomaticNodeTypeEnum nodeType;

    @GatewayModelProperty(description = "创建人编码")
    private Long creatorId;

    @GatewayModelProperty(description = "创建人")
    private String creator;

    @GatewayModelProperty(description = "创建时间")
    private Date gmtCreate;

    @GatewayModelProperty(description = "更新人编码")
    private Long modifierId;

    @GatewayModelProperty(description = "更新人")
    private String modifier;

    @GatewayModelProperty(description = "更新时间")
    private Date gmtModified;

    @GatewayModelProperty(description = "测试步骤")
    private List<TestcaseStepVO> testSteps;

    @GatewayModelProperty(description = "标签")
    private List<TagVO> tags;

    @GatewayModelProperty(description = "可编辑字段")
    private List<FieldVO> fields;

    @GatewayModelProperty(description = "可操作按钮")
    private List<ButtonVO> buttons;

    @GatewayModelProperty(description = "是否心跳")
    private Boolean setHeart;

    @GatewayModelProperty(description = "登记库code")
    private String automaticSourceCode;

    @GatewayModelProperty(description = "dubbo接口名")
    private String interfaceName;

    @GatewayModelProperty(description = "自动化框架类型")
    private AutomaticRecordTypeEnum automaticSourceType;

    @GatewayModelProperty(description = "是否核心用例")
    private Boolean setCore;

    @GatewayModelProperty(description = "版本code")
    private String versionCode;

    @GatewayModelProperty(description = "版本名称")
    private String versionName;

    public void setPriority(TestcasePriorityEnum priority) {
        this.priority = priority;
        if (null != priority) {
            this.priorityDesc = priority.getValue();
        }
    }

    public void setStatus(TestcaseStatusEnum status) {
        this.status = status;
        if (null != status) {
            this.statusDesc = status.getDesc();
        }
    }

    public void setType(TestcaseTypeEnum type) {
        this.type = type;
        if (null != type) {
            this.typeDesc = type.getValue();
        }
    }
}
