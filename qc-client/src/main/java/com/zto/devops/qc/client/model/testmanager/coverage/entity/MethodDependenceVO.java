package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.zto.devops.framework.client.entity.BaseModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class MethodDependenceVO extends BaseModel implements Serializable {

    private Long id;

    private String productCode;

    private String productName;

    private String methodCode;

    private String appId;

    private String versionCode;

    private String versionName;

    private String commitId;

    private String entryMethodCode;

    private String parentMethodCode;

    private String fullClassName;

    private String methodName;

    private String methodCesc;

    private String methodType;

    private String methodAnnotation;

    private Integer methodLevel;

    private Integer methodSort;

}
