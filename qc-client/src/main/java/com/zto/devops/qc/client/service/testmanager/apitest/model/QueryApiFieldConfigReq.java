package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "查询接口字段配置请求模型")
public class QueryApiFieldConfigReq implements Serializable {

    @ZModelProperty(description = "接口code", required = true, sample = "SNF950030989046906880")
    private String apiCode;
}
