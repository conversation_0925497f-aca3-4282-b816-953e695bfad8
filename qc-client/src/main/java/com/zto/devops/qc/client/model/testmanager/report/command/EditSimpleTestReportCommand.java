package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditSimpleTestReportCommand extends SimpleTestReportCommand {

    @GatewayModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    public EditSimpleTestReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
