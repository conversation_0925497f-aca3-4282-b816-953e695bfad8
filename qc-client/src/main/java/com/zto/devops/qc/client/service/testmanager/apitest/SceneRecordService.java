package com.zto.devops.qc.client.service.testmanager.apitest;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;

public interface SceneRecordService {

    Result<String> executeSceneDataCenter(SceneDataCenterExecuteReq req);

    Result<SceneTaskResultResp> querySceneTaskResult(SceneTaskResultQueryReq req);

    Result<Void> saveUserVariable(SaveUserVariableReq req);

    Result<String> queryUserVariable(UserVariableQueryReq req);

}
