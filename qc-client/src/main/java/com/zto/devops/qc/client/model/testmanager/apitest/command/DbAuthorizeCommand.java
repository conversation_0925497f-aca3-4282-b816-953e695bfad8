package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeDbVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeProductVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DbAuthorizeCommand extends BaseCommand {

    private String productCode;

    private List<ApiTestAuthorizeProductVO> authorizeProductList;

    private List<ApiTestAuthorizeDbVO> authorizeDbList;

    public DbAuthorizeCommand(String aggregateId) {
        super(aggregateId);
    }
}
