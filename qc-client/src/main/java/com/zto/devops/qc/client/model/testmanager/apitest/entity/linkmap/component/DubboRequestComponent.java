package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;

@Data
public class DubboRequestComponent extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    String address;
    String interfaceName;
    String methodName;
    String group;
    String version;
    int retries=0;
    int timeout=3000;
    JSONArray args;//{"paramType","", "paramValue":""}
    JSONArray attachmentArgs;//{"key","", "value":""}
}
