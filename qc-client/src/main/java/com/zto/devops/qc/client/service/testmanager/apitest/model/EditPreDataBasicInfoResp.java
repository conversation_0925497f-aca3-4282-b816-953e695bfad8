package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ZsmpModel(description = "编辑场景响应模型")
public class EditPreDataBasicInfoResp implements Serializable {

    @ZsmpModelProperty(description = "场景code")
    private String sceneCode;

    @ZsmpModelProperty(description = "场景版本")
    private String sceneVersion;

    public static EditPreDataBasicInfoResp buildSelf(String sceneCode, String sceneVersion) {
        return EditPreDataBasicInfoResp.builder()
                .sceneCode(sceneCode)
                .sceneVersion(sceneVersion)
                .build();
    }
}
