package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryInterfaceCoverageRateReq implements Serializable {

    private static final long serialVersionUID = -8480136873561985038L;

    @ZModelProperty(description = "版本code", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "应用名", required = true, sample = "zto-qamp")
    private String appId;

}
