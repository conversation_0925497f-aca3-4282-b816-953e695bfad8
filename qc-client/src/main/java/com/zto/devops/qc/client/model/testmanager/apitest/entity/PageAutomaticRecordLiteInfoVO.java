
package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@GatewayModel(description = "分页查询登记库信息VO（精简版）")
@Data
public class PageAutomaticRecordLiteInfoVO implements Serializable {
    private static final long serialVersionUID = 3581464620675792144L;

    @GatewayModelProperty(description = "总数")
    private Long total;

    @GatewayModelProperty(description = "登记库数据")
    private List<AutomaticRecordLiteInfoVO> list;

    public static PageAutomaticRecordLiteInfoVO buildSelf(List<AutomaticRecordLiteInfoVO> doList, Long total) {
        PageAutomaticRecordLiteInfoVO result = new PageAutomaticRecordLiteInfoVO();
        result.setList(CollectionUtil.isEmpty(doList) ? new ArrayList<>() : doList);
        result.setTotal(CollectionUtil.isEmpty(doList) ? 0l : total);
        return result;
    }
}
