package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.service.tag.model.BatchAddTagReq;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@ZsmpModel(description = "用例管理添加标签请求模型")
public class BatchAddTestcaseTagReq extends BatchAddTagReq implements Serializable {

    @ZModelProperty(description = "领域 PRODUCT TESTCASE", required = true, sample = "PRODUCT")
    private DomainEnum domain;
}