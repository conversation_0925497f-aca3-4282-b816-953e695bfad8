package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneTagVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SceneInfoResp implements Serializable {

    @ZModelProperty(description = "id")
    private Long id;

    @ZModelProperty(description = "产品编号")
    private String productCode;

    @ZModelProperty(description = "场景编号")
    private String sceneCode;

    @ZModelProperty(description = "场景名称")
    private String sceneName;

    @ZModelProperty(description = "登记库编号")
    private String automaticSourceCode;

    @ZModelProperty(description = "场景版本")
    private String sceneVersion;

    @ZModelProperty(description = "场景信息描述")
    private String sceneInfoDesc;

    @ZModelProperty(description = "场景OSS上的存储路径")
    private String sceneOssPath;

    @ZModelProperty(description = "场景OSS上的存储文件")
    private String sceneOssFile;

    @ZModelProperty(description = "阶段记录")
    private String stepRecord;

    @ZModelProperty(description = "场景发布状态")
    private SceneInfoStatusEnum status;
    @ZModelProperty(description = "场景状态")
    private SceneInfoEnableEnum enable;

    @ZModelProperty(description = "更新人")
    private String modifier;

    @ZModelProperty(description = "更新时间")
    private Date gmtModified;

    @ZModelProperty(description = "场景状态(中文)")
    private String enableDesc;

    @ZModelProperty(description = "是否修改")
    private Boolean isModified;

    @ZModelProperty(description = "发布异常原因")
    private String publishErrorMsg;

    @ZModelProperty(description = "更新人编号")
    private Long modifierId;

    @ZModelProperty(description = "前端数据")
    private String sceneFrontData;

    @ZModelProperty(description = "后端数据")
    private JSONObject sceneBackData;

    @ZModelProperty(description = "类型")
    private SceneIndexTypeEnum sceneIndexType;

    @ZModelProperty(description = "父分组code")
    private String parentCode;

    @ZModelProperty(description = "创建人")
    private String creator;

    @ZModelProperty(description = "tag类型")
    private List<SceneTagVO> tagVOList;

    @ZModelProperty(description = "tag")
    private List<String> tags;

    private List<Button> buttonList;

    private List<Button> collapseButtonList;

    private Boolean isCheck;

    @ZModelProperty(description = "共享状态")
    private Boolean shareStatus;

    @ZModelProperty(description = "是否展示导入按钮")
    private Boolean isUploadShow;

    private String scenePath;

    public String getEnableDesc() {
        if(SceneIndexTypeEnum.SCENE.equals(this.sceneIndexType)) {
            return SceneInfoEnableEnum.getDesc(this.enable.getCode());
        }
        return null;
    }
}
