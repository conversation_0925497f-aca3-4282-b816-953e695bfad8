package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class SceneModuleCopyCommand  extends BaseCommand implements Serializable {

    private static final long serialVersionUID = -1694254500711033036L;

    private String productCode;

    private String moduleCode;

    private String productName;

    private UseCaseFactoryTypeEnum sceneType;

    public SceneModuleCopyCommand(String aggregateId) {
        super(aggregateId);
    }
}
