package com.zto.devops.qc.client.service.testmanager.rpc;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestVariableVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.UserCookieMapVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskInfo;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.AmazonS3ConfigVO;
import com.zto.devops.qc.client.service.testmanager.rpc.model.UserCookieQueryReq;

public interface DocumentQcRpcService {

    Result<UserCookieMapVO> queryUserCookie(UserCookieQueryReq req);

    ApiTestVariableVO queryVariableByKey(String variableCode);

    Result<Void> debugWithoutNode(DebugTaskInfo debugTaskInfo, User user);

    AmazonS3ConfigVO getAmazonS3Config();

    JSONObject queryDbConfig(String dbId);
}
