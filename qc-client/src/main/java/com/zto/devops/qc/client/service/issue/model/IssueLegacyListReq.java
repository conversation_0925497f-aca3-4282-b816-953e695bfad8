package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class IssueLegacyListReq implements Serializable {
    @ZModelProperty(description = "业务code", required = true, sample = "ISS230303008063")
    private String businessCode;

    @ZModelProperty(description = "报告类型", required = true, sample = "TEST_ACCESS")
    private ReportType reportType;

    /**
     * 当前报告类型是否支持统计缺陷
     *
     * @return 是：支持；否：不支持
     */
    public boolean isValidReportType() {
        if (null == this.reportType) {
            return false;
        }
        List<ReportType> validReportTypeList = new ArrayList<>();
        validReportTypeList.add(ReportType.TEST_PERMIT);
        validReportTypeList.add(ReportType.CHECED_TEST);
        validReportTypeList.add(ReportType.SIMPLE_PROCESS);
        validReportTypeList.add(ReportType.SPECIAL_MOBILE);
        return validReportTypeList.contains(this.reportType);
    }
}
