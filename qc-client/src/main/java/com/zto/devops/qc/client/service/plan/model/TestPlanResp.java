package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TestPlanResp implements Serializable {
    private static final long serialVersionUID = 3905167881178353891L;

    @GatewayModelProperty(description = "编号", required = false)
    private String code;

    @GatewayModelProperty(description = "计划名", required = false)
    private String planName;

    @GatewayModelProperty(description = "产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "类型", required = false)
    private TestPlanNewTypeEnum type;

    @GatewayModelProperty(description = "状态", required = false)
    private TestPlanNewStatusEnum status;

    @GatewayModelProperty(description = "状态描述", required = false)
    private String statusDesc;

    @GatewayModelProperty(description = "类型 描述", required = false)
    private String typeDesc;

    @GatewayModelProperty(description = "计划编号", required = false)
    private String planCode;

    @GatewayModelProperty(description = "测试负责人id", required = false)
    private Long testDirectorId;

    @GatewayModelProperty(description = "测试负责人名", required = false)
    private String testDirectorName;

    @GatewayModelProperty(description = "计划负责人id", required = false)
    private Long planDirectorId;
    
    @GatewayModelProperty(description = "计划负责人姓名", required = false)
    private String planDirectorName;

    @GatewayModelProperty(description = "创建时间")
    private Date gmtCreate;

    @GatewayModelProperty(description = "修改时间")
    private Date gmtModified;
}
