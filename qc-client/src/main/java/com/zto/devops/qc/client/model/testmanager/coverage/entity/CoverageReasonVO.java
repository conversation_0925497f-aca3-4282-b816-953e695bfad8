package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Data
public class CoverageReasonVO implements Serializable {

    @ZModelProperty(description = "覆盖率", sample = "90")
    private BigDecimal recordRate;

    @ZModelProperty(description = "不达标原因（多选）", sample = "[未覆盖部分跟本次改动无关]")
    private List<String> reasonList;

    @ZModelProperty(description = "自定义原因", sample = "自定义原因")
    private String customReason;

    @ZModelProperty(description = "应用id", required = true, sample = "zto-qamp")
    private String appId;

    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "版本名称", required = true, sample = "V2.92.0 test")
    private String versionName;

    private String finalReason;

    public String getFinalReason() {
        if (StringUtil.isNotBlank(this.finalReason)) {
            return this.finalReason;
        }
        this.finalReason = Strings.EMPTY;
        AtomicInteger index = new AtomicInteger(1);
        if (CollectionUtil.isNotEmpty(this.reasonList)) {
            reasonList.forEach(reason -> {
                if (StringUtil.isNotBlank(reason)) {
                    this.finalReason += index + ". " + reason + "$";
                    index.getAndIncrement();
                }
            });
        }
        if (StringUtil.isNotEmpty(this.customReason)) {
            this.finalReason += index.get() + ". 自定义：" + customReason + "$";
        }
        return this.finalReason;
    }

}
