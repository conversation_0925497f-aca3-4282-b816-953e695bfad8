package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticChildTaskVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskGroupVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AutomaticTaskVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.CountCaseStatusVO;
import com.zto.titans.common.util.StringUtil;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ZsmpModel(description = "自动化任务执行记录响应模型(父任务分组)")
public class AutomaticTaskGroupResp implements Serializable {

    @ZsmpModelProperty(description = "定时任务code", required = true)
    private String schedulerCode;

    @ZsmpModelProperty(description = "定时任务name", required = true)
    private String schedulerName;

    @ZsmpModelProperty(description = "是否是父任务（true：是;false：否）")
    private Boolean parentFlag;

    @ZsmpModelProperty(description = "自动化任务id")
    private String taskId;

    @ZsmpModelProperty(description = "唯一标识（同 taskId）")
    private String code;

    public String getCode() {
        return StringUtil.isNotBlank(this.taskId) ? this.taskId : "";
    }

    @ZsmpModelProperty(description = "自动化任务结果")
    private AutomaticStatusEnum status;

    @ZsmpModelProperty(description = "自动化任务结果描述")
    private String statusDesc;

    @ZsmpModelProperty(description = "用例总数")
    private Integer totalCount;

    @ZsmpModelProperty(description = "用例执行结果VOS")
    private List<CountCaseStatusVO> caseStatusVOS;

    @ZsmpModelProperty(description = "版本code")
    private String versionCode;

    @ZsmpModelProperty(description = "版本名称")
    private String versionName;

    @ZsmpModelProperty(description = "测试计划编码")
    private String testPlanCode;

    @ZsmpModelProperty(description = "测试计划名称")
    private String testPlanName;

    @ZsmpModelProperty(description = "测试阶段")
    private TestPlanStageEnum testStage;

    @ZsmpModelProperty(description = "测试阶段描述")
    private String testStageDesc;

    @ZsmpModelProperty(description = "触发方式")
    private AutomaticTaskTrigModeEnum trigMode;

    @ZsmpModelProperty(description = "触发方式描述")
    private String trigModeDesc;

    @ZsmpModelProperty(description = "开始时间")
    private Date startTime;

    @ZsmpModelProperty(description = "结束时间")
    private Date finishTime;

    @ZsmpModelProperty(description = "执行人id")
    private Long executorId;

    @ZsmpModelProperty(description = "执行人")
    private String executor;

    @ZsmpModelProperty(description = "终止人id")
    private Long stopUserId;

    @ZsmpModelProperty(description = "终止人")
    private String stopUser;

    @ZsmpModelProperty(description = "是否自动终止")
    private Boolean autoStopFlag;

    @ZsmpModelProperty(description = "自动终止原因")
    private String autoStopReason;

    @ZsmpModelProperty(description = "执行空间")
    private String env;

    public Boolean getAutoStopFlag() {
        return StringUtil.isBlank(this.stopUser) ? null :
                (this.stopUser.equalsIgnoreCase("abort") ||
                        this.stopUser.equalsIgnoreCase("cookieFailAbort"));
    }

    public String getAutoStopReason() {
        if(null != getAutoStopFlag() && getAutoStopFlag()) {
            return this.stopUser.equalsIgnoreCase("abort") ?
                    "超过一小时未执行，系统自动终止" : "cookie获取失败，系统自动终止，请联系管理员";
        }
        return null;
    }

    @ZsmpModelProperty(description = "子任务")
    private List<AutomaticChildTaskVO> children;

    public String getStatusDesc() {
        this.statusDesc = (null != this.status) ?
                (this.status.equals(AutomaticStatusEnum.NOT_STARTED)
                        ? "排队中"
                        : this.status.getDesc())
                : "";
        return this.statusDesc;
    }

    public void setTestStage(TestPlanStageEnum testStage) {
        this.testStage = testStage;
        if (null != testStage) {
            this.testStageDesc = testStage.getValue();
        }
    }

    public void setTrigMode(AutomaticTaskTrigModeEnum trigMode) {
        this.trigMode = trigMode;
        if (null != trigMode) {
            this.trigModeDesc = trigMode.getDesc();
        }
    }

    /**
     * 组装参数
     *
     * @param list {@link AutomaticTaskVO}
     * @return
     */
    public static List<AutomaticTaskGroupResp> buildSelf(List<AutomaticTaskGroupVO> list) {
        List<AutomaticTaskGroupResp> respList = new ArrayList<>(list.size());
        list.forEach(vo -> {
            AutomaticTaskGroupResp resp = new AutomaticTaskGroupResp();
            BeanUtils.copyProperties(vo, resp);
            resp.setChildren(vo.getChildren());
            resp.setCaseStatusVOS(vo.getCaseStatusVOS());
            respList.add(resp);
        });
        return respList;
    }
}
