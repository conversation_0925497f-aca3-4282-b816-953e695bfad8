package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;

@Data
public class ESComponent extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    Integer id;
    String clusterName;//用户自定义集群名称
    Long clusterId;
    String requestType;
    String path;
    String script;
//    String sign;
    JSONArray variableExtraction;
    String proxyExecBodyJsonStr;
}
