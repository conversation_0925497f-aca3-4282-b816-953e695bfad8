package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "删除场景图请求模型")
@Data
public class DeleteSceneInfoReq implements Serializable {

    @ZsmpModelProperty(description = "场景图code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "产品code", required = false)
    private String productCode;

    @ZsmpModelProperty(description = "类型", required = false)
    private UseCaseFactoryTypeEnum sceneType;
}
