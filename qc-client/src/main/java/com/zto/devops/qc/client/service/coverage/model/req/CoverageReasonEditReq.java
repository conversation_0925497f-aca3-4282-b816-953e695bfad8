package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "修改不达标原因接口入参")
public class CoverageReasonEditReq implements Serializable {

    private static final long serialVersionUID = -7903067196368285461L;

    @ZModelProperty(description = "版本code", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "应用名", required = true, sample = "zto-qamp")
    private String appId;

    @ZModelProperty(description = "产品code", required = true, sample = "PRO2207128000")
    private String productCode;

    @ZModelProperty(description = "系统原因list", sample = "代码无实现类")
    private List<String> reasonList;

    @ZModelProperty(description = "自定义原因", sample = "测试自定义原因")
    private String customReason;

}
