package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTagEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ZModel(description = "分页查询接口测试用例Req")
@Data
@EqualsAndHashCode(callSuper = true)
public class PageApiTestCaseChildrenReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = -6111956124123270894L;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "父用例code", required = true, sample = "TC240202008544")
    private String parentCaseCode;

    @ZModelProperty(description = "用例类别（多选）", sample = "API_CASE")
    private List<ApiCaseTypeEnum> typeList;

    @ZModelProperty(description = "标识", sample = "API_CASE_MODIFY")
    private List<ApiCaseTagEnum> tagList;

    @ZModelProperty(description = "用例名(模糊搜索)", sample = "caseName")
    private String caseName;

    @ZModelProperty(description = "最近一次执行结果（多选）", sample = "SUCCESS")
    private List<TestPlanCaseStatusEnum> testResultList;

    @ZModelProperty(description = "查询开始时间(最近一次执行)", sample = "2024-01-01 00:00:00")
    private Date startTime;

    @ZModelProperty(description = "查询结束时间(最近一次执行)", sample = "2024-01-31 23:59:59")
    private Date endTime;

}
