package com.zto.devops.qc.client.model.testmanager.scheduler.entity;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Data
public class AutomaticSchedulerVO implements Serializable {
    private static final long serialVersionUID = -5845910011734512235L;

    @ZsmpModelProperty(description = "任务code")
    private String schedulerCode;

    @ZsmpModelProperty(description = "任务名")
    private String schedulerName;

    @ZsmpModelProperty(description = "是否开启定时任务")
    private Boolean switchFlag;

    @ZsmpModelProperty(description = "crontab表达式")
    private String crontab;

    @ZsmpModelProperty(description = "执行环境")
    private String executeEnv;

    @ZsmpModelProperty(description = "运行空间tag，用于场景用例执行")
    private String executeTag;

    @ZsmpModelProperty(description = "运行空间code")
    private String executeSpaceCode;

    @ZsmpModelProperty(description = "是否开启消息通知")
    private Boolean messageFlag;

    @ZsmpModelProperty(description = "执行结果描述")
    private String executeResultDesc;

    public String getExecuteResultDesc() {
        if (StringUtils.isNotBlank(this.executeResultName)) {
            this.executeResultDesc = AutomaticStatusEnum.getDescByName(executeResultName);
        }
        return this.executeResultDesc;
    }

    @ZsmpModelProperty(description = "执行结果code")
    private String executeResultName;

    @ZsmpModelProperty(description = "执行人")
    private String executor;

    @ZsmpModelProperty(description = "创建人")
    private String creator;

    @ZsmpModelProperty(description = "执行时间")
    private Date executeTime;

    @ZsmpModelProperty(description = "最近一次任务id")
    private String taskId;

    @ZsmpModelProperty(description = "用例数")
    private Long caseNum;

}
