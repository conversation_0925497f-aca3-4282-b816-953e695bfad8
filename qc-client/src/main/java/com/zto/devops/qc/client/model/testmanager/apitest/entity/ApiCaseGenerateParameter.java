package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.model.dto.ApiGlobalConfigurationEntityDO;
import lombok.Data;

import java.util.List;

@Data
public class ApiCaseGenerateParameter implements Serializable {

    private static final long serialVersionUID = 1L;


    private ApiTypeEnum apiType;

    private String apiData;

    private List<ApiFieldConfigVO> apiFieldConfigList;

    private List<ApiGlobalConfigurationEntityDO> apiGlobalConfigList;

    private boolean initialize;

    private boolean gatewaySource;

    private boolean apiGatewayRule;

    private List<ApiCaseExceptionVO> diffApiCaseList;
}
