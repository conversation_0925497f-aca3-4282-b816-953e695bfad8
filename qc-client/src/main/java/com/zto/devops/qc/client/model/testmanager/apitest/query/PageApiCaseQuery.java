package com.zto.devops.qc.client.model.testmanager.apitest.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseSourceTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class PageApiCaseQuery extends PageQueryBase {

    @GatewayModelProperty(description = "用例状态（多选）", required = false)
    private List<Integer> enableNumList;

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "接口名称（多选）", required = false)
    private List<String> apiNameList;

    @GatewayModelProperty(description = "接口地址（多选）", required = false)
    private List<String> apiAddressList;

    @GatewayModelProperty(description = "接口code", required = false)
    private String apiCode;

    @GatewayModelProperty(description = "用例类别（多选）", required = false)
    private List<ApiCaseSourceTypeEnum> typeList;
    private List<Integer> typeNumList;
    public List<Integer> getTypeNumList() {
        if (CollectionUtil.isEmpty(this.typeList)) {
            return Arrays.asList(1, 3, 4);
        }
        if (this.typeList.contains(ApiCaseSourceTypeEnum.NODE) && !this.typeList.contains(ApiCaseSourceTypeEnum.GENERAL)) {
            this.typeList.add(ApiCaseSourceTypeEnum.GENERAL);
        }
        return this.typeList.stream().map(ApiCaseSourceTypeEnum::getCode).collect(Collectors.toList());
    }

    @GatewayModelProperty(description = "最近一次执行结果（多选）", required = false)
    private List<TestPlanCaseStatusEnum> testResultList;

    @GatewayModelProperty(description = "用例名(模糊搜索)", required = false)
    private String caseName;
    public String getCaseName() {
        if (StringUtils.isNotBlank(this.caseName)) {
            return this.caseName
                    .replaceAll("/", "//")
                    .replaceAll("%", "/%")
                    .replaceAll("_", "/_");
        }
        return this.caseName;
    }

    @GatewayModelProperty(description = "查询开始时间(最近一次执行)", required = false)
    private Date startTime;

    @GatewayModelProperty(description = "查询结束时间(最近一次执行)", required = false)
    private Date endTime;

    @GatewayModelProperty(description = "文档版本", required = false)
    private List<String> docVersionList;
}
