package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.client.simple.HasTransactor;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SceneIndexVO implements Serializable {

    private static final long serialVersionUID = -5690903608233813282L;

    private Long id;

    private String sceneIndexCode;

    private String sceneIndexName;

    private String sceneInfoDesc;

    private String productCode;

    private String parentCode;

    private SceneIndexTypeEnum sceneIndexType;

    private Integer sceneType;

    private String testcaseCode;

    private Integer sceneEnable;

    private Boolean enable;

    private Long creatorId;

    private String creator;

    private Date gmtCreate;

    private Long modifierId;

    private String modifier;

    private Date gmtModified;

    private SceneInfoEnableEnum sceneInfoEnable;

    private String automaticSourceCode;

    private Boolean shareStatus;

    private Boolean isCollect;

    public void preUpdate(HasTransactor hasTransactor) {
        User user = hasTransactor.getTransactor();
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preUpdate(User user) {
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
            this.setGmtModified(new Date());
        }
    }

    public void preCreate(User user) {
        if (null != user) {
            this.setCreatorId(user.getUserId());
            this.setCreator(user.getUserName());
            this.setGmtCreate(new Date());
        }
    }
}
