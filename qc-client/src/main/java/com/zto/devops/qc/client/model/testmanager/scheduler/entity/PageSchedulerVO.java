package com.zto.devops.qc.client.model.testmanager.scheduler.entity;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageSchedulerVO implements Serializable {

    private static final long serialVersionUID = -2862754937783183617L;

    @ZsmpModelProperty(description = "定时任务集合")
    private List<AutomaticSchedulerVO> list;

    @ZsmpModelProperty(description = "总数")
    private Long total;
}
