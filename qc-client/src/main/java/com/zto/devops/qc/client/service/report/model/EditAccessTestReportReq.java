package com.zto.devops.qc.client.service.report.model;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class EditAccessTestReportReq extends SaveBasicInfoReq implements Serializable {

    private static final long serialVersionUID = 9090362652712304053L;

    @ZModelProperty(description = "总结、分析、描述", required = false, sample = "总结")
    private String summary;

    @ZModelProperty(description = "实际提测时间", required = false, sample = "1711382400000")
    private Date actualPresentationDate;

    @ZModelProperty(description = "实际提测时间--上午、下午", required = false, sample = "下午")
    private String actualPresentationDay;

}
