package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddAccessTestReportReq extends BasicInfoReq implements Serializable {

    private static final long serialVersionUID = 535690997901085123L;

    @ZModelProperty(description = "计划提测时间", required = false, sample = "1711987200000")
    private Date presentationDate;

    @ZModelProperty(description = "计划提测时间-上下午", required = false, sample = "上午")
    private String presentationDay;

    @ZModelProperty(description = "实际提测时间", required = false,sample = "1711987200000")
    private Date actualPresentationDate;

    @ZModelProperty(description = "实际提测时间-上下午", required = false, sample = "上午")
    private String actualPresentationDay;

    @ZModelProperty(description = "提测延期天数", required = false,  sample= "1")
    private Integer delayDays;

    @GatewayModelProperty(description = "用例信息", required = false,sample= "{}")
    private CaseExecuteResultVO caseExecuteResultVO;
}
