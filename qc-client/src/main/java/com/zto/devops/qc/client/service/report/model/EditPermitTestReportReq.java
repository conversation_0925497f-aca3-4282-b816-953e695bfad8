package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class EditPermitTestReportReq extends SaveBasicInfoReq implements Serializable {

    private static final long serialVersionUID = -3851253050301340142L;

    @ZModelProperty(description = "总结、分析、描述", required = false, sample = "总结")
    private String summary;

    @ZModelProperty(description = "功能用例模块测试结果", required = true, sample = "[]")
    private List<TmModuleTestVO> moduleTestVOS;

    @ZModelProperty(description = "总体测试结果", required = false, sample = "PASS")
    private TmTestResultEnum testResult;

    @ZModelProperty(description = "实际准出时间", required = false, sample = "1711987200000")
    private Date actualApprovalExitDate;

    @ZModelProperty(description = "实际准出时间-上下午", required = false, sample = "上午")
    private String actualApprovalExitDay;

    @ZModelProperty(description = "zui测试结果", required = false, sample = "PASS")
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "ui测试结果", required = false, sample = "PASS")
    private UiTestResultEnum uiTestResult;

}
