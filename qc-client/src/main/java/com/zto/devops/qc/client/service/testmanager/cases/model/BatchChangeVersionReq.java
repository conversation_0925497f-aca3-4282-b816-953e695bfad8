
package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class BatchChangeVersionReq implements Serializable {
    private static final long serialVersionUID = 964868675651620062L;

    @ZModelProperty(description = "用例code集合", required = true, sample = "['TC231130064892']")
    private List<String> codeList;

    @ZModelProperty(description = "目标版本code，[未分组：NONE_VERSION]",required = true, sample = "NONE_VERSION")
    private String targetVersionCode;

    @ZModelProperty(description = "目标模块编码，[未分组：NO_GROUP]", required = false, sample = "NO_GROUP")
    private String targetParentCode;

    @ZModelProperty(description = "是否复用原来分组", required = false, sample = "0")
    private Boolean useOriginalGroup;

    @ZModelProperty(description = "是否删除原来分组", required = false, sample = "0")
    private Boolean deleteOriginalGroup;

}
