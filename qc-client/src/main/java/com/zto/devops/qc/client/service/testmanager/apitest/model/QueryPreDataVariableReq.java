package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryPreDataVariableReq implements Serializable {

    @GatewayModelProperty(description = "所属产品编码")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @GatewayModelProperty(description = "业务编码")
    private String linkCode;

    @GatewayModelProperty(description = "变量类型")
    private VariableTypeEnum type;

    @GatewayModelProperty(description = "变量子类型")
    private Integer subVariableType;

}
