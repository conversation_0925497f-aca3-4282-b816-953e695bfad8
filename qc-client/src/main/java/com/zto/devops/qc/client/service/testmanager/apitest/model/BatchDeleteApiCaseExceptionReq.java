package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "批量删除异常用例请求模型")
public class BatchDeleteApiCaseExceptionReq implements Serializable {

    @ZModelProperty(description = "用例code列表", required = true)
    private List<String> caseCodeList;
}
