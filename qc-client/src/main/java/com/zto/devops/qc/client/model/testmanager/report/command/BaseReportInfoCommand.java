package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BaseReportInfoCommand extends BaseCommand {

    @GatewayModelProperty(description = "编号", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "报告名称", required = true)
    private String reportName;

    @GatewayModelProperty(description = "计划编号", required = true)
    private String planCode;

    @GatewayModelProperty(description = "计划名称", required = false)
    private String planName;

    @GatewayModelProperty(description = "版本code", required = true)
    private String versionCode;

    @GatewayModelProperty(description = "版本名称", required = true)
    private String versionName;

    @GatewayModelProperty(description = "所属产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "所属产品名称", required = false)
    private String productName;

    @GatewayModelProperty(description = "报告类型", required = true)
    private ReportType reportType;

    @GatewayModelProperty(description = "报告状态： 草稿；已发送", required = false)
    private TestPlanStatusEnum status;

   @GatewayModelProperty(description = "邮件预览", required = false)
    private String  preview;

    @GatewayModelProperty(description = "总体测试结果", required = false)
    private TmTestResultEnum testResult;

    @GatewayModelProperty(description = "ui测试结果", required = false)
    private UiTestResultEnum uiTestResult;

    public BaseReportInfoCommand(String aggregateId) {
        super(aggregateId);
    }
}
