package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@GatewayModel(description = "查询登记库列表Req")
@Data
public class PageAutomaticRecordLiteInfoReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 6258445312981363065L;

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "登记库名称或code（模糊搜索）", required = false)
    private String nameOrCode;
}
