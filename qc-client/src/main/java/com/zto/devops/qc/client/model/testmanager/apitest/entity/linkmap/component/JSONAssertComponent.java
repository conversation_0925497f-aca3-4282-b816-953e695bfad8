package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;

@Data
public class JSONAssertComponent extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    String jsonPath;
    String expectedValue="";
    boolean jsonValidation=true;
    boolean ifRegex=true;
    boolean expectNull=false;
    boolean invert=false;

}
