package com.zto.devops.qc.client.service.issue.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 版本关联迭代
 * @Date 2023/4/13
 * @Version 1.0
 */
@Data
public class VersionPlannedReq implements Serializable {

    private String versionCode;

    private String versionName;

    private Boolean confirm = false;

    private List<String> addedIssues;

    private List<String> removedIssues;
}
