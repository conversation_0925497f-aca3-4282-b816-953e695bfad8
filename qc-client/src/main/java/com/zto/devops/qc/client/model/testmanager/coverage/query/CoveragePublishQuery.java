package com.zto.devops.qc.client.model.testmanager.coverage.query;

import java.io.Serializable;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022/9/20 16:40
 */
@Data
public class CoveragePublishQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    private List<String> versionCodes;

    private String branchName;

    private Long creatorId;

    private String creator;

    private List<String> appIds;

    private String envName;

    private String productCode;

}
