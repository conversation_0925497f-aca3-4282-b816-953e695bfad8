package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "xmind新增模型")
public class XmindCaseAddReq implements Serializable {

    @ZModelProperty(description = "新增节点类型(用例/模块/步骤)", required = false, sample = "TESTCASE")
    private TestcaseAttributeEnum attribute;

    @ZModelProperty(description = "节点名称", required = false, sample = "节点名称")
    private String topic;

    @ZModelProperty(description = "层级", required = false, sample = "1")
    private Integer layer;

    @ZModelProperty(description = "上级节点code", required = false, sample = "11")
    private String parentCode;

    @ZModelProperty(description = "产品code", required = false, sample = "399")
    private String productCode;

    @ZModelProperty(description = "类型", required = false, sample = "MANUAL")
    private TestcaseTypeEnum type;

    @ZModelProperty(description = "用例等级 HIGH MIDDLE LOW", required = false, sample = "MIDDLE")
    private TestcasePriorityEnum priority;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;

    @ZModelProperty(description = "版本code", required = false, sample = "VER24041600205")
    private String versionCode;

}
