package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ZModel(description = "复制场景请求模型")
public class CopySceneInfoReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZModelProperty(description = "需要复制的场景code", sample = "SNF954001974792028160", required = true)
    private String sceneCode;

    @ZModelProperty(description = "新的场景名称", sample = "新的场景名称", required = true)
    @Size(min = 1, max = 60)
    private String sceneName;

    @ZModelProperty(description = "新的场景描述", sample = "新的场景描述")
    @Size(max = 500)
    private String sceneInfoDesc;

    @ZModelProperty(description = "分组code", sample = "ALL", required = true)
    private String parentCode;

    @ZModelProperty(description = "类型", sample = "SCENE")
    private UseCaseFactoryTypeEnum sceneType;

}
