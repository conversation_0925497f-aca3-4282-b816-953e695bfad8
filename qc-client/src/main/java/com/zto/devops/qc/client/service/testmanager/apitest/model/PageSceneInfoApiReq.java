package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PageSceneInfoApiReq implements Serializable {

    @GatewayModelProperty(description = "关联接口类型", required = false)
    private ApiTypeEnum apiType;

    @GatewayModelProperty(description = "关联接口的应用id", required = false)
    private String appId;

    @GatewayModelProperty(description = "关联接口的请求地址", required = false)
    private String apiAddress;
}
