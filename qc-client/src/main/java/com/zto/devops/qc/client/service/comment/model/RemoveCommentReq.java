package com.zto.devops.qc.client.service.comment.model;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RemoveCommentReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZModelProperty(description = "domain 对应的实例编码", required = true, sample = "ISS230303008063")
    private String businessCode;

    @ZModelProperty(description = "业务编码: 业务实例 如 缺陷编码等", required = true, sample = "ISS230303008063")
    private String code;

}
