package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateApiCaseUserCommand extends BaseCommand {

    private String userVariableCode;

    public UpdateApiCaseUserCommand(String aggregateId) {
        super(aggregateId);
    }
}
