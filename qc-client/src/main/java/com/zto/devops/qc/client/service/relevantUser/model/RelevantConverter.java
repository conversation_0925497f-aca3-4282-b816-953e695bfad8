package com.zto.devops.qc.client.service.relevantUser.model;

import com.zto.devops.qc.client.model.relevantUser.query.MyIssueQuery;
import com.zto.devops.qc.client.model.relevantUser.query.MyTaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RelevantConverter {

    RelevantConverter INSTANCE = Mappers.getMapper(RelevantConverter.class);

    MyIssueQuery convertTask(MyTaskReq req);

    @Mapping(target = "productName",source = "product.name")
    @Mapping(target = "productCode",source = "product.code")
    @Mapping(target = "findVersionCode",source = "findVersion.code")
    @Mapping(target = "findVersionName",source = "findVersion.name")
    @Mapping(target = "developerUserName",source = "developer.userName")
    @Mapping(target = "developerUserId",source = "developer.userId")
    @Mapping(target = "testerUserName",source = "tester.userName")
    @Mapping(target = "testerUserId",source = "dockingUser.userId")
    @Mapping(target = "associatedVersionName",source = "associatedVersion.name")
    @Mapping(target = "associatedVersionCode",source = "associatedVersion.code")
    @Mapping(target = "domain",expression = "java(com.zto.devops.framework.client.enums.DomainEnum.getNameByEnum(req.getDomainEnum()))")
    @Mapping(target = "domainDesc",expression = "java(com.zto.devops.framework.client.enums.DomainEnum.getValueByEnum(req.getDomainEnum()))")
    MyTaskResp convert(MyTaskVO req);

    List<MyTaskResp> convert(List<MyTaskVO> req);



}
