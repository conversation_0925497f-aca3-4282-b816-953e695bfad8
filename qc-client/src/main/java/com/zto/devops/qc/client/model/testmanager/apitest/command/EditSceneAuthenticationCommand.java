package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditSceneAuthenticationCommand extends BaseCommand {

    public EditSceneAuthenticationCommand(String aggregateId) {
        super(aggregateId);
    }
}
