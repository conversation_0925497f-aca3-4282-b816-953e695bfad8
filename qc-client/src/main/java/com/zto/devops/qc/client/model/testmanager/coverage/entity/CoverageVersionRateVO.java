package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import java.io.Serializable;
import lombok.Data;

@Data
public class CoverageVersionRateVO implements Serializable {

    /**
     * 分支代码覆盖行数
     */
    private Integer branchCodeCoverNum;

    /**
     * 分支总行数
     */
    private Integer branchCodeSum;

    /**
     * 主干代码覆盖行数
     */
    private Integer masterCodeCoverNum;

    /**
     * 主干总行数
     */
    private Integer masterCodeSum;

    /**
     * 分支版本覆盖率
     */
    private Integer branchVersionRate;

    /**
     * 主干版本覆盖率
     */
    private Integer masterVersionRate;

    /**
     * 版本编码
     */
    private String versionCode;
}
