package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchAddVariableReq implements Serializable {

    @GatewayModelProperty(description = "所属产品code")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @GatewayModelProperty(description = "所属产品名称")
    private String productName;

    @GatewayModelProperty(description = "变量集合")
    private List<BaseApiTestVariableReq> apiTestVariableList;

    @GatewayModelProperty(description = "请求头集合")
    private List<BaseApiTestVariableReq> apiTestHeaderList;

    @GatewayModelProperty(description = "类型")
    private VariableTypeEnum type;

    @GatewayModelProperty(description = "业务code")
    private String linkCode;

    @GatewayModelProperty(description = "场景类型")
    private UseCaseFactoryTypeEnum sceneType;
}
