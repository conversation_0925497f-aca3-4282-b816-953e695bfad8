package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "用例关联缺陷响应模型")
@Data
public class TestcaseIssueResp implements Serializable {

    @ZsmpModelProperty(description = "编号")
    private String code;

    @ZsmpModelProperty(description = "标题")
    private String title;

    @ZsmpModelProperty(description = "缺陷状态")
    private IssueStatus status;

    @ZsmpModelProperty(description = "缺陷状态说明")
    private String statusDesc;

    @ZsmpModelProperty(description = "当前处理人ID")
    private Long handleUserId;

    @ZsmpModelProperty(description = "当前处理人名称")
    private String handleUserName;

    public void setStatus(IssueStatus status) {
        this.status = status;
        if (null != status) {
            this.statusDesc = status.getValue();
        }
    }
}
