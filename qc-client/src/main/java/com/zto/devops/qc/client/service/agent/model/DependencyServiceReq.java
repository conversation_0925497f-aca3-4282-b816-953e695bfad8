package com.zto.devops.qc.client.service.agent.model;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DependencyServiceReq implements Serializable {
    @ZModelProperty(description = "dependAppId", required = true, sample = "devops-qc")
    String dependAppId;
    @ZModelProperty(description = "dependService", required = true, sample = "com.zto.devops.qc.client.service.agent.AgentService.queryExceptionInfo")
    String dependService;
}
