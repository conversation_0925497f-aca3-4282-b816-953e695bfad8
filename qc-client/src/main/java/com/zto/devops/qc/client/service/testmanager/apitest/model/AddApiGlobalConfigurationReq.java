package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiGlobalConfigurationVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "新增接口全局配置模型")
public class AddApiGlobalConfigurationReq implements Serializable {

    @ZsmpModelProperty(description = "产品名称", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "全局配置List", required = true)
    private List<ApiGlobalConfigurationVO> apiGlobalConfigurationVOList;

}
