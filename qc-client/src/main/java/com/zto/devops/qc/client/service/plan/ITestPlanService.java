
package com.zto.devops.qc.client.service.plan;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.report.entity.PlanCaseButtonVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ButtonVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.*;
import com.zto.devops.qc.client.service.plan.model.*;

import java.util.List;

/**
 * plan service
 **/
public interface ITestPlanService {

    Result<TestPlanTipVO> testPlanTip(TestPlanDetailReq req);

    Result<TmTestPlanVO> testPlanDetail(TestPlanDetailReq req);

    Result<RelatedPlanReportResp> relatedTest(RelatedTestReq req);

    PageResult<PlanIssueResp> getPlanIssue(TestPlanIssueReq req);

    Result checkPlanEmail(SendPlanCheckReq req);

    Result<TestPlanCaseResultVO> planCaseResultCount(TestPlanCaseResultCountReq req);

    /**
     * 测试计划分页查询
     *
     * @param req {@link PlanPageReq}
     * @return PageResult<TmTestPlanVO>
     */
    PageResult<TmTestPlanVO> pageTestPlanList(PlanPageReq req);

    PageResult<PagePlanTestcaseResp> pageListPlanCase(PageTestPlanCaseListReq req);

    Result<List<String>> listTestPlanCaseCode(TestPlanCaseCodeListReq req);

    /**
     * 查询测试计划与阶段
     *
     * @param req {@link ListPlanPhaseReq}
     * @return PageResult<PlanPhaseVO>
     */
    PageResult<PagePlanPhaseResp> listPlanPhase(ListPlanPhaseReq req);

    Result<FindSortedPlanCaseResp> findSortedPlanCase(FindSortedPlanCaseReq req);

    PageResult<PageTestPlanResp> getPagePlan(PageTestPlanReq req);

    Result<List<TestPlanResp>> getVersionPlan(VersionPlanReq req);

    Result<List<TestPlanCaseVO>> listPlanCase(TestPlanCaseListReq req);

    Result<List<PlanCaseButtonVO>> listPlanCaseButtons(String planCode, TestPlanStageEnum testStage);

    Result<List<TestPlanCaseVO>> listPlanCaseModule(TestPlanCaseModuleListReq req);

    Result<Void> changeStageStatus(EditTestPlanStageReq req);

    Result<Void> changePlanStatus(EditTestPlanStatusReq req);

    /**
     * 修改测试计划阶段接口
     *
     * @param req {@link ModifyCaseTestStageReq}
     * @return void
     */
    Result<Void> modifyCaseTestStage(ModifyCaseTestStageReq req);


    /**
     * 移除测试计划用例接口
     *
     * @param req {@link RemoveCaseFromTestPlanReq}
     * @return void
     */
    Result<Void> removeCaseFromTestPlan(RemoveCaseFromTestPlanReq req);

    Result<Void> sendPlanEmail(SendTestPlanReq req);

    Result<String> addMobileSpecialTestPlan(AddMobileSpecialTestPlanReq req);

    /**
     * 查询关联测试计划接口
     *
     * @return Result<List < TmTestPlanVO>>
     */
    Result<List<TmTestPlanVO>> associatedTestPlanList(AssociatedTestPlanListReq req);

    /**
     * 关联用例加入测试计划
     *
     * @param req {@link BatchAddCaseInTestPlanReq}
     * @return void
     */
    Result<Void> batchAddCaseInTestPlan(BatchAddCaseInTestPlanReq req);

    /**
     * 变更测试计划执行结果接口
     *
     * @param req {@link ChangeCaseExecuteResultReq}
     * @return void
     */
    Result<Void> changeCaseExecuteResult(ChangeCaseExecuteResultReq req);

    Result<Void> changePlanCaseResultComment(ChangePlanCaseResultCommentReq req);

    /**
     * 获取当前人的权限信息接口
     *
     * @return Result<List < ButtonVO>>
     */
    Result<List<ButtonVO>> currentPersonPermissionInformation(CurrentPersonPermissionInformationReq currentPersonPermissionInformationReq);

    Result<Void> editCommonTestPlan(AddCommonTestPlanReq req);

    Result<Void> editMobileSpecialTestPlan(AddMobileSpecialTestPlanReq req);

    Result<Void> editSafeTestPlan(AddSafeTestPlanReq req);

    /**
     * 某个阶段测试是否已经开始了
     *
     * @return
     */
    Result<Boolean> stageTestStart(StageTestStartReq req);

    /**
     * 测试计划是否已经开始了
     *
     * @return
     */
    Result<Boolean> testPlanStart(String versionCode);

    Result<VerifyTestPassConditionResp> verifyTestPassCondition(VerifyTestPassConditionReq req);

    Result<Void> closeDelayAcceptAudit(CloseDelayAcceptAuditReq req);
}