package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.PreDataTagEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "造数共享列表查询请求模型")
public class SharedSceneModuleQueryReq implements Serializable {

    @ZsmpModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZsmpModelProperty(description = "上级code", sample = "238243473r5t4673489756734989675468932")
    private String parentCode;

    @ZsmpModelProperty(description = "模糊查询", required = false, sample = "238243473r5t4673489756734989675468932")
    private String search;

    @ZsmpModelProperty(description = "共享产品标识", required = false, sample = "true")
    private Boolean shareProductFlag;

    @ZsmpModelProperty(description = "当前操作人id", required = false, sample = "545535")
    private Long currentUserId;

    @ZsmpModelProperty(description = "当前操作人", required = false, sample = "ののののののののののののののののののののの")
    private String currentUserName;

    @ZsmpModelProperty(description = "造数类型", required = false, sample = "MY_COLLECT")
    private PreDataTagEnum preDataTagType;

}
