package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddAutomaticRecordReq implements Serializable {

    @ZModelProperty(description = "是否根据git提交操作，自动重新解析登记库（0:不是；1:是;）", required = false, sample = "0")
    private Boolean autoAnalysisFlag;

    /**
     * 所属产品code
     */
    @ZModelProperty(description = "所属产品code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    /**
     * 登记库名称
     */
    @ZModelProperty(description = "登记库名称", required = true, sample = "111")
    private String name;

    /**
     * 备注
     */
    @ZModelProperty(description = "描述", required = false, sample = "222")
    private String comment;

    /**
     * jmeter:源地址-OSS上的脚本文件地址;xunit-git地址
     */
    @ZModelProperty(description = "脚本地址", required = true, sample = "333")
    private String address;

    /**
     * 类型：1-jmeter, 2-testng, 3-postman, 4-junit,5-pyunit
     */
    @ZModelProperty(description = "框架类型", required = false, sample = "JMETER")
    private AutomaticRecordTypeEnum type;

    /**
     * 脚本文件名
     */
    @ZModelProperty(description = "脚本文件名", required = false, sample = "111")
    private String fileName;

    /**
     * 数据文件地址
     */
    @ZModelProperty(description = "数据文件地址", required = false, sample = "111")
    private String dataFileAddress;

    /**
     * 扩展jar包地址
     */
    @ZModelProperty(description = "扩展jar包地址", required = false, sample = "222")
    private String extendJarAddress;

    /**
     * 第三方jar包地址
     */
    @ZModelProperty(description = "第三方jar包地址", required = false, sample = "333")
    private String thirdJarAddress;

    @ZModelProperty(description = "桶名", required = false, sample = "111")
    private String bucketName;

    //所属目录code
    @ZModelProperty(description = "所属目录code", required = false, sample = "TC231130064892")
    private String testcaseCode;

    @ZModelProperty(description = "所属目录path", required = false, sample = "path333")
    private String path;

    @ZModelProperty(description = "工作目录", required = false, sample = "fat")
    private String workSpace;
    @ZModelProperty(description = "分支名", required = false, sample = "branch111")
    private String branch;
    @ZModelProperty(description = "git路径", required = false, sample = "git111")
    private String gitAddress;
    @ZModelProperty(description = "扫描路径", required = false, sample = "111")
    private String scanDirectory;

    @ZModelProperty(description = "commitId", required = false, sample = "1234")
    private String commitId;

    @ZModelProperty(description = "data目录", required = false, sample = "eeee/")
    private String dataDir;

    @ZModelProperty(description = "ib目录", required = false, sample = "aaa/")
    private String libDir;

}
