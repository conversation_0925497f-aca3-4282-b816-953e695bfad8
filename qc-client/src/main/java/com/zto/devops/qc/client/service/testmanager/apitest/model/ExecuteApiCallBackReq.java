package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExecuteApiCallBackReq implements Serializable {

    @GatewayModelProperty(description = "任务code", required = true)
    private String taskCode;

    @GatewayModelProperty(description = "用例 code", required = true)
    private String caseCode;

    @GatewayModelProperty(description = "执行结果", required = true)
    private TestPlanCaseStatusEnum result;

    @GatewayModelProperty(description = "json文件path", required = true)
    private String path;

}
