package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSONArray;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DataCenterParameter;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DebugOnNodeReq implements Serializable {

    @GatewayModelProperty(description = "场景code")
    private String sceneCode;

    @GatewayModelProperty(description = "节点code")
    private String nodeCode;

    @GatewayModelProperty(description = "状态", required = false)
    private SceneInfoStatusEnum status;

//    @GatewayModelProperty(description = "版本")
//    private String sceneVersion;

    @GatewayModelProperty(description = "调试类别[NODE(调试单个节点) FIRSTLY(调试节点所在的第一条链路)]")
    private DebugTypeEnum debugType;

    @GatewayModelProperty(description = "执行环境", required = false)
    private String ztoenv;

    @GatewayModelProperty(description = "执行环境", required = false)
    private String envName;

    @GatewayModelProperty(description = "造数入参", required = false)
    private JSONArray inputParameter;

}
