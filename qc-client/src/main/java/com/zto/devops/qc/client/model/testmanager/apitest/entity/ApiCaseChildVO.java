
package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseSourceTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@GatewayModel(description = "api自动化用例VO")
@Data
public class ApiCaseChildVO implements Serializable {
    private static final long serialVersionUID = 43066355548949415L;

    @GatewayModelProperty(description = "用例编码")
    private String caseCode;

    @GatewayModelProperty(description = "用例名")
    private String caseName;

    @GatewayModelProperty(description = "用例类别")
    private ApiCaseSourceTypeEnum sourceType;
    private Integer sourceTypeNum;
    public ApiCaseSourceTypeEnum getSourceType() {
        if (null != this.sourceTypeNum) {
            this.sourceType = ApiCaseSourceTypeEnum.codeOf(this.sourceTypeNum);
        }
        return this.sourceType;
    }

    @GatewayModelProperty(description = "用例类别(中文描述)")
    private String sourceTypeDesc;
    public String getSourceTypeDesc() {
        if (null != this.sourceType) {
            this.sourceTypeDesc = this.sourceType.getDesc();
        }
        return this.sourceTypeDesc;
    }

    @GatewayModelProperty(description = "最近一次执行任务id")
    private String taskCode;

    @GatewayModelProperty(description = "最近一次执行结果")
    private TestPlanCaseStatusEnum testResult;

    @GatewayModelProperty(description = "最近一次执行结果(中文描述)")
    private String testResultDesc;
    public String getTestResultDesc() {
        if (null != this.testResult) {
            this.testResultDesc = this.testResult.getValue();
        }
        return this.testResultDesc;
    }

    @GatewayModelProperty(description = "最近一次执行耗时(秒)")
    private Long duration;

    @GatewayModelProperty(description = "最近一次执行开始时间")
    private Date executeTime;

    @GatewayModelProperty(description = "用例状态[删除 DELETED(0); 启用 ENABLED(1); 禁用 DISABLED(2);]")
    private ApiCaseEnableEnum enable;

    @GatewayModelProperty(description = "状态(数据库实际值)")
    private Integer enableNum;
    public ApiCaseEnableEnum getEnable() {
        if (null != this.enableNum) {
            this.enable = ApiCaseEnableEnum.codeOf(this.enableNum);
        }
        return this.enable;
    }

    @GatewayModelProperty(description = "状态(中文描述)")
    private String enableDesc;
    public String getEnableDesc() {
        return ApiCaseEnableEnum.getDesc(this.enableNum);
    }

    @GatewayModelProperty(description = "用例更新时间")
    private Date gmtModified;
}
