package com.zto.devops.qc.client.service.attachment.model;

import java.io.Serializable;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentDocumentTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@GatewayModel(description = "附件")
@Data
public class AttachmentResp implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "领域")
    private DomainEnum domain;

    @GatewayModelProperty(description = "业务编码")
    private String businessCode;

    @GatewayModelProperty(description = "附件编码")
    private String code;

    @GatewayModelProperty(description = "附件路径")
    private String url;

    @GatewayModelProperty(description = "附件名称")
    private String name;

    @GatewayModelProperty(description = "私有组文件名")
    private String remoteFileId;

    @GatewayModelProperty(description = "附件类型 FILE 文件, URL 链接")
    private AttachmentTypeEnum type;

    @GatewayModelProperty(description = "文档类型")
    private AttachmentDocumentTypeEnum documentType;

    @GatewayModelProperty(description = "创建人id")
    private Long creatorId;
    @GatewayModelProperty(description = "创建人")
    private String creator;
    @GatewayModelProperty(description = "创建时间")
    private String gmtCreate;
    @GatewayModelProperty(description = "创建人id")
    private Long modifierId;
    @GatewayModelProperty(description = "创建人id")
    private String modifier;
    @GatewayModelProperty(description = "创建人id")
    private String gmtModified;

//    @GatewayModelProperty(description = "创建人id")
//    private Long creatorId;
//
//    @GatewayModelProperty(description = "创建人")
//    private String creator;
//
//    @GatewayModelProperty(description = "创建时间")
//    private Date gmtCreate;

}
