package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.model.testmanager.plan.entity.BatchTestPlanVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SceneBatchAddToTestPlanCommand extends BaseCommand {

    private List<String> sceneCodeList;

    private List<BatchTestPlanVO> testPlanList;

    private String productCode;

    public SceneBatchAddToTestPlanCommand(String aggregateId) {
        super(aggregateId);
    }
}
