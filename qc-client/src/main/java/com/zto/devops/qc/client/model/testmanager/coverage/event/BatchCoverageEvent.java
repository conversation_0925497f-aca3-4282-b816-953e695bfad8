package com.zto.devops.qc.client.model.testmanager.coverage.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BatchCoverageEvent extends BaseEvent implements ActionEvent {

    @GatewayModelProperty(description = "产品编号")
    private String productCode;

    @GatewayModelProperty(description = "版本编码")
    private String code;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "应用名称集")
    private List<String> appIdList;

    @GatewayModelProperty(description = "报告类型")
    private RecordTypeEnum recordType;

    @Override
    public String action() {
        return "批量应用生成覆盖率报告";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }

}
