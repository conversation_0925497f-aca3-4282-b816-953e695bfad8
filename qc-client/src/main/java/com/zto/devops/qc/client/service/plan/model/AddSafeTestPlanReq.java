package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanPriorityEnum;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AddSafeTestPlanReq implements Serializable {

    @ZModelProperty(description = "计划编码", required = false, sample = "TP240305003075")
    private String planCode;
    @ZModelProperty(description = "计划名", required = false, sample = "V1.0.0测试计划")
    private String planName;
    @ZModelProperty(description = "所属产品id", required = false, sample = "399")
    private String productCode;
    @ZModelProperty(description = "产品名称", required = false, sample = "一站式研发平台")
    private String productName;
    @ZModelProperty(description = "关联版本id", required = false, sample = "VER2306058015")
    private String versionCode;
    @ZModelProperty(description = "版本名称", required = false, sample = "V1.0.0")
    private String versionName;
    @ZModelProperty(description = "关联计划code", required = false, sample = "TP240305003075")
    private String relationPlanCode;
    @ZModelProperty(description = "关联计划名称", required = false, sample = "V1.0.0安全测试计划")
    private String relationPlanName;
    @ZModelProperty(description = "部门Id", required = false, sample = "817355")
    private Long deptId;
    @ZModelProperty(description = "部门名", required = false, sample = "研发效能部")
    private String deptName;
    @ZModelProperty(description = "产品负责人id", required = false, sample = "5984549")
    private Long productDirectorId;
    @ZModelProperty(description = "产品负责人名", required = false, sample = "产品负责人")
    private String productDirectorName;
    @ZModelProperty(description = "权限测试", required = false, sample = "0")
    private Boolean permissionsTest;
    @ZModelProperty(description = "优先级", required = false, sample = "MEDIUM")
    private TestPlanPriorityEnum priority;
    @ZModelProperty(description = "最晚测试日期", required = false, sample = "1711987200000")
    private Date lastTestDate;
    @ZModelProperty(description = "测试信息", required = false, sample = "测试信息")
    private String testInformation;
    @ZModelProperty(description = "权限测试信息", required = false, sample = "权限测试信息")
    private String permissionsTestInformation;
}
