package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class JmxFileUploadCommand extends BaseCommand {


    private String bucketName;

    private String address;

    private String fileName;

    private String productCode;

    private String productName;

    private String sceneCode;

    private UseCaseFactoryTypeEnum type;

    public JmxFileUploadCommand(String aggregateId) {
        super(aggregateId);
    }
}
