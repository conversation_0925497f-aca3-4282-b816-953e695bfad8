package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.qc.client.enums.issue.*;
import com.zto.devops.qc.client.model.issue.entity.*;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
public class IssueResp implements Serializable {
    private static final long serialVersionUID = -5081557705616517719L;

    @GatewayModelProperty(description = "编号", required = false)
    private String code;
    @GatewayModelProperty(description = "标题", required = false)
    private String title;
    @GatewayModelProperty(description = "描述", required = false)
    private String description;

    @GatewayModelProperty(description = "缺陷状态", required = false)
    private IssueStatus status;
    @GatewayModelProperty(description = "缺陷状态说明", required = false)
    private String statusDesc;

    @GatewayModelProperty(description = "修复版本编码", required = false)
    private String fixVersionCode;
    @GatewayModelProperty(description = "修复版本名称", required = false)
    private String fixVersionName;

    @GatewayModelProperty(description = "重新打开时间", required = false)
    private String reopenTime;
    @GatewayModelProperty(description = "开始修复时间", required = false)
    private String startFixTime;
    @GatewayModelProperty(description = "延期修复时间", required = false)
    private String delayFixTime;
    @GatewayModelProperty(description = "交付验证时间", required = false)
    private String deliverTime;
    @GatewayModelProperty(description = "拒绝时间", required = false)
    private String rejectTime;
    @GatewayModelProperty(description = "关闭时间", required = false)
    private String closeTime;
    @GatewayModelProperty(description = "更新时间", required = false)
    private String updateTime;
    @GatewayModelProperty(description = "更新时间", required = false)
    private String findTime;
    @GatewayModelProperty(description = "创建时间", required = false)
    private String gmtCreate;

    @GatewayModelProperty(description = "发现环境", required = false)
    private IssueFindEnv findEnv;
    @GatewayModelProperty(description = "发现环境说明", required = false)
    private String findEnvDesc;

    @GatewayModelProperty(description = "发现阶段", required = false)
    private IssueFindStage findStage;
    @GatewayModelProperty(description = "发现阶段说明", required = false)
    private String findStageDesc;

    @GatewayModelProperty(description = "优先级", required = false)
    private IssuePriority priority;
    @GatewayModelProperty(description = "优先级说明", required = false)
    private String priorityDesc;

    @GatewayModelProperty(description = "复现概率", required = false)
    private IssueRepetitionRate repetitionRate;
    @GatewayModelProperty(description = "复现概率说明", required = false)
    private String repetitionRateDesc;

    @GatewayModelProperty(description = "缺陷根源", required = false)
    private IssueRootCause rootCause;
    @GatewayModelProperty(description = "缺陷根源说明", required = false)
    private String rootCauseDesc;

    @GatewayModelProperty(description = "测试方法", required = false)
    private IssueTestMethod testMethod;
    @GatewayModelProperty(description = "测试方法说明", required = false)
    private String testMethodDesc;

    @GatewayModelProperty(description = "缺陷类型", required = false)
    private IssueType type;
    @GatewayModelProperty(description = "缺陷类型说明", required = false)
    private String typeDesc;

    @GatewayModelProperty(description = "产品编码", required = false)
    private String productCode;
    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;

    @GatewayModelProperty(description = "关联迭代code", required = false)
    private String sprintCode;

    @GatewayModelProperty(description = "关联迭代名称", required = false)
    private String sprintName;



    @GatewayModelProperty(description = "需求编码", required = false)
    private String requirementCode;
    @GatewayModelProperty(description = "需求名称", required = false)
    private String requirementName;

    @GatewayModelProperty(description = "需求层级", required = false)
    private RequirementLevel requirementLevel;
    @GatewayModelProperty(description = "需求层级说明", required = false)
    private String requirementLevelDesc;

    @GatewayModelProperty(description = "发现版本编码", required = false)
    private String findVersionCode;
    @GatewayModelProperty(description = "发现版本名称", required = false)
    private String findVersionName;

    @GatewayModelProperty(description = "报告人ID", required = false)
    private Long findUserId;
    @GatewayModelProperty(description = "报告人名称", required = false)
    private String findUserName;

    @GatewayModelProperty(description = "当前处理人ID", required = false)
    private Long handleUserId;
    @GatewayModelProperty(description = "当前处理人名称", required = false)
    private String handleUserName;

    @GatewayModelProperty(description = "开发人员ID", required = false)
    private Long developUserId;
    @GatewayModelProperty(description = "开发人员名称", required = false)
    private String developUserName;

    @GatewayModelProperty(description = "测试人员ID", required = false)
    private Long testUserId;
    @GatewayModelProperty(description = "测试人员名称", required = false)
    private String testUserName;

    @GatewayModelProperty(description = "创建人ID", required = false)
    private Long creatorId;
    @GatewayModelProperty(description = "创建人名称", required = false)
    private String creator;

    @GatewayModelProperty(description = "更新人员ID", required = false)
    private Long updateUserId;
    @GatewayModelProperty(description = "更新人员名称", required = false)
    private String updateUserName;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;
    @GatewayModelProperty(description = "用例集合", required = false)
    private List<CaseVO> caseList;
    @GatewayModelProperty(description = "标签", required = false)
    private List<TagVO> tags;
    @GatewayModelProperty(description = "标签", required = false)
    private String tagName;
    @GatewayModelProperty(description = "评论", required = false)
    private Set<CommentVO> comments;
    @GatewayModelProperty(description = "状态说明", required = false)
    private String content;
    @GatewayModelProperty(description = "原因", required = false)
    private Reason reason;
    @GatewayModelProperty(description = "原因", required = false)
    private String reasonDesc;
    @GatewayModelProperty(description = "工时", required = false)
    private Double actualWorkingHours;

    @GatewayModelProperty(description = "文档个数", required = false)
    private Integer fileCount;

    @GatewayModelProperty(description = "用例个数", required = false)
    private Integer caseCount;

    @GatewayModelProperty(description = "可编辑字段", required = false)
    private Set<String> editableFieldNames;

    @GatewayModelProperty(description = "可操作事件", required = false)
    private List<Event> operableEvents;
//    @GatewayModelProperty(description = "可操作事件", required = false)
//    private List<ButtonVO> operableEvents;

    @GatewayModelProperty(description = "是否流转开发", required = false)
    private Boolean transitToDevelop;

    @GatewayModelProperty(description = "是否流转测试", required = false)
    private Boolean transitToTest;

    @GatewayModelProperty(description = "before 版本确认之前，after 版本确认之前", required = false)
    private String versionConfirm;

    @GatewayModelProperty(description = "是否是多个 修复的缺陷", required = false)
    private Boolean morefixVersion;

    /**
     * 审查状态  1已审查 0未审查
     */
    @GatewayModelProperty(description = "审查状态", required = false)
    private Boolean examination;
    /**
     * 测试遗漏  1为是 0为否
     */
    @GatewayModelProperty(description = "测试遗漏", required = false)
    private Boolean testOmission;
    /**
     * 代码缺陷  1为是 0为否
     */
    @GatewayModelProperty(description = "代码缺陷", required = false)
    private Boolean codeDefect;


    @GatewayModelProperty(description = "测试遗漏原因", required = false)
    private String testOmissionVersion;
    @GatewayModelProperty(description = "代码遗漏原因", required = false)
    private String codeDefectVersion;

    @GatewayModelProperty(description = "应用类型", required = false)
    private IssueApplicationType applicationType;

    @GatewayModelProperty(description = "应用类型描述", required = false)
    private String applicationTypeDesc;

    @GatewayModelProperty(description = "拒绝原因", sample = "NO_ISSUE", required = false)
    private RefuseReason refuseReason;

    @GatewayModelProperty(description = "拒绝原因描述", sample = "非Bug", required = false)
    private String refuseReasonDesc;

    @GatewayModelProperty(description = "抄送人id", required = false)
    private String ccUserId;

    @GatewayModelProperty(description = "抄送人名字", required = false)
    private String ccUserName;

    /**
     * 抄送人
     */
    @GatewayModelProperty(description = "抄送人", required = false)
    private Set<RelevantUserVO> ccManList;

    @GatewayModelProperty(description = "计划开始时间", required = false)
    private Date planStartDate;
    @GatewayModelProperty(description = "计划结束时间", required = false)
    private Date planEndDate;
    @GatewayModelProperty(description = "宝盒聊天记录code", required = false)
    private String msgCode;

    @GatewayModelProperty(description = "提醒")
    private String warn;
    @GatewayModelProperty(description = "天数")
    private int warnDay;

    @GatewayModelProperty(description = "是否有效缺陷", required = false)
    private Boolean validFlag;



    public void setEnumrationDesc() {
        this.setStatusDesc(Objects.nonNull(this.getStatus()) ? this.getStatus().getValue() : null);
        this.setPriorityDesc(Objects.nonNull(this.getPriority()) ? this.getPriority().getValue() : null);
        this.setFindEnvDesc(Objects.nonNull(this.getFindEnv()) ? this.getFindEnv().getValue() : null);
        this.setFindStageDesc(Objects.nonNull(this.getFindStage()) ? this.getFindStage().getValue() : null);
        this.setTypeDesc(Objects.nonNull(this.getType()) ? this.getType().getValue() : null);
        this.setRepetitionRateDesc(Objects.nonNull(this.getRepetitionRate()) ? this.getRepetitionRate().getValue() : null);
        this.setRootCauseDesc(Objects.nonNull(this.getRootCause()) ? this.getRootCause().getValue() : null);
        this.setTestMethodDesc(Objects.nonNull(this.getTestMethod()) ? this.getTestMethod().getValue() : null);
        this.setRequirementLevelDesc(Objects.nonNull(this.getRequirementLevel()) ? this.getRequirementLevel().getValue() : null);
        this.setReasonDesc(Objects.nonNull(this.getReason()) ? this.getReason().getValue() : null);
        this.setApplicationTypeDesc(Objects.nonNull(this.getApplicationType()) ? this.getApplicationType().getValue() : null);
        this.setRefuseReasonDesc(Objects.nonNull(this.getRefuseReason()) ? this.getRefuseReason().getValue() : null);
    }

}
