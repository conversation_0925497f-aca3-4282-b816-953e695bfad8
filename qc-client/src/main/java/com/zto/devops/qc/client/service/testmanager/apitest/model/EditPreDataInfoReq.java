package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSONObject;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "造数保存req")
public class EditPreDataInfoReq implements Serializable {

    @ZsmpModelProperty(description = "造数code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "前端数据", required = true)
    private String sceneFrontData;

    @ZsmpModelProperty(description = "后端数据", required = true)
    private JSONObject sceneBackData;

    @ZsmpModelProperty(description = "数据库编号")
    private List<Integer> dbIds;
}
