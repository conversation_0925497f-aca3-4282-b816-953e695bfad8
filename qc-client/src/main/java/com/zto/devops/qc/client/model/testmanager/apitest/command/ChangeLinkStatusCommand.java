package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ChangeLinkStatusCommand extends BaseCommand {

    private Integer sceneVersion;

    private String linkMapCode;

    private Boolean enable;

    private UseCaseFactoryTypeEnum sceneType;

    private String productCode;

    public ChangeLinkStatusCommand(String aggregateId) {
        super(aggregateId);
    }
}
