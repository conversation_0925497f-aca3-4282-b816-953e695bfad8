package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestFunctionPointVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AddCommonTestPlanReq implements Serializable {

    @GatewayModelProperty(description = "计划编码", required = false, sample = "TP240305003075")
    private String planCode;
    @GatewayModelProperty(description = "计划名", required = false, sample = "V1.0.0测试计划")
    private String planName;
    @GatewayModelProperty(description = "计划状态", required = false, sample = "IN_PROGRESS")
    private TestPlanNewStatusEnum status;
    @GatewayModelProperty(description = "测试策略", required = false, sample = "EXPLORE_TEST")
    private TestPlanStrategyEnum testStrategy;

    @GatewayModelProperty(description = "所属产品id", required = false, sample = "399")
    private String productCode;
    @GatewayModelProperty(description = "产品名称", required = false, sample = "一站式研发平台")
    private String productName;

    @GatewayModelProperty(description = "关联版本id", required = false, sample = "VER2306058015")
    private String versionCode;
    @GatewayModelProperty(description = "版本名称", required = false, sample = "V1.0.0")
    private String versionName;
    @GatewayModelProperty(description = "版本开始时间", required = false, sample = "1711987200000")
    private Date startDate;
    @GatewayModelProperty(description = "版本上线时间", required = false, sample = "1711987200000")
    private Date publishDate;
    @GatewayModelProperty(description = "开发人数", required = false, sample = "1")
    private Integer developerNum;
    @GatewayModelProperty(description = "测试人数", required = false, sample = "1")
    private Integer testerNum;
    @GatewayModelProperty(description = "计划提测/准入时间", required = false, sample = "1711987200000")
    private Date accessDate;
    @GatewayModelProperty(description = "计划提测时间:上午-AM | 下午-PM", required = false, sample = "AM")
    private TestPlanDatePartitionEnum accessDatePartition;
    @GatewayModelProperty(description = "计划准出时间", required = false, sample = "1711987200000")
    private Date permitDate;
    @GatewayModelProperty(description = "计划提测时间:上午-AM | 下午-PM", required = false, sample = "AM")
    private TestPlanDatePartitionEnum permitDatePartition;

    @GatewayModelProperty(description = "产品负责人id", required = false, sample = "817355")
    private Long productDirectorId;
    @GatewayModelProperty(description = "产品负责人名", required = false, sample = "产品负责人")
    private String productDirectorName;
    @GatewayModelProperty(description = "测试负责人id", required = false, sample = "817355")
    private Long testDirectorId;
    @GatewayModelProperty(description = "测试负责人名", required = false, sample = "测试负责人")
    private String testDirectorName;
    @GatewayModelProperty(description = "计划负责人id", required = false, sample = "817355")
    private Long planDirectorId;
    @GatewayModelProperty(description = "计划负责人名", required = false, sample = "计划负责人")
    private String planDirectorName;
    @GatewayModelProperty(description = "备注", required = false, sample = "备注")
    private String comment;

    @GatewayModelProperty(description = "移动专项测试", required = false, sample = "1")
    private Boolean mobileSpecialTest;
    @GatewayModelProperty(description = "安全扫描", required = false, sample = "1")
    private Boolean securityScan;

    @GatewayModelProperty(description = "静态分析", required = false, sample = "0")
    private Boolean staticAnalysis;
    @GatewayModelProperty(description = "静态分析时间", required = false, sample = "1711987200000")
    private Date staticAnalysisTime;
    @GatewayModelProperty(description = "静态分析负责人id", required = false, sample = "817355")
    private Long staticAnalysisDirectorId;
    @GatewayModelProperty(description = "静态分析负责人名", required = false, sample = "静态分析负责人")
    private String staticAnalysisDirectorName;

    @GatewayModelProperty(description = "性能测试", required = false, sample = "0")
    private Boolean performanceTest;
    @GatewayModelProperty(description = "性能测试时间", required = false, sample = "1711987200000")
    private Date performanceTestTime;
    @GatewayModelProperty(description = "性能测试负责人id", required = false, sample = "817355")
    private Long performanceTestDirectorId;
    @GatewayModelProperty(description = "性能测试负责人名", required = false, sample = "性能测试负责人")
    private String performanceTestDirectorName;

    @GatewayModelProperty(description = "探索性测试", required = false, sample = "0")
    private Boolean exploratoryTest;
    @GatewayModelProperty(description = "探索性测试负责人", required = false, sample = "817355")
    private Long exploratoryTestDirectorId;
    @GatewayModelProperty(description = "探索性测试负责人名", required = false, sample = "探索性测试负责人")
    private String exploratoryTestDirectorName;
    @GatewayModelProperty(description = "探索性测试时间", required = false, sample = "1711987200000")
    private Date exploratoryTestTime;

    @GatewayModelProperty(description = "测试功能点List", required = false, sample = "[]")
    private List<TestFunctionPointVO> pointList;

}
