package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoverageAppInfoVO implements Serializable {
    private static final long serialVersionUID = 8143111838555670452L;

    @GatewayModelProperty(description = "版本code")
    @JsonIgnore
    private String versionCode;

    @GatewayModelProperty(description = "版本名称")
    @JsonIgnore
    private String versionName;

    @GatewayModelProperty(description = "应用id")
    private String appId;

    @GatewayModelProperty(description = "不达标原因")
    private String reason;

    @GatewayModelProperty(description = "合并生成状态(INITIAL-待生成，RUNNING-生成中，SUCCEED-成功，FAIL-失败，NEEDLESS-无需生成)")
    @JsonIgnore
    private String status;

    @GatewayModelProperty(description = "合并报告或错误日志地址")
    @JsonIgnore
    private String recordUrl;

    @GatewayModelProperty(description = "合并报告类型(FULL-全量,BRANCH-分支增量,MASTER-主干增量)")
    @JsonIgnore
    private String recordType;

    @GatewayModelProperty(description = "代码覆盖率求和")
    @JsonIgnore
    private BigDecimal recordRate;

    @GatewayModelProperty(description = "标准值求和")
    @JsonIgnore
    private BigDecimal standardRate;

    private String customReason;

    /**
     * 覆盖率报告异常原因
     */
    @JsonIgnore
    private String recordErrorMsg;

    private List<String> reasonList;

    /**
     * 筛选出不达标应用集合
     *
     * @param voList 待筛选应用集合
     * @return {@link CoverageAppInfoVO}
     */
    public static List<CoverageAppInfoVO> filterFailedList(String versionCode,List<CoverageAppInfoVO> voList) {
        List<CoverageAppInfoVO> resultList = new ArrayList<>();
        Map<String, List<CoverageAppInfoVO>> voMap = voList.stream().collect(Collectors.groupingBy(CoverageAppInfoVO::getAppId));

        for (Map.Entry<String, List<CoverageAppInfoVO>> entry : voMap.entrySet()) {
            List<CoverageAppInfoVO> values = entry.getValue();
            boolean flag = false;
            String reason = "";
            if (CollectionUtil.isEmpty(values)){
                continue;
            }
            String versionName = values.get(0).getVersionName();
            for (CoverageAppInfoVO vo : values) {
                reason = StringUtils.isNotBlank(vo.getReason()) ? vo.getReason() : "";
                if (vo.getStatus().equals(RecordStatusEnum.SUCCEED.name())) {
                    // 覆盖率大于等于标准值 && 生成成功 达标
                    // 代码没有实现类 && 生成成功 达标
                    if ((vo.getRecordRate().compareTo(vo.getStandardRate()) >= 0)
                            || (StringUtils.isNotBlank(vo.getReason()) && vo.getReason().equals("代码没有实现类"))) {
                        flag = true;
                        break;
                    }
                }
            }
            if (!flag) {
                resultList.add(CoverageAppInfoVO.builder().versionCode(versionCode).versionName(versionName).appId(entry.getKey()).reason(reason).build());
            }
        }
        return resultList;
    }

    public static List<CoverageAppInfoVO> filterSuccessList(String versionCode,List<CoverageAppInfoVO> voList) {
        List<CoverageAppInfoVO> resultList = new ArrayList<>();
        Map<String, List<CoverageAppInfoVO>> voMap = voList.stream().collect(Collectors.groupingBy(CoverageAppInfoVO::getAppId));

        for (Map.Entry<String, List<CoverageAppInfoVO>> entry : voMap.entrySet()) {
            List<CoverageAppInfoVO> values = entry.getValue();
            boolean flag = false;
            String reason = "";
            if (CollectionUtil.isEmpty(values)){
                continue;
            }
            String versionName = values.get(0).getVersionName();
            for (CoverageAppInfoVO vo : values) {
                reason = StringUtils.isNotBlank(vo.getReason()) ? vo.getReason() : "";
                if (vo.getStatus().equals(RecordStatusEnum.SUCCEED.name())) {
                    // 覆盖率大于等于标准值 && 生成成功 达标
                    // 代码没有实现类 && 生成成功 达标
                    if (vo.getRecordRate().compareTo(vo.getStandardRate()) >= 0) {
                        flag = true;
                        break;
                    }
                }
            }
            if (flag) {
                resultList.add(CoverageAppInfoVO.builder().versionCode(versionCode).versionName(versionName).appId(entry.getKey()).reason(reason).build());
            }
        }
        return resultList;
    }

    /**
     * 兼容多版本的情况
     * @param voList
     * @return
     */
    public static List<CoverageAppInfoVO> filterFailedByVersionCodeList(List<CoverageAppInfoVO> voList) {
        List<CoverageAppInfoVO> resultList = new ArrayList<>();
        Map<String, List<CoverageAppInfoVO>> voMap = voList.stream().collect(Collectors.groupingBy(CoverageAppInfoVO::getVersionCode));
        for (Map.Entry<String, List<CoverageAppInfoVO>> versionEntry : voMap.entrySet()) {
            resultList.addAll(filterFailedList(versionEntry.getKey(),versionEntry.getValue()));
        }
        return resultList;
    }

    public static List<CoverageAppInfoVO> filterSuccessByVersionCodeList(List<CoverageAppInfoVO> voList) {
        List<CoverageAppInfoVO> resultList = new ArrayList<>();
        Map<String, List<CoverageAppInfoVO>> voMap = voList.stream().collect(Collectors.groupingBy(CoverageAppInfoVO::getVersionCode));
        for (Map.Entry<String, List<CoverageAppInfoVO>> versionEntry : voMap.entrySet()) {
            resultList.addAll(filterSuccessList(versionEntry.getKey(),versionEntry.getValue()));
        }
        return resultList;
    }

    public void addAppInfoVOReason() {
        List<String> reasonList = new ArrayList<>();
        String customReason = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(this.reason)) {
            String regular = "(?<=[1-9]\\.\\s|[1-9][0-9]\\.\\s)([\\d\\D]*?)(?=\\$)";
            Pattern pattern = Pattern.compile(regular);
            Matcher m = pattern.matcher(this.reason);
            while (m.find()) {
                String content = m.group(1);
                if (StringUtils.isNotBlank(content)) {
                    if (content.contains("自定义：")) {
                        customReason = content.substring(4);
                    } else {
                        reasonList.add(content);
                    }
                }
            }
            // 系统自动回填的原因，没有序号
            if (CollectionUtils.isEmpty(reasonList) && StringUtils.isBlank(customReason)) {
                reasonList.add(this.reason);
            }
            this.reasonList = reasonList;
            this.customReason = customReason;
        }
    }

}
