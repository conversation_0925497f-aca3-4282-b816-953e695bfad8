package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.qc.client.enums.issue.*;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SlowSqlToIssueReq implements Serializable {

    @ZModelProperty(description = "产品编码", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "产品名称", required = true, sample = "一站式研发平台")
    private String productName;

    @ZModelProperty(description = "标题", required = true, sample = "缺陷标题")
    private String title;

    @ZModelProperty(description = "描述", required = true, sample = "缺陷描述")
    private String description;

    @ZModelProperty(description = "测试人员ID", required = true, sample = "5305175")
    private Long testUserId;

    @ZModelProperty(description = "测试人员名称", required = true, sample = "测试人")
    private String testUserName;

    @ZModelProperty(description = "发现人员ID", required = true, sample = "5305175")
    private Long findUserId;

    @ZModelProperty(description = "发现人员名称", required = true, sample = "报告人")
    private String findUserName;

    @ZModelProperty(description = "执行时间", required = true, sample = "1725897600000")
    private Long startAt;

    @ZModelProperty(description = "ip地址", required = true, sample = "*************")
    private String location;

    @ZModelProperty(description = "应用id", required = true, sample = "devops-qc")
    private String appId;

    @ZModelProperty(description = "发现环境", sample = "FAT_EVN")
    private IssueFindEnv findEnv = IssueFindEnv.FAT_EVN;

    @ZModelProperty(description = "发现阶段", sample = "DEVELOP_STAGE")
    private IssueFindStage findStage = IssueFindStage.TEST_STAGE;

    @ZModelProperty(description = "优先级", sample = "MIDDLE")
    private IssuePriority priority = IssuePriority.MIDDLE;

    @ZModelProperty(description = "复现概率", sample = "OCCASIONALLY")
    private IssueRepetitionRate repetitionRate = IssueRepetitionRate.INEVITABLE;

    @ZModelProperty(description = "缺陷根源", sample = "FUNCTIONAL_DEVELOPMENT_BUG")
    private IssueRootCause rootCause = IssueRootCause.FUNCTIONAL_DEVELOPMENT_BUG;

    @ZModelProperty(description = "测试方法", sample = "FUNCTION_TEST")
    private IssueTestMethod testMethod = IssueTestMethod.OTHER;

    @ZModelProperty(description = "缺陷类型", sample = "FUNCTION_BUG")
    private IssueType type = IssueType.SLOW_QUERY;
}
