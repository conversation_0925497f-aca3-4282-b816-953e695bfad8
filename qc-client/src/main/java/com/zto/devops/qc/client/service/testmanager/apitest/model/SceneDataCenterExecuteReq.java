package com.zto.devops.qc.client.service.testmanager.apitest.model;


import com.alibaba.fastjson.JSONObject;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SceneDataCenterExecuteReq implements Serializable {

    @ZModelProperty(description = "造数编号", required = true, sample = "SNF979388218212352000")
    private String sceneCode;

    @ZModelProperty(description = "造数入参", required = true, sample = "[{\"key\":\"value\"}]")
    private JSONObject inputParameter;

    @ZModelProperty(description = "造数入参", sample = "399")
    private String productCode;

}
