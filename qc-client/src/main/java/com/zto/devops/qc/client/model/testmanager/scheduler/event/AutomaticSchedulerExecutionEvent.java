package com.zto.devops.qc.client.model.testmanager.scheduler.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AutomaticSchedulerExecutionEvent extends BaseEvent {

    private String preCode;

    private String schedulerCode;

    private String executeEnv;

    /**
     * 运行空间tag，用于场景用例执行
     */
    private String executeTag;

    private Boolean coverageFlag;

    private AutomaticTaskTrigModeEnum trigMode;

}
