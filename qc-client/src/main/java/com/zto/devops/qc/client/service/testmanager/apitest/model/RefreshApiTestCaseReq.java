package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
@ZModel(description = "造数收藏/取消收藏造数请求模型")
public class RefreshApiTestCaseReq implements Serializable {
    private static final long serialVersionUID = -6235136631425255365L;
    @ZModelProperty(description = "接口code", sample = "SNF969612906859593728")
    private Set<String> apiCodeSet;

    @ZModelProperty(description = "操作类型（0-接口信息；1-执行结果；2-tag；3-发布状态）", sample = "0")
    private Set<Integer> typeList;
}
