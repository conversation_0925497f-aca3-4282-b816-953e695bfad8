package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "查询测试计划用例上一条/下一条请求模型")
public class FindSortedPlanCaseReq implements Serializable {
    private static final long serialVersionUID = -5708811255351952098L;

    @ZModelProperty(description = "用例code", required = true, sample = "TC231130064892")
    private String caseCode;

    @ZModelProperty(description = "测试计划code", required = true, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "测试计划阶段", required = true, sample = "SMOKE_TEST")
    private TestPlanStageEnum testStage;

    @ZModelProperty(description = "用例等级", required = false, sample = "['MIDDLE']")
    private List<TestcasePriorityEnum> priorityList;

    @ZModelProperty(description = "用例标签", required = false, sample = "[]")
    private List<String> tagList;

    @ZModelProperty(description = "执行结果", required = false, sample = "['PASSED']")
    private List<TestPlanCaseStatusEnum> statusList;

    @ZModelProperty(description = "执行人", required = false, sample = "[5878415]")
    private List<Long> executorIdList;

    @ZModelProperty(description = "名称/编号", required = false, sample = "TC231130064892")
    private String codeOrTitle;

    @ZModelProperty(description = "测试用例类型", required = true, sample = "MANUAL")
    private TestcaseTypeEnum testcaseType;
}
