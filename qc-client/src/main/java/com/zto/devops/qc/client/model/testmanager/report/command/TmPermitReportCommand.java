package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class TmPermitReportCommand extends BaseReportInfoCommand {
    public TmPermitReportCommand(String aggregateId) {
        super(aggregateId);
    }

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date planApprovalExitDate;

    @GatewayModelProperty(description = "实际准出时间", required = false)
    private Date actualApprovalExitDate;

    @GatewayModelProperty(description = "准出延期天数", required = false)
    private Integer delayDays;

    @GatewayModelProperty(description = "测试信息")
    private CaseExecuteResultVO caseResultVO;

    @GatewayModelProperty(description = "安全测试人ID", required = false)
    private Long securityUserId;

    @GatewayModelProperty(description = "安全测试人名称", required = false)
    private String securityUserName;

    @GatewayModelProperty(description = "安全测试结果code", required = false)
    private String securityTestResultCode;

    @GatewayModelProperty(description = "安全测试结果描述", required = false)
    private String securityTestResultDesc;
    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

    @GatewayModelProperty(description = "功能用例模块测试结果")
    private List<TmModuleTestVO> moduleTestVOS;

    @GatewayModelProperty(description = "ui测试结果", required = false)
    private UiTestResultEnum uiTestResult;
}
