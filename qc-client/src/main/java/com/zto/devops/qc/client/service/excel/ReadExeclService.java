package com.zto.devops.qc.client.service.excel;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.report.entity.*;
import com.zto.devops.qc.client.service.excel.model.ReadExcelReq;

import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：ReadExeclService
 * 类 描 述：TODO
 * 创建时间：2021/11/10 3:09 下午
 * 创 建 人：bulecat
 */
public interface ReadExeclService {

    Result<List<PlanFunctionPointResp>> readExcelToPlanFunctionPoint(ReadExcelReq req);

    Result<List<PlanMobileResp>> readExcelToPlanMobile(ReadExcelReq req);
}
