package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import lombok.Data;

@Data
public class HttpParameter implements Serializable {

    private static final long serialVersionUID = 1L;

    String name;
    String value;
    Boolean urlEncode;
    String contentType="text/plain";
    Boolean includeEquals;
    String type;

    public Boolean getIncludeEquals() {
        return null == includeEquals || includeEquals;
    }
}
