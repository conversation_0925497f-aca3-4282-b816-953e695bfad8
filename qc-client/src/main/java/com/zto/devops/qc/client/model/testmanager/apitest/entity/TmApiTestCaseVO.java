package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@GatewayModel(description = "api自动化用例VO")
@Data
public class TmApiTestCaseVO implements Serializable {
    private static final long serialVersionUID = 43066355548949415L;

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "用例编码")
    private String caseCode;

    @GatewayModelProperty(description = "用例名")
    private String caseName;

    @GatewayModelProperty(description = "接口编码")
    private String apiCode;

    @GatewayModelProperty(description = "状态：草稿，发布")
    private ApiCaseStatusEnum status;

    @GatewayModelProperty(description = "用例类型：接口用例")
    private Integer caseType;

    @GatewayModelProperty(description = "用例请求数据")
    private String caseReqData;

    @GatewayModelProperty(description = "父用例编码")
    private String parentCaseCode;

    @GatewayModelProperty(description = "最新任务id")
    private String latestTaskId;

    @GatewayModelProperty(description = "最近一次运行结果")
    private String latestExecuteResultStr;

    private TestPlanCaseStatusEnum latestExecuteResult;

    public TestPlanCaseStatusEnum getTestResult() {
        this.latestExecuteResult = StringUtils.isNotBlank(this.latestExecuteResultStr) ? TestPlanCaseStatusEnum.getByName(this.latestExecuteResultStr) : null;
        return this.latestExecuteResult;
    }

    @GatewayModelProperty(description = "最近一次执行结果(中文描述)")
    private String latestExecuteResultDesc;

    public String getTestResultDesc() {
        this.latestExecuteResultDesc = null != this.latestExecuteResult ? this.latestExecuteResult.getValue() : "";
        return this.latestExecuteResultDesc;
    }

    @GatewayModelProperty(description = "用例状态[删除 DELETED(0); 启用 ENABLED(1); 禁用 DISABLED(2);]")
    private ApiCaseEnableEnum enable;

    public ApiCaseEnableEnum getEnable() {
        if (null != this.enableNum) {
            this.enable = ApiCaseEnableEnum.codeOf(this.enableNum);
        }
        return this.enable;
    }

    @GatewayModelProperty(description = "状态(数据库实际值)")
    private Integer enableNum;

    @GatewayModelProperty(description = "状态(中文描述)")
    private String enableDesc;

    public String getEnableDesc() {
        return ApiCaseEnableEnum.getDesc(this.enableNum);
    }

    @GatewayModelProperty(description = "登记库编号")
    private String automaticSourceCode;

}
