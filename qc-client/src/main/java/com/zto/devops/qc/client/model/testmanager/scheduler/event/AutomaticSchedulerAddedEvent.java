package com.zto.devops.qc.client.model.testmanager.scheduler.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AutomaticSchedulerAddedEvent extends BaseEvent {

    private String schedulerCode;

    private String schedulerName;

    private String productCode;

    private String crontab;

    private String executeEnv;

    private String executeTag;

    private String executeSpaceCode;

    private Boolean coverageFlag;

    private Boolean messageFlag;

    private List<User> ccList;
}
