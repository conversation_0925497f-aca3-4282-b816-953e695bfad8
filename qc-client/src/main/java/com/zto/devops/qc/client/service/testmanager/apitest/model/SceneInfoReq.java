package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SceneInfoReq implements Serializable {

    @GatewayModelProperty(description = "场景code", required = true)
    private String sceneCode;

    @GatewayModelProperty(description = "场景发布状态", required = true)
    private SceneInfoStatusEnum status;

}
