package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.zto.devops.framework.common.util.StringUtil;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/9/16 15:16
 */
@Data
public class CoverageRecordVO implements Serializable {
    private static final long serialVersionUID = 6767763371026671171L;

    @GatewayModelProperty(description = "覆盖率报告编码")
    private String code;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "版本名称")
    private String versionName;

    @GatewayModelProperty(description = "应用名")
    private String appId;

    @GatewayModelProperty(description = "应用编码")
    private String applicationCode;

    @GatewayModelProperty(description = "标准值")
    private BigDecimal standardRate;

    @GatewayModelProperty(description = "分支覆盖率", required = false)
    private BigDecimal branchRecordRate;

    @GatewayModelProperty(description = "分支生成状态", required = false)
    private String branchStatus;

    @GatewayModelProperty(description = "分支报告地址", required = false)
    private String branchRecordUrl;

    @GatewayModelProperty(description = "分支不达标原因", required = false)
    private String branchRemark;

    @GatewayModelProperty(description = "分支覆盖率报告异常原因", required = false)
    private String branchRecordErrorMsg;

    @GatewayModelProperty(description = "分支创建人", required = false)
    private String branchCreator;

    @GatewayModelProperty(description = "分支创建时间", required = false)
    private Date branchGmtCreate;

    @GatewayModelProperty(description = "分支commitId")
    private String branchCommitId;

    @GatewayModelProperty(description = "分支git比对地址")
    private String branchGitCompareUrl;

    @GatewayModelProperty(description = "分支存储桶名")
    private String branchBucketName;

    @GatewayModelProperty(description = "分支文件名")
    private String branchFileName;

    @GatewayModelProperty(description = "分支差异类型", required = false)
    private String branchDiffType;

    @GatewayModelProperty(description = "代码覆盖率备注", required = false)
    private String comment;

    @GatewayModelProperty(description = "计划code")
    private String testPlanCode;

    @GatewayModelProperty(description = "不达标原因集合")
    private List<String> reasonList;

    @GatewayModelProperty(description = "接口覆盖率")
    private BigDecimal interfaceCoverageRate;

    public List<String> getReasonList() {
        if (StringUtil.isNotBlank(this.branchRemark)) {
            this.reasonList = Arrays.asList(this.branchRemark.split("\\$"))
                    .stream()
                    .filter(item -> StringUtil.isNotBlank(item))
                    .collect(Collectors.toList());
        }
        return this.reasonList;
    }
}
