package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "批量生成接口用例请求模型")
public class BatchGenerateApiCaseReq implements Serializable {

    @ZModelProperty(description = "接口code列表")
    private List<String> apiCodes;

    @ZModelProperty(description = "用例code列表")
    private List<String> caseCodes;
}
