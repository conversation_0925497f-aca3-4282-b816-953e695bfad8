package com.zto.devops.qc.client.service.coverage.model.resp;

import com.zto.devops.qc.client.model.testmanager.coverage.entity.CommitInfoVO;

import java.util.List;
import java.util.Objects;

public class CoverageReportClassInfoResp {
    private String classFile;
    private String className;
    private String packages;
    private List<int[]> addLines;
    private List<int[]> delLines;
    private String type;
    private List<CommitInfoVO> commitInfos;

    public CoverageReportClassInfoResp() {

    }

    public String getClassFile() {
        return classFile;
    }

    public void setClassFile(String classFile) {
        this.classFile = classFile;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getPackages() {
        return packages;
    }

    public void setPackages(String packages) {
        this.packages = packages;
    }

    public List<int[]> getAddLines() {
        return addLines;
    }

    public void setAddLines(List<int[]> addLines) {
        this.addLines = addLines;
    }

    public List<int[]> getDelLines() {
        return delLines;
    }

    public void setDelLines(List<int[]> delLines) {
        this.delLines = delLines;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<CommitInfoVO> getCommitInfos() {
        return commitInfos;
    }

    public void setCommitInfos(List<CommitInfoVO> commitInfos) {
        this.commitInfos = commitInfos;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) return true;
        if (object == null || getClass() != object.getClass()) return false;
        CoverageReportClassInfoResp that = (CoverageReportClassInfoResp) object;
        return Objects.equals(classFile, that.classFile) && Objects.equals(className, that.className) && Objects.equals(packages, that.packages) && Objects.equals(addLines, that.addLines) && Objects.equals(delLines, that.delLines) && Objects.equals(type, that.type) && Objects.equals(commitInfos, that.commitInfos);
    }

    @Override
    public int hashCode() {
        return Objects.hash(classFile, className, packages, addLines, delLines, type, commitInfos);
    }
}
