package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/10/12 17:40
 */
@Data
@ZModel(description = "查询覆盖率报告类增量数据入参")
public class CoverageReportClassInfoReq implements Serializable {
    private static final long serialVersionUID = 935566555725748029L;

    @Auth(type = AuthTypeConstant.VERSION_CODE)
    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "应用名", required = true, sample = "devops-qc")
    private String appId;

    @ZModelProperty(description = "commitId", required = true, sample = "dbbe406954097858331a78d26950263b05fb6562")
    private String commitId;

    @ZModelProperty(description = "包路径", required = true, sample = "com.zto.devops.qc.domain.service")
    private String packages;

    @ZModelProperty(description = "类名", required = true, sample = "TestClass")
    private String className;


}
