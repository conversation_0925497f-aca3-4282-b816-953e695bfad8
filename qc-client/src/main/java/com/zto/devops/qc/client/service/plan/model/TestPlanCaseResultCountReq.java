package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TestPlanCaseResultCountReq implements Serializable {
    private static final long serialVersionUID = 786569561978497934L;

    @ZModelProperty(description = "计划Code", required = true, sample = "TP240305003075")
    private String code;

    @ZModelProperty(description = "测试阶段,SMOKE_TEST-冒烟测试|FUNCTIONAL_TEST-功能测试|ONLINE_SMOKE_TEST-线上冒烟测试", sample = "1", required = false)
    private TestPlanStageEnum testStage;
}
