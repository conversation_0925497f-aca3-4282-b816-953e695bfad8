package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ZModel(description = "文档运行历史转case")
public class DocInvokeHistoryToApiTestCaseVO implements Serializable {

    private static final long serialVersionUID = 935566555725748029L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "用例名称", required = true, sample = "单接口测试用例名称")
    @Size(min = 1, max = 60)
    private String caseName;

    @ZModelProperty(description = "接口类型", required = true, sample = "HTTP")
    private ApiTypeEnum apiType;

    @ZModelProperty(description = "接口文档运行历史Id", required = false, sample = "5487")
    private Long docHistoryId;

    @ZModelProperty(description = "应用名称", required = false, sample = "单接口测试用例名称")
    private String appId;

    @ZModelProperty(description = "文档id", required = true, sample = "920216")
    private Long serviceDocId;

    @ZModelProperty(description = "文档版本", required = true, sample = "default")
    private String version;

    @ZModelProperty(description = "接口请求方法", required = true, sample = "http:POST; Dubbo:gyq202505131700Test()")
    private String method;

    @ZModelProperty(description = "http接口请求地址", required = false, sample = "")
    private String url;

    @ZModelProperty(description = "dubbo接口分组", required = false, sample = "gyq123321Test")
    private String serviceGroup;

    @ZModelProperty(description = "dubbo接口版本", required = false, sample = "2.0")
    private String serviceVersion;

    @ZModelProperty(description = "dubbo接口服务", required = false, sample = "com.zto.test.service.GyqTestService")
    private String service;

    @ZModelProperty(description = "接口请求参数", required = true, sample = "")
    private String parameters;

}
