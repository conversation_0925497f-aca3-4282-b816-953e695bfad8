package com.zto.devops.qc.client.service.testmanager.email.model;

import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PageEmailResp implements Serializable {

    @GatewayModelProperty(description = "邮件code")
    private String emailCode;

    @GatewayModelProperty(description = "邮件名称")
    private String emailName;

    @GatewayModelProperty(description = "报告/计划code")
    private String businessCode;

    @GatewayModelProperty(description = "邮件类型", required = false)
    private String emailType;
    private EmailTypeEnum emailTypeEnum;

    public String getEmailType() {
        return null == emailTypeEnum ? "" : emailTypeEnum.getValue();
    }

    @GatewayModelProperty(description = "所属版本", required = false)
    private String versionDesc;

    @GatewayModelProperty(description = "所属版本Code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "关联测试计划Code", required = false)
    private String relatePlanCode;

    @GatewayModelProperty(description = "关联测试计划名称", required = false)
    private String relatePlanName;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    @GatewayModelProperty(description = "发送时间", required = false)
    private Date sendTime;

    @GatewayModelProperty(description = "发送人名字", required = false)
    private String sendUserName;

    @GatewayModelProperty(description = "发送人id", required = false)
    private String sendUserId;

}
