package com.zto.devops.qc.client.model.relevantUser.query;

import com.zto.devops.framework.client.simple.Button;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MyIssueBHVO implements Serializable {

    @GatewayModelProperty(description = "业务code")
    private String businessCode;

    @GatewayModelProperty(description = "名称")
    private String name;

    @GatewayModelProperty(description = "状态")
    private String status;

    @GatewayModelProperty(description = "状态")
    private String statusDesc;

    @GatewayModelProperty(description = "优先级")
    private String priority;

    @GatewayModelProperty(description = "优先级")
    private String priorityDesc;

    @GatewayModelProperty(description = "发现版本")
    private String findVersionCode;

    @GatewayModelProperty(description = "发现版本名称")
    private String findVersionName;

    @GatewayModelProperty(description = "开发人员姓名")
    private String developerUserName;

    @GatewayModelProperty(description = "开发人员Id")
    private Long developerUserId;

    @GatewayModelProperty(description = "测试人员姓名")
    private String testerUserName;

    @GatewayModelProperty(description = "测试人员Id")
    private Long testerUserId;

    @GatewayModelProperty(description = "提醒")
    private String warn;

    @GatewayModelProperty(description = "创建时间")
    private Date gmtCreate;

    @GatewayModelProperty(description = "按钮")
    private List<Button> buttonVOs;

}
