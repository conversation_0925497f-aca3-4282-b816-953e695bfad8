package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;

import java.util.Map;

@Data
public class JsonExtractorComponent extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参考：https://blog.csdn.net/qq_42484209/article/details/110878150*
     * 放在后置里，默认针对当前的取样器进行json内容变量提取*
     */
//    String referenceNames;//Names of created variables
//    String jsonPathExprs;//JSON Path expressions
//    String matchNumbers; //Match No.(0 for Random)
//    String defaultValues;
    JSONArray variableExtraction;
}
