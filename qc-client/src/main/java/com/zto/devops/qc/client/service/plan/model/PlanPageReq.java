package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.enums.OrderByEnum;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ZModel(description = "测试计划分页查询Req")
public class PlanPageReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 534442810715987031L;

    @ZModelProperty(description = "所属产品code", sample = "1", required = true)
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "计划类型", sample = "1, 2, 3")
    private List<String> type;

    @ZModelProperty(description = "计划名称", sample = "1")
    private String planName;

    @ZModelProperty(description = "计划状态", sample = "1, 2")
    private List<String> status;

    @ZModelProperty(description = "测试策略", sample = "STANDARD_TEST,SIMPLE_TEST")
    private List<TestPlanStrategyEnum> testStrategyList;

    @ZModelProperty(description = "计划提测开始时间", sample = "2021-09-07 00:00:00")
    private Date accessTimeStart;

    @ZModelProperty(description = "计划提测结束时间", sample = "2021-09-07 00:00:00")
    private Date accessTimeEnd;

    @ZModelProperty(description = "计划准出开始时间", sample = "2021-09-07 00:00:00")
    private Date permitTimeStart;

    @ZModelProperty(description = "计划准出结束时间", sample = "2021-09-07 00:00:00")
    private Date permitTimeEnd;

    @ZModelProperty(description = "计划负责人id", sample = "1, 2, 3")
    private List<String> planDirectorId;

    @ZModelProperty(description = "创建开始时间", sample = "2021-09-07 00:00:00")
    private Date createTimeStart;

    @ZModelProperty(description = "创建结束时间", sample = "2021-09-07 00:00:00")
    private Date createTimeEnd;

    @ZModelProperty(description = "更新开始时间", sample = "2021-09-07 00:00:00")
    private Date updateTimeStart;

    @ZModelProperty(description = "更新结束时间", sample = "2021-09-07 00:00:00")
    private Date updateTimeEnd;

    @ZModelProperty(description = "更新人编码")
    private List<Long> modifierId;

    @ZModelProperty(description = "排序字段")
    private String orderField;

    @ZModelProperty(description = "排序方式")
    private OrderByEnum orderType = OrderByEnum.DESC;

}
