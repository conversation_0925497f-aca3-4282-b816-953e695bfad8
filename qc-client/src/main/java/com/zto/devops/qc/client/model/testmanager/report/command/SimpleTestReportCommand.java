package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SimpleTestReportCommand extends BaseReportInfoCommand {

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;

    @GatewayModelProperty(description = "开发人数", required = false)
    private Integer developerCount;

    @GatewayModelProperty(description = "测试人数", required = false)
    private Integer testerCount;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date planPresentationDate;

    @GatewayModelProperty(description = "计划提测时间-上下午", required = false)
    private String planPresentationDay;

    @GatewayModelProperty(description = "实际提测时间", required = false)
    private Date actualPresentationDate;

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date planApprovalExitDate;

    @GatewayModelProperty(description = "计划准出时间--上下午", required = false)
    private String planApprovalExitDay;

    @GatewayModelProperty(description = "实际准出时间", required = false)
    private Date actualApprovalExitDate;

    @GatewayModelProperty(description = "实际提测时间上午、下午", required = false)
    private String actualPresentationDay;

    @GatewayModelProperty(description = "实际准出时间上午、下午", required = false)
    private String actualApprovalExitDay;

    @GatewayModelProperty(description = "实际上线时间", required = false)
    private Date actualOnlineDate;

    @GatewayModelProperty(description = "计划发布时间", required = false)
    private Date planOnlineDate;

    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    @GatewayModelProperty(description = "延期 -1 否，1 是", required = false)
    private Integer delay;

    @GatewayModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @GatewayModelProperty(description = "测试信息", required = false)
    private CaseExecuteResultVO caseExecuteResultVO;

    @GatewayModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @GatewayModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    @GatewayModelProperty(description = "ui测试结果", required = false)
    private UiTestResultEnum uiTestResult;

    public SimpleTestReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
