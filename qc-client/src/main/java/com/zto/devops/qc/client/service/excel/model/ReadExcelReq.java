package com.zto.devops.qc.client.service.excel.model;

import com.zto.devops.qc.client.enums.constants.FileTypeEunm;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReadExcelReq implements Serializable {

    @ZModelProperty(description = "文件地址", required = false, sample = "文件地址")
    private String url;

//    @GatewayModelProperty(description = "文件地址", required = true)
//    private FileTypeEunm type;
    @ZModelProperty(description = "文件地址", required = false, sample = "文件地址")
    private String remoteFileId;

}
