package com.zto.devops.qc.client.model.testmanager.coverage.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class BatchCoverageCommand extends BaseCommand {

    @GatewayModelProperty(description = "产品编号")
    private String productCode;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "版本名称")
    private String versionName;

    @GatewayModelProperty(description = "应用名称集")
    private List<String> appIdList;

    @GatewayModelProperty(description = "报告类型")
    private RecordTypeEnum recordType;

    @GatewayModelProperty(description = "差异类型", required = false)
    private DiffTypeEnum diffType;

    public BatchCoverageCommand(String aggregateId) {
        super(aggregateId);
    }

}
