package com.zto.devops.qc.client.model.testmanager.coverage.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/16 14:40
 */
@Data
public class CoverageRecordPageQuery extends PageQueryBase {

    @GatewayModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @GatewayModelProperty(description = "应用名", required = false)
    private String appId;

    @GatewayModelProperty(description = "分支生成状态", required = false)
    private List<String> branchStatus;

    @GatewayModelProperty(description = "创建人", required = false)
    private Long creatorId;

    @GatewayModelProperty(description = "创建时间-开始", required = false)
    private Date gmtCreateStart;

    @GatewayModelProperty(description = "创建时间-结束", required = false)
    private Date gmtCreateEnd;

    @GatewayModelProperty(description = "排序字段 branch_record_rate|master_record_rate", required = false)
    private String orderField;

    @GatewayModelProperty(description = "排序类型 asc|desc", required = false)
    private String orderType;

    @GatewayModelProperty(description = "差异类型", required = false)
    private DiffTypeEnum diffType;

}
