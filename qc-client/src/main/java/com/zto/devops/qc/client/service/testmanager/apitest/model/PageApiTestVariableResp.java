package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PageApiTestVariableResp implements Serializable {

    @GatewayModelProperty(description = "id")
    private Long id;

    @GatewayModelProperty(description = "产品编号")
    private String productCode;

    @GatewayModelProperty(description = "产品名称")
    private String productName;

    @GatewayModelProperty(description = "链路编号")
    private String linkCode;

    @GatewayModelProperty(description = "变量编号")
    private String variableCode;

    @GatewayModelProperty(description = "变量名称")
    private String variableName;

    @GatewayModelProperty(description = "变量键")
    private String variableKey;

    @GatewayModelProperty(description = "变量值")
    private String variableValue;

    @GatewayModelProperty(description = "变量类型")
    private String type;

    @GatewayModelProperty(description = "执行状态")
    private String variableStatus;

    @GatewayModelProperty(description = "变量子类型")
    private SubVariableTypeEnum subVariableType;

    @GatewayModelProperty(description = "登录有效时间")
    private Integer loginValidTime;

    private Long creatorId;
    private String creator;
    private Date gmtCreate;
    private Long modifierId;
    private String modifier;
    private Date gmtModified;
}
