package com.zto.devops.qc.client.service.relevantUser.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

@Data
@GatewayModel(description = "取消关注缺陷实体")
public class UnfollowIssueReq implements Serializable {

    @ZModelProperty(description = "缺陷编号", required = true, sample = "ISS230303008063")
    @NotBlank(message = "缺陷编号不能为空")
    private String issueCode;

    @ZModelProperty(description = "关注编号", required = true, sample = "关注编号")
    @NotBlank(message = "关注编号不能为空")
    private String code;
}
