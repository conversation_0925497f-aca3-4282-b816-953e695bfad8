
package com.zto.devops.qc.client.model.testmanager.scheduler.entity;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class ProductSchedulerVO implements Serializable {
    private static final long serialVersionUID = -19403612534276535L;

    @ZsmpModelProperty(description = "任务code")
    private String schedulerCode;

    @ZsmpModelProperty(description = "任务名称")
    private String schedulerName;

    @ZsmpModelProperty(description = "所属产品")
    private String productCode;

}
