package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.common.util.StringUtil;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZsmpModel(description = "ES列表查询请求模型")
public class QueryZBaseESListReq implements Serializable {
    private static final long serialVersionUID = 208631724449283487L;

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "模糊查询")
    private String logicName;

    @ZsmpModelProperty(description = "模糊查询")
    private Integer pageSize;

    @ZsmpModelProperty(description = "模糊查询")
    private Integer currentPage;

    public static QueryZBaseESListReq buildSelf(String docProductCode, String search) {
        return QueryZBaseESListReq.builder()
                .productCode(docProductCode)
                .logicName(StringUtil.isBlank(search) ? Strings.EMPTY : search)
                .pageSize(100)
                .currentPage(0)
                .build();
    }
}
