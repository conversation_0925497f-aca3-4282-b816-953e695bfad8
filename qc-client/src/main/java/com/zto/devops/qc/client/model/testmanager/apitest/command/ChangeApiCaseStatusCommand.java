package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseBatchOperationEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ChangeApiCaseStatusCommand extends BaseCommand {
    private static final long serialVersionUID = 8126956923365100435L;

    private String apiCaseCode;

    private ApiCaseStatusEnum status;

    private ApiCaseBatchOperationEnum operation;

    public ChangeApiCaseStatusCommand(String aggregateId) {
        super(aggregateId);
    }
}
