package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableUsageTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.QueryPreDataVariableVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class QueryPreDataVariableResp implements Serializable {


    @GatewayModelProperty(description = "入参变量集合")
    private List<QueryPreDataVariableVO> inputParameter;

    @GatewayModelProperty(description = "出参变量集合")
    private List<QueryPreDataVariableVO> outputParameter;

    public static QueryPreDataVariableResp buildSelf(List<QueryPreDataVariableVO> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return null;
        }
        Map<VariableUsageTypeEnum, List<QueryPreDataVariableVO>> voMap =
                voList.stream().collect(Collectors.groupingBy(QueryPreDataVariableVO::getUsageType));
        QueryPreDataVariableResp resp = new QueryPreDataVariableResp();
        if (MapUtils.isNotEmpty(voMap)) {
            resp.setInputParameter(voMap.getOrDefault(VariableUsageTypeEnum.INPUT, new ArrayList<>()));
            resp.setOutputParameter(voMap.getOrDefault(VariableUsageTypeEnum.OUTPUT, new ArrayList<>()));
        }
        return resp;
    }
}
