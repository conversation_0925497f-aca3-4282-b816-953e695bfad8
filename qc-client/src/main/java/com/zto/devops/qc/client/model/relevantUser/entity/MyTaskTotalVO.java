package com.zto.devops.qc.client.model.relevantUser.entity;

import com.zto.devops.framework.client.query.PageQueryBase;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目名称：user-parent
 * 类 名 称：MyTaskResp
 * 类 描 述：TODO
 * 创建时间：2021/10/12 5:08 下午
 * 创 建 人：bulecat
 */
@Data
public class MyTaskTotalVO extends PageQueryBase implements Serializable {

    private Integer taskTotal = 0;
    private Integer issueTotal = 0;
    private Integer requirementTotal = 0;
    private Integer sprintTotal = 0;
    private Integer versionTotal = 0;
    private Integer reviewTotal= 0;
    private Integer riskTotal= 0;
    private Integer projectTotal= 0;
}
