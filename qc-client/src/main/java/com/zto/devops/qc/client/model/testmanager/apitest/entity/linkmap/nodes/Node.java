package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.nodes;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;


@Data
public class Node extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    String user;

    String apiCode;

    String apiName;

    JSONArray preComponents;

    JSONArray data;  //笛卡尔积规则数据

    JSONObject sampler; //HttpRequestComponent

    JSONArray postComponents;

    String requestUrl;

    Boolean gatewaySource;

    String docProductCode;

    Long docId;

    String interfaceType;

    String envTag;

    String envName;

    JSONArray dataCenter;

    JSONObject pollingController;
}
