package com.zto.devops.qc.client.model.testmanager.scheduler.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.simple.User;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddAutomaticSchedulerCommand extends BaseCommand {

    private String schedulerName;

    private String productCode;

    private String crontab;

    private String executeEnv;

    private String executeTag;

    private String executeSpaceCode;

    private Boolean coverageFlag;

    private Boolean messageFlag;

    private List<User> ccList;

    public AddAutomaticSchedulerCommand(String aggregateId) {
        super(aggregateId);
    }
}
