package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class EditApiTestVariableCommand extends BaseCommand {

    private String productCode;

    private String productName;

    private String linkCode;

    private String variableCode;

    private String variableName;

    private String variableKey;

    private String variableValue;

    private VariableTypeEnum type;

    private String variableStatus;

    private SubVariableTypeEnum subVariableType;

    private Integer loginValidTime;

    public EditApiTestVariableCommand(String aggregateId) {
        super(aggregateId);
    }
}
