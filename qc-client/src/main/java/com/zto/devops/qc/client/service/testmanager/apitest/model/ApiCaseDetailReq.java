package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZModel(description = "接口用例详情请求模型")
public class ApiCaseDetailReq implements Serializable {

    @ZModelProperty(description = "接口用例code", required = true, sample = "TC231130064892")
    private String caseCode;

    @ZModelProperty(description = "接口用例状态", required = true, sample = "edit")
    private ApiCaseStatusEnum status;
}
