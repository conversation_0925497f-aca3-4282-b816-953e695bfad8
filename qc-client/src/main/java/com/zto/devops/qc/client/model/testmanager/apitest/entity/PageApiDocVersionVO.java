
package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@GatewayModel(description = "分页文档版本号VO")
@Data
public class PageApiDocVersionVO implements Serializable {
    private static final long serialVersionUID = -3570202573041724361L;

    @GatewayModelProperty(description = "总数")
    private Long total;

    @GatewayModelProperty(description = "文档版本号")
    private List<ApiDocVersionVO> list;

    public static PageApiDocVersionVO buildSelf(List<ApiDocVersionVO> doList, Long total) {
        PageApiDocVersionVO result = new PageApiDocVersionVO();
        result.setList(CollectionUtil.isEmpty(doList) ? new ArrayList<>() : doList);
        result.setTotal(CollectionUtil.isEmpty(doList) ? 0l : total);
        return result;
    }
}
