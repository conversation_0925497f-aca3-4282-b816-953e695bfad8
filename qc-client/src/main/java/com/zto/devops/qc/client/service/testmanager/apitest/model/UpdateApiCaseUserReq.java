package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "更新接口用例用户请求模型")
public class UpdateApiCaseUserReq implements Serializable {

    @ZsmpModelProperty(description = "用例code", required = true)
    private String caseCode;

    @ZsmpModelProperty(description = "用户变量code", required = true)
    private String userVariableCode;
}
