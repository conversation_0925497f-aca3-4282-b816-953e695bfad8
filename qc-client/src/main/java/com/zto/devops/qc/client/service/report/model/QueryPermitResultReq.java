package com.zto.devops.qc.client.service.report.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryPermitResultReq implements Serializable {
    private static final long serialVersionUID = -806143456588092978L;

    @ZModelProperty(description = "相关测试计划code", required = true, sample = "TP240416002001")
    private String planCode;
}
