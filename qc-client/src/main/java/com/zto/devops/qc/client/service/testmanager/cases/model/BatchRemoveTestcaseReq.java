package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.model.testmanager.cases.entity.BatchOperateCaseVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchRemoveTestcaseReq implements Serializable {

    @ZsmpModelProperty(description = "唯一标识", required = true, sample = "[]")
    private List<BatchOperateCaseVO> codeList;

}
