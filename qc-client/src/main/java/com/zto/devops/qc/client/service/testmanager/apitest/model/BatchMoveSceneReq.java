package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "批量移动场景请求模型")
public class BatchMoveSceneReq implements Serializable {

    @ZsmpModelProperty(description = "产品编号", required = true)
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZsmpModelProperty(description = "父分组编号", required = true)
    private String parentCode;

    @ZsmpModelProperty(description = "场景编号集合", required = true)
    private List<String> sceneCodeList;

    @ZsmpModelProperty(description = "类型", required = false)
    private UseCaseFactoryTypeEnum sceneType;

}
