package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "节点调试-节点结果VO")
@Data
public class DebugLinkVO implements Serializable {
    private static final long serialVersionUID = 9064389866663434120L;

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "场景code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "场景版本", required = true)
    private Integer sceneVersion;

    private String linkMapCode;

    @ZsmpModelProperty(description = "是否删除 0停用, 1正常", required = true)
    private Boolean enable;

    @ZsmpModelProperty(description = "节点或者线的code", required = true)
    private String linkComponentCode;

    @ZsmpModelProperty(description = "节点或者线的名称", required = true)
    private String linkComponentName;

    @ZsmpModelProperty(description = "类型：NODE;LINE", required = true)
    private String linkComponentType;

    @ZsmpModelProperty(description = "顺序号", required = true)
    private Integer sequenceNumber;

    @ZsmpModelProperty(description = "是否开启mock开关", required = true)
    private Boolean mockSwitch = false;

    @ZsmpModelProperty(description = "节点状态")
    private String status;

    @ZsmpModelProperty(description = "节点类型")
    private String type;

    @ZsmpModelProperty(description = "是否调试历史")
    private Boolean historyFlag = true;
}
