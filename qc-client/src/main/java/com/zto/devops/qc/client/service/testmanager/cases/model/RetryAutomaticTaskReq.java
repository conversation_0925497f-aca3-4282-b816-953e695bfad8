package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "自动化任务重试请求模型")
public class RetryAutomaticTaskReq implements Serializable {

    @ZModelProperty(description = "自动化任务code", sample = "SNF987293442717515776")
    private List<String> taskCodeList;
}
