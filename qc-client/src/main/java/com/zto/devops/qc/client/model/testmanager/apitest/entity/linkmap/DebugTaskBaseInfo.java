package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap;

import com.zto.devops.qc.client.model.dto.SceneLinkInfoEntityDO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "Debug响应")
public class DebugTaskBaseInfo implements Serializable {
    @ZsmpModelProperty(description = "任务id")
    String taskId;
    @ZsmpModelProperty(description = "链路信息")
    List<SceneLinkInfoEntityDO> linkBaseInfo;
}
