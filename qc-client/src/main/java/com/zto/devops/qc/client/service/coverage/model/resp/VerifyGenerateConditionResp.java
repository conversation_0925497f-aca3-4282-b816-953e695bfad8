
package com.zto.devops.qc.client.service.coverage.model.resp;

import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordBasicVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.VerifyGenerateReasonFlatVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.VerifyGenerateReasonVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.VerifyGenerateVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerifyGenerateConditionResp implements Serializable {
    private static final long serialVersionUID = 1870509636783316888L;

    @GatewayModelProperty(description = "总数")
    private Integer totalNum;

    @GatewayModelProperty(description = "通过数")
    private Integer passNum;

    @GatewayModelProperty(description = "不通过数")
    private Integer failNum;

    @GatewayModelProperty(description = "无需生成数")
    private Integer needlessNum;

    @GatewayModelProperty(description = "是否全部通过（全部通过不弹框，直接生成）")
    private Boolean passFlag;

    public Boolean getPassFlag() {
        if (this.passNum > 0 && this.passNum == this.totalNum) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @GatewayModelProperty(description = "是否可以去生成")
    private Boolean generateFlag;

    public Boolean getGenerateFlag() {
        if (this.passNum > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @GatewayModelProperty(description = "校验结果列表")
    private List<VerifyGenerateVO> dataList;

    public static VerifyGenerateConditionResp buildSelf(List<VerifyGenerateReasonFlatVO> dataList, List<CoverageRecordBasicVO> initialList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return pass(initialList);
        }
        Map<RecordStatusEnum, List<VerifyGenerateReasonFlatVO>> statusMap = dataList.stream().collect(Collectors.groupingBy(VerifyGenerateReasonFlatVO::getStatus));
        if (MapUtils.isEmpty(statusMap)) {
            return pass(initialList);
        }
        VerifyGenerateConditionResp resp = init(initialList.size());
        List<VerifyGenerateVO> statusList = new ArrayList<>(statusMap.size());
        statusMap.keySet().forEach(item -> {
            statusList.add(VerifyGenerateVO.buildSelf(item, buildReasonList(statusMap.get(item))));
            setStatusNum(item, statusMap.get(item), resp);
        });
        resp.setDataList(statusList);
        return resp;
    }

    public static void setStatusNum(RecordStatusEnum status, List<VerifyGenerateReasonFlatVO> voList, VerifyGenerateConditionResp resp) {
        if (status.equals(RecordStatusEnum.FAIL)) {
            resp.setFailNum(voList.size());
            return;
        }
        if (status.equals(RecordStatusEnum.SUCCEED)) {
            resp.setPassNum(voList.size());
            return;
        }
        if (status.equals(RecordStatusEnum.NEEDLESS)) {
            resp.setNeedlessNum(voList.size());
            return;
        }
    }

    public static List<VerifyGenerateReasonVO> buildReasonList(List<VerifyGenerateReasonFlatVO> dataList) {
        List<VerifyGenerateReasonVO> reasonList = new ArrayList<>();
        Map<String, List<VerifyGenerateReasonFlatVO>> reasonMap = dataList.stream().collect(Collectors.groupingBy(VerifyGenerateReasonFlatVO::getReason));
        reasonMap.keySet().forEach(reason -> reasonList.add(VerifyGenerateReasonVO.buildSelf(reason,
                reasonMap.get(reason).stream().map(VerifyGenerateReasonFlatVO::getAppId).collect(Collectors.toList()))));
        return reasonList;
    }

    public static VerifyGenerateConditionResp pass(List<CoverageRecordBasicVO> initialList) {
        Integer totalNum = initialList.size();
        VerifyGenerateVO vo = VerifyGenerateVO.buildSelfForSuccess(RecordStatusEnum.SUCCEED, "",
                initialList.stream().map(CoverageRecordBasicVO::getAppId).collect(Collectors.toList()));
        return VerifyGenerateConditionResp.builder()
                .dataList(Collections.singletonList(vo))
                .totalNum(totalNum)
                .passNum(totalNum)
                .failNum(0)
                .needlessNum(0)
                .build();
    }

    public static VerifyGenerateConditionResp init(Integer totalNum) {
        return VerifyGenerateConditionResp.builder()
                .dataList(new ArrayList<>())
                .totalNum(totalNum)
                .passNum(0)
                .failNum(0)
                .needlessNum(0)
                .build();
    }

    /**
     * 符合生成条件的app
     *
     * @return
     */
    public List<String> getAppIdList() {
        if (CollectionUtils.isEmpty(this.dataList)) {
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        this.dataList.stream().filter(data -> (data.getStatus().equals(RecordStatusEnum.SUCCEED))).forEach(data -> {
            data.getReasonList().forEach(reason -> {
                resultList.addAll(reason.getAppIdList());
            });
        });
        return resultList;
    }
}
