package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.apitest.PreDataTagEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneRelatedWithMeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneTagEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageSceneInfoReq extends PageQueryBase implements Serializable {

    @GatewayModelProperty(description = "所属产品code", required = true)
    private String productCode;

    @GatewayModelProperty(description = "场景名称", required = false)
    private String sceneName;

    @GatewayModelProperty(description = "场景描述", required = false)
    private String sceneInfoDesc;

    @GatewayModelProperty(description = "场景状态", required = false)
    private List<Integer> enableList;

    @GatewayModelProperty(description = "更新人编码", required = false)
    private List<Long> modifierIdList;

    @GatewayModelProperty(description = "更新时间（开始）", required = false)
    private Date gmtModifiedStart;

    @GatewayModelProperty(description = "更新时间（結束）", required = false)
    private Date gmtModifiedEnd;

    @GatewayModelProperty(description = "父节点code", required = false)
    private String parentCode;

    @GatewayModelProperty(description = "type", required = false)
    private Integer sceneIndexType;

    @GatewayModelProperty(description = "场景code", required = false)
    private List<String> sceneCodeList;

    @GatewayModelProperty(description = "创建人编码", required = false)
    private List<Long> creatorIdList;

    @GatewayModelProperty(description = "与我相关", required = false)
    private List<SceneRelatedWithMeEnum> relatedWithMe;

    @GatewayModelProperty(description = "当前操作人", required = false)
    private Long currentUserId;

    @GatewayModelProperty(description = "标签", required = false)
    private List<SceneTagEnum> tagNameList;

    @GatewayModelProperty(description = "类型", required = false)
    private String sceneType;

    @GatewayModelProperty(description = "权限", required = false)
    private List<String>  permissions;

    /**
     * 共享状态
     */
    private Boolean shareStatus;

    /**
     * 共享产品标识
     */
    private Boolean shareProductFlag;

    @ZsmpModelProperty(description = "造数类型", required = false, sample = "MY_COLLECT")
    private PreDataTagEnum preDataTagType;

    @GatewayModelProperty(description = "关联接口", required = false)
    private PageSceneInfoApiReq apiReq;

    @ZsmpModelProperty(description = "标签筛选", required = false)
    private List<String> sceneTagNameList;
}
