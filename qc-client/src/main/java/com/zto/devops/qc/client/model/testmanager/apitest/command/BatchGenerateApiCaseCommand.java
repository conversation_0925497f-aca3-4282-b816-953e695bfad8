package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BatchGenerateApiCaseCommand extends BaseCommand {

    private List<String> apiCodes;

    private List<String> caseCodes;

    public BatchGenerateApiCaseCommand(String aggregateId) {
        super(aggregateId);
    }
}
