package com.zto.devops.qc.client.service.plan.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ZsmpModel(description = "关闭延迟验收工单Req")
@Data
public class CloseDelayAcceptAuditReq implements Serializable {

    @ZModelProperty(description = "版本code集合", required = true, sample = "VER2401118036")
    private List<String> versionCodeList;
}
