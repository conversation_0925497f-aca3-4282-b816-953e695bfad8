package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.service.issue.model.IssueResp;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class PlanIssueResp extends IssueResp {
    private static final long serialVersionUID = -3599666860100229581L;

    @GatewayModelProperty(description = "用例编号", required = false)
    private String caseCode;

    @GatewayModelProperty(description = "用例名称", required = false)
    private String caseName;

}
