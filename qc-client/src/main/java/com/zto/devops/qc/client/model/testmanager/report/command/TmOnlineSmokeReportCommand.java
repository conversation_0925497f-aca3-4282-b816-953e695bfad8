package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TmOnlineSmokeReportCommand extends BaseReportInfoCommand {
    public TmOnlineSmokeReportCommand(String aggregateId) {
        super(aggregateId);
    }

    @GatewayModelProperty(description = "实际上线时间", required = false)
    private Date actualPublishDate;

    @GatewayModelProperty(description = "延期 -1 否，1 是", required = false)
    private Integer delay;

    @GatewayModelProperty(description = "延期 -1 否，1 是", required = false)
    private String delayDesc;

    @GatewayModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

}
