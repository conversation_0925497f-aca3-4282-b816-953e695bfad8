package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.BatchOperateCaseVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchModifyCaseGradeReq implements Serializable {

    @ZModelProperty(description = "用例code", required = false, sample = "[]")
    private List<BatchOperateCaseVO> codeList;

    @ZModelProperty(description = "用例等级 HIGH MIDDLE LOW", required = false, sample = "MIDDLE")
    private TestcasePriorityEnum priority;

}
