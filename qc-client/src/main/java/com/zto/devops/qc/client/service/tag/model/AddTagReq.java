package com.zto.devops.qc.client.service.tag.model;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

@Data
@GatewayModel(description = "模型")
public class AddTagReq implements Serializable {
    private static final long serialVersionUID = 6307762460137765768L;

    @ZModelProperty(description = "业务编码code", sample = "业务实例 如 缺陷编码等")
    private String businessCode;

    @ZModelProperty(description = "所属领域", sample = "ISSUE、SCENE、SCENE_PRODUCT")
    private DomainEnum domain;

    @ZModelProperty(description = "标签名称", sample = "test")
    private String tagName;

    @ZModelProperty(description = "标签编码", sample = "111")
    private String tagCode;
}
