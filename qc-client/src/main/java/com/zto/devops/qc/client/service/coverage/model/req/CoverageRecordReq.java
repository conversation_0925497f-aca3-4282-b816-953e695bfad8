package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/9/10 10:40
 */
@Data
@ZModel(description = "查询覆盖率报告入参")
public class CoverageRecordReq implements Serializable {
    private static final long serialVersionUID = 935566555725748029L;

    @Auth(type = AuthTypeConstant.VERSION_CODE)
    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "应用名", required = true, sample = "devops-qc")
    private String appId;


}
