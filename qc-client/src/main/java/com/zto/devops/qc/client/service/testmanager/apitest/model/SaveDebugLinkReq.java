package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.DebugNodeInfoVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SaveDebugLinkReq implements Serializable {
    private static final long serialVersionUID = -1712642873880693789L;

    @ZsmpModelProperty(description = "所属产品code", required = true)
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZsmpModelProperty(description = "场景code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "任务id", required = true)
    private String taskId;

    @ZsmpModelProperty(description = "本次任务最终状态", required = true)
    private String status;

    @ZsmpModelProperty(description = "调试方式", required = true)
    private String debugType;

    @ZsmpModelProperty(description = "节点信息", required = true)
    private List<DebugNodeInfoVO> linkBaseInfo;
}
