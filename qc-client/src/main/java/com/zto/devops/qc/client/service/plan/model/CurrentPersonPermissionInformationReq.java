package com.zto.devops.qc.client.service.plan.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class CurrentPersonPermissionInformationReq implements Serializable {

    @ZModelProperty(description = "权限类型", required = true, sample = "TEST_PLAN")
    private String permissionType;

    @NotEmpty(message = "产品code不能为空")
    @ZModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;
}
