package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "删除场景分组请求模型")
public class DeleteSceneModuleReq implements Serializable {

    @ZsmpModelProperty(description = "分组code")
    private String code;

    @ZsmpModelProperty(description = "类型", required = false)
    private UseCaseFactoryTypeEnum sceneType;

}
