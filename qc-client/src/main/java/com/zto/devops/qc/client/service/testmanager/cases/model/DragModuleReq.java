package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.DragModuleActionEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "拖曳用例分组请求模型")
public class DragModuleReq implements Serializable {

    @ZModelProperty(description = "节点code", required = true, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "上级code", required = true, sample = "TC231130064892")
    private String parentCode;

    @ZModelProperty(description = "目标code", required = true, sample = "TC231130064892")
    private String targetCode;

    @ZModelProperty(description = "行为：上方 BEFORE，下方 AFTER，分组内 INNER", required = true, sample = "BEFORE")
    private DragModuleActionEnum action;

    @ZModelProperty(description = "类型：MANUAL 手工，AUTO 自动化，SOURCERECORD 登记库", required = true, sample = "MANUAL")
    private TestcaseTypeEnum type;

}
