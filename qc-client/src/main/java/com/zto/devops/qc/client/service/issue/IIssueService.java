package com.zto.devops.qc.client.service.issue;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.entity.action.ActionLog;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.issue.command.LinkSprintIssueCommand;
import com.zto.devops.qc.client.model.issue.entity.*;
import com.zto.devops.qc.client.model.issue.query.*;
import com.zto.devops.qc.client.model.relevantUser.entity.MyTaskTotalVO;
import com.zto.devops.qc.client.model.relevantUser.query.*;
import com.zto.devops.qc.client.model.rpc.project.VersionBaseEvent;
import com.zto.devops.qc.client.service.issue.model.*;

import java.util.List;
import java.util.Map;

public interface IIssueService {

    /********************* 给其他域调用都接口  **********************************/
    Integer requirementQuery(RequirementQuery query); // 项目域使用

    /**
     * 根据修复版本编码查询缺陷数
     * @param fixedVersionCodes 版本编码
     * @return 缺陷数
     */
    Map<String,Long> getIssueCountByFixedVersionCode(List<String> fixedVersionCodes);

    PageIssueVO pageIssueQuery(PageIssueQuery query); // 项目域使用

    List<RelatedMatterStatusCountVO> listRelatedIssueStatusCountQuery(ListRelatedIssueStatusCountQuery query);  // 项目域使用

    List<IssueVO> listIssueForVersionQuery(ListIssueForVersionQuery query);// 项目域使用

    List<MyTaskVO> myIssueObjQuery(MyIssueObjQuery query); // 项目域使用

    MyTaskTotalVO myIssueTotalQuery(MyIssueTotalQuery query); // 项目域使用

    Long listRelevantUserTypeCountQuery(ListRelevantUserTypeCountQuery query); // 项目域使用

    List<IssueBaseVO> listBySprintQuery(ListBySprintQuery query); // 项目域使用

    PageIssueVO pageIssueThingQuery(PageIssueThingQuery query); // 项目域使用

    List<IssueVO> simpleIssueQuery(SimpleIssueQuery query); // 项目域使用

    void handleLinkSprintIssueCommand(List<LinkSprintIssueCommand> commands); //// 项目域使用

    List<IssueVO> issueQuery(IssueQuery issueQuery); // 项目域

    void handleVersionPlanedEvent(VersionPlannedReq req);  // 版本规划缺陷

    ListMyTaskVO myIssueQuery(MyIssueQuery query);

    /********************* 网关接口  **********************************/

    PageResult<ActionLog> listLogs(ListActionLogReq req);

    Result<Void> deliveryValidated(DeliveryValidatedReq req);

    Result<IssueResp> findIssueByCode(FindIssueByCodeReq req);

    Result<List<IssueVO>> findUnClosedByVersionCodes(FindUnClosedReq req);

    Result<List<IssueVO>> findUnClosedByFixedVersionCodes(FindUnClosedReq req);

    Result<List<IssueLegacyListResp>> issueLegacyList(IssueLegacyListReq req);

    Result<IssueNumStatisticsResp> issueNumStatistic(IssueNumStatisticsReq req);

    Result<IssueNumStatisticsResp> issueUiTestNumStatistic(IssueNumStatisticsReq req);

    Result<Void> issueRelated(IssueRelatedRequirementReq req);

    /**
     * 添加缺陷
     */
    Result<Void> addIssue(AddIssueReq req);

    /**
     * 2. 延期修复
     * @param req
     * @return
     */
    Result<Void> delayFixIssue(DelayFixIssueReq req);

    /**
     * 5.退回开发修复
     * @param req
     * @return
     */
    Result<Void> backToRepair(BackToRepairReq req);

    /**
     * 6. 流转  circulation
     * @param req
     * @return
     */
    Result<Void> circulation(CirculationIssueReq req);

    /**
     * 9. 确认关闭
     * @param req
     * @return
     */
    Result<Void> confirmClose(ConfirmCloseReq req);

    Result<Void> reopenIssue(ReopenIssueReq req);

    Result<Void> startFixIssue(StartFixIssueReq req);

    Result<Void> validatedAccessClosed(ValidatedAccessClosedReq req);

    Result<List<RelationTestcaseListVO>> getRelationTestcaseList(String code);

    PageResult<IssueResp> pageIssue(PageIssueReq req);

    Result<LaneIssueCtxVO> listLaneQuery(PageIssueReq req);

    Result<IssueResp> findIssueByCodeM(String issueCode);

    Result<Void> editIssue(EditIssueReq req);

    Result<Void> refusedIssue(RefusedIssueReq req);

    Result<Void> removeIssue(RemoveIssueReq req);

    Result<Void> editIssueByVersionLane(EditIssueByLaneReq req);


    /*************************************** 对外接口 ***************************************************/
    /**
     * 通过发现版本code查询所有缺陷详情
     * */
    Result<List<IssueResp>> findIssueByVersionCode(FindIssueByVersionCodeReq req);

    /**
     * 通过缺陷code查询缺陷详情
     * */
    Result<IssueResp> findIssueByIssueCode(FindIssueByIssueCodeReq req);

    /**
     * 通过缺陷code查询所有缺陷流转信息
     * */
    Result<List<TransitionNodeResp>> findIssueTransitionNodeByIssueCode(FindIssueTransitionNodeByIssueCodeReq req);

    /**
     * 通过缺陷code查询所有缺陷标签信息
     * */
    Result<List<IssueTagResp>> findIssueTagByIssueCode(FindIssueTagByIssueCodeReq req);

    /**
     * 获取产品维度缺陷统计数据
     * */
    Result<StatisticsProductIssueResp> getStatisticsProductIssue(StatisticsProductIssueReq req);

    /*************************************** 对外接口 ***************************************************/

    List<HandlerWaitFixIssueCountVO> handlerWaitFixIssueCountByProductCodeListQuery(List<String> productCodeList);

    List<DevThingVO> listIssueByHandleUserId(Long handleUserId);

    void updateIssueHandleUserId(List<DevThingUpdateVO> vos);

    List<TagVO> listBusinessTagWithFixVersionCode(String versionCode); //project

    IssueVO selectIssueByCode(FindIssueByCodeQuery query);

    void closeIssueByCode(String issueCode,User user);

    Result<Void> issueMsgJob();

    Result<SlowSqlToIssueResp> slowSqlToIssue(SlowSqlToIssueReq req);

    Result<IssueResp> findIssueByCodeToken(FindIssueByCodeTokenReq req);

    /**
     * 规划版本缺陷
     * */
    void planVersionIssue(VersionBaseEvent event);

    Result<Void> exportIssue(ExportIssueReq req);

    Result<Integer> exportIssueCount(ExportIssueReq req);
}
