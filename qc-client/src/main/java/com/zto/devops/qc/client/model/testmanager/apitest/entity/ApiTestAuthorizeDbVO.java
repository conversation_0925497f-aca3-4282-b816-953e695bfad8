package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

@Data
public class ApiTestAuthorizeDbVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZsmpModelProperty(description = "数据库id")
    private Long id;

    @ZsmpModelProperty(description = "数据库名称")
    private String dbName;

    @ZsmpModelProperty(description = "物理库名称")
    private String physicDbName;

}
