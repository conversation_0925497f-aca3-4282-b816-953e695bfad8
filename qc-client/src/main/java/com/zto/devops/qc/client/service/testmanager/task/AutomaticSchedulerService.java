package com.zto.devops.qc.client.service.testmanager.task;

import java.util.List;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.testmanager.scheduler.entity.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;

public interface AutomaticSchedulerService {
    Result<Void> add(AddAutomaticSchedulerReq req);

    Result<Void> edit(EditAutomaticSchedulerReq req);

    Result<Void> delete(AutomaticSchedulerSimpleReq req);

    /**
     * 关联用例
     *
     * @param req {@link AddSchedulerCasesReq}
     * @return
     */
    Result<Void> addCase(AddSchedulerCasesReq req);

    /**
     * 删除用例关联关系
     *
     * @param req
     * @return
     */
    Result<Void> removeCase(RemoveSchedulerCasesReq req);

    /**
     * 分页查询定时任务
     *
     * @param req {@link PageSchedulerReq}
     * @return {@link PageSchedulerVO}
     */
    PageResult<AutomaticSchedulerVO> page(PageSchedulerReq req);

    /**
     * 定时任务详情
     *
     * @param req
     * @return
     */
    Result<AutomaticSchedulerDetailVO> detail(AutomaticSchedulerSimpleReq req);

    Result<List<SchedulerCaseVO>> getModuleList(SchedulerModuleListReq req);

    Result<List<String>> listSchedulerCaseCode(SchedulerCaseCodeListReq req);

    Result<List<SchedulerCaseVO>> getListSchedulerCase(ListSchedulerCaseReq req);

    /**
     * 产品下定时任务列表
     *
     * @param req {@link ProductSchedulerReq}
     * @return {@link ProductSchedulerVO}
     */
    Result<List<ProductSchedulerVO>> list(ProductSchedulerReq req);

    Result<String> getNextExecuteTimes(String cron);

    /**
     * 定时任务-运行一次
     *
     * @param req {@link RunSchedulerReq}
     * @return
     */
    Result<Void> run(RunSchedulerReq req);
}
