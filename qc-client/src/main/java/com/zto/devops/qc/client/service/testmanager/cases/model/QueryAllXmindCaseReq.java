package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryAllXmindCaseReq implements Serializable {

    private static final long serialVersionUID = 8927211571499817740L;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "版本code", required = true, sample = "VER2302168133")
    private String versionCode;

    @ZModelProperty(description = "父节点code", required = true, sample = "TC231130064892")
    private String parentCode;

    @ZModelProperty(description = "是否核心用例", required = true, sample = "0")
    private Boolean setCore;

    @ZModelProperty(description = "类型 MANUAL AUTO", required = true, sample = "MANUAL")
    private TestcaseTypeEnum type;

    @ZModelProperty(description = "计划code", required = false, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "计划阶段", required = false, sample = "SMOKE_TEST")
    private TestPlanStageEnum testStage;

}
