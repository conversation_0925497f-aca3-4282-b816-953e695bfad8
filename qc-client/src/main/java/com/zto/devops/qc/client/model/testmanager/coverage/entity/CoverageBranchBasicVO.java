package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.entity.BaseModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022/10/18 17:16
 */
@Data
public class CoverageBranchBasicVO extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;


    public CoverageBranchBasicVO() {

    }

    public CoverageBranchBasicVO(String versionCode, String appId, String branchName) {
        this.versionCode = versionCode;
        this.appId = appId;
        this.branchName = branchName;
    }

    private Long id;

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * appId
     */
    private String appId;

    /**
     * 分支名称
     */
    private String branchName;

    /**
     * 基准分支名称
     */
    private String basicBranchName;

    /**
     * 基准commitId
     */
    private String basicCommitId;

    /**
     * 项目gitId
     */
    private Long gitProjectId;

}
