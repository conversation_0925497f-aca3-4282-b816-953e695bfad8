package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "校验用例创建人和责任人响应模型")
public class CheckCreatorOrDutyUserResp implements Serializable {

    @ZsmpModelProperty(description = "总数")
    private Integer total;

    @ZsmpModelProperty(description = "通过数")
    private Integer passed;

    @ZsmpModelProperty(description = "拒绝数")
    private Integer refused;
}
