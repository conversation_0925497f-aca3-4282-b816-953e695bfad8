package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ButtonVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ExecuteCaseResultContentVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.FieldVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ZsmpModel(description = "测试计划用例详情响应模型")
@Data
public class PlanCaseResp implements Serializable {

    @ZsmpModelProperty(description = "测试计划code")
    private String planCode;

    @ZsmpModelProperty(description = "用例code")
    private String caseCode;

    @ZsmpModelProperty(description = "所属产品code")
    private String productCode;

    @ZsmpModelProperty(description = "产品名称")
    private String productName;

    @ZsmpModelProperty(description = "版本code")
    private String versionCode;

    @ZsmpModelProperty(description = "版本名称")
    private String versionName;

    @ZsmpModelProperty(description = "所属模块code")
    private String parentCode;

    @ZsmpModelProperty(description = "所属模块名称")
    private List<String> parentFullName;

    @ZsmpModelProperty(description = "名称")
    private String name;

    @ZsmpModelProperty(description = "类型")
    private TestcaseTypeEnum type;

    @ZsmpModelProperty(description = "类型描述")
    private String typeDesc;

    @ZsmpModelProperty(description = "等级")
    private TestcasePriorityEnum priority;

    @ZsmpModelProperty(description = "等级描述")
    private String priorityDesc;

    @ZsmpModelProperty(description = "状态")
    private TestPlanCaseStatusEnum status;

    @ZsmpModelProperty(description = "状态描述")
    private String statusDesc;

    @ZsmpModelProperty(description = "前置条件")
    private String precondition;

    @ZsmpModelProperty(description = "执行人id")
    private Long executorId;

    @ZsmpModelProperty(description = "执行人")
    private String executor;

    @ZsmpModelProperty(description = "备注")
    private String comment;

    @ZsmpModelProperty(description = "已启用/删除")
    private Boolean enable;

    @ZsmpModelProperty(description = "测试步骤")
    private List<TestcaseStepVO> testSteps;

    @ZsmpModelProperty(description = "标签")
    private List<TagVO> tags;

    @ZsmpModelProperty(description = "可编辑字段")
    private List<FieldVO> fields;

    @ZsmpModelProperty(description = "可操作按钮")
    private List<ButtonVO> buttons;

    @ZsmpModelProperty(description = "节点类型")
    private AutomaticNodeTypeEnum nodeType;

    @ZsmpModelProperty(description = "更新人id")
    private Long modifierId;

    @ZsmpModelProperty(description = "更新人")
    private String modifier;

    @ZsmpModelProperty(description = "更新时间")
    private Date gmtModified;

    @ZsmpModelProperty(description = "测试阶段")
    private TestPlanStageEnum testStage;

    @ZsmpModelProperty(description = "执行结果文件")
    private String resultFile;

    @ZsmpModelProperty(description = "执行结果文件内容")
    private List<ExecuteCaseResultContentVO> resultContent;

    @ZsmpModelProperty(description = "日志记录code")
    private String operateCaseCode;

    @ZsmpModelProperty(description = "自动化任务id")
    private String automaticTaskId;

    @ZsmpModelProperty(description = "报告")
    private String reportFile;

    @ZsmpModelProperty(description = "执行日志")
    private String execLogFile;

    @ZsmpModelProperty(description = "用例状态")
    private TestcaseStatusEnum testcaseStatus;

    @ZsmpModelProperty(description = "自动化登记库类型")
    private AutomaticRecordTypeEnum automaticRecordType;

    @ZsmpModelProperty(description = "dubbo接口名")
    private String interfaceName;

    @ZsmpModelProperty(description = "结果描述")
    private String resultComment;

    public void setPriority(TestcasePriorityEnum priority) {
        this.priority = priority;
        if (null != priority) {
            this.priorityDesc = priority.getValue();
        }
    }

    public void setStatus(TestPlanCaseStatusEnum status) {
        this.status = status;
        if (null != status) {
            this.statusDesc = status.getValue();
        }
    }

    public void setType(TestcaseTypeEnum type) {
        this.type = type;
        if (null != type) {
            this.typeDesc = type.getValue();
        }
    }
}
