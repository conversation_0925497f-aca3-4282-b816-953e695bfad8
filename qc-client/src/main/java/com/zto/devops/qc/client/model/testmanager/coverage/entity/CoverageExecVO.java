package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022/9/26 14:31
 */
@Data
public class CoverageExecVO implements Serializable {

    public CoverageExecVO() {

    }

    public CoverageExecVO(String versionCode, String appId, String branchName, String execName, String execPath, String bucketName, String commitId, String creator, Long creatorId, String modifier,
                          Long modifierId, String flowLaneType, DiffTypeEnum diffType) {
        this.versionCode = versionCode;
        this.appId = appId;
        this.branchName = branchName;
        this.execName = execName;
        this.execPath = execPath;
        this.bucketName = bucketName;
        this.commitId = commitId;
        this.creator = creator;
        this.creatorId = creatorId;
        this.modifier = modifier;
        this.modifierId = modifierId;
        this.flowLaneType = flowLaneType;
        this.diffType = diffType;
    }

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * appId
     */
    private String appId;

    /**
     * 分支名称
     */
    private String branchName;

    /**
     * commitId
     */
    private String commitId;

    /**
     * 存储桶名字
     */
    private String bucketName;

    /**
     * exec路径
     */
    private String execPath;

    /**
     * exec名称
     */
    private String execName;

    private Long creatorId;

    private String creator;

    private Date gmtCreate;

    private Long modifierId;

    private String modifier;

    private Date gmtModified;

    /**
     * 发布泳道
     */
    private String flowLaneType;

    /**
     * 生成类型
     */
    private DiffTypeEnum diffType;

}
