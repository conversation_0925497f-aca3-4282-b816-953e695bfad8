package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class SceneModuleQueryReq implements Serializable {

    private static final long serialVersionUID = -3732917628804709850L;

    @GatewayModelProperty(description = "产品code", required = true)
    private String productCode;

    @GatewayModelProperty(description = "是否只要分组", required = false)
    private Boolean onlyModule;

    @GatewayModelProperty(description = "当前节点", required = false)
    private String code;

    @GatewayModelProperty(description = "父分组code", required = false)
    private String parentCode;

    @GatewayModelProperty(description = "分组/场景名称", required = false)
    private String nameOrGroup;

    @GatewayModelProperty(description = "当前操作人", required = false)
    private Long currentUserId;

    @GatewayModelProperty(description = "类型", required = false)
    private String sceneType;

    @GatewayModelProperty(description = "场景状态", required = false)
    private List<Integer> status;

    @GatewayModelProperty(description = "权限", required = false)
    private List<String> permissions;


    /**
     * 共享产品标识
     */
    @ZsmpModelProperty(description = "共享产品标识", required = false, sample = "true")
    private Boolean shareProductFlag;
    /**
     * 共享标识
     */
    @ZsmpModelProperty(description = "共享造数标识", required = false, sample = "true")
    private Boolean shareStatus;

}
