package com.zto.devops.qc.client.model.testmanager.coverage.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/22 10:55
 */
@Data
public class GenerateCoverageExecEvent extends BaseEvent implements ObservedEvent {

    private List<String> versionCodes;

    private String branchName;

    private List<String> appIds;
}
