package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AutomaticSourceGenerateReq implements Serializable {

    @GatewayModelProperty(description = "场景code")
    private String sceneCode;

    @GatewayModelProperty(description = "状态")
    private SceneInfoStatusEnum status;

    @GatewayModelProperty(description = "版本")
    private String sceneVersion;

}
