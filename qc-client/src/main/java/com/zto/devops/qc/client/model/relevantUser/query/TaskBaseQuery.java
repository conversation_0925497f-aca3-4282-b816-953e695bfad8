package com.zto.devops.qc.client.model.relevantUser.query;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 项目名称：project-parent
 * 类 名 称：TaskBaseQuery
 * 类 描 述：TODO
 * 创建时间：2021/12/16 3:35 下午
 * 创 建 人：bulecat
 */
@Data
public class TaskBaseQuery extends PageQueryBase {
    private List<RelevantUserTypeEnum> relevantUserTypes;

    private DomainEnum domain;

    private String orderField;

    private String orderType ;

    private Long userId;

    private Long modifierId;

    private List<String> status;

    private String name;

    private String businessCode;

    private List<String> projectLevel;

    private List<String> projectScale;

    private List<String> worthLevel;

    private List<String> versionCodeList;
}
