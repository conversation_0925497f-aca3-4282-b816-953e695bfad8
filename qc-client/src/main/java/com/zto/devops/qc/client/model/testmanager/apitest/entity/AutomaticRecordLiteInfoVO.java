package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@GatewayModel(description = "登记库信息(精简版)VO")
@Data
public class AutomaticRecordLiteInfoVO implements Serializable {
    private static final long serialVersionUID = -7977586171860269002L;

    @GatewayModelProperty(description = "登记库编码")
    private String code;

    @GatewayModelProperty(description = "登记库名称")
    private String name;
}
