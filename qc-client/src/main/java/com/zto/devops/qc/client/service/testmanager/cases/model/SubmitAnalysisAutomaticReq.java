package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SubmitAnalysisAutomaticReq implements Serializable {

    @ZModelProperty(description = "code", sample = "SNF994641594810368000")
    private String code;

    @ZModelProperty(description = "automaticSourceLogCode", sample = "SNF994641594810368000")
    private String automaticSourceLogCode;

    @ZModelProperty(description = "产品code", sample = "399")
    private String productCode;
}
