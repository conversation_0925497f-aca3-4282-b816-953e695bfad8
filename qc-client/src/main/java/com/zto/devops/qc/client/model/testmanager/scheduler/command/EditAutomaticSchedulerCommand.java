package com.zto.devops.qc.client.model.testmanager.scheduler.command;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditAutomaticSchedulerCommand extends AddAutomaticSchedulerCommand {

    private Boolean switchFlag;

    public EditAutomaticSchedulerCommand(String aggregateId) {
        super(aggregateId);
    }
}
