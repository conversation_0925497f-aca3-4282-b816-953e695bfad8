package com.zto.devops.qc.client.service.coverage;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.service.coverage.model.req.ListVersionInterfaceReq;
import com.zto.devops.qc.client.service.coverage.model.req.PageInterfaceCoverageInfoReq;
import com.zto.devops.qc.client.service.coverage.model.req.QueryInterfaceCoverageRateReq;
import com.zto.devops.qc.client.service.coverage.model.req.ZcatMqBodyReq;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageInfoResp;
import com.zto.devops.qc.client.service.coverage.model.resp.InterfaceCoverageRateResp;
import com.zto.devops.qc.client.service.coverage.model.resp.VersionInterfaceResp;

/**
 * <AUTHOR>
 */
public interface InterfaceCoverageService {
    PageResult<InterfaceCoverageInfoResp> queryPageInterfaceCoverageInfo(PageInterfaceCoverageInfoReq req);

    Result<InterfaceCoverageRateResp> queryInterfaceCoverageRate(QueryInterfaceCoverageRateReq req);

    Result<Boolean> syncInterfaceTestedInfo(ZcatMqBodyReq req);

    Result<VersionInterfaceResp> queryInterfaceRelatedToVersion(ListVersionInterfaceReq req);
}
