package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class GetApiTestVariableResultCommand extends BaseCommand {

    private String variableKey;

    private String variableValue;

    private VariableTypeEnum type;

    public GetApiTestVariableResultCommand(String aggregateId) {
        super(aggregateId);
    }
}
