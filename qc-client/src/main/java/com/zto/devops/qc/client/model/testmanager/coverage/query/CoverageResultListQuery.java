package com.zto.devops.qc.client.model.testmanager.coverage.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/20 16:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoverageResultListQuery extends BaseQuery {

    @GatewayModelProperty(description = "版本编码", required = true)
    private List<String> versionCodeList;

    @GatewayModelProperty(description = "应用id集合", required = true)
    private List<String> appIdList;

}
