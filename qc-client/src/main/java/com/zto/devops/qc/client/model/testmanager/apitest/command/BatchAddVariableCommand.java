package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BaseApiTestVariableReq;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class BatchAddVariableCommand extends BaseCommand {

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 所属产品名称
     */
    private String productName;

    /**
     * 变量集合
     */
    private List<BaseApiTestVariableReq> apiTestVariableList;

    /**
     * 类型：LOGIN, JDBC_CONFIG, VARIABLE
     */
    private VariableTypeEnum type;

    /**
     * 业务code
     */
    private String linkCode;

    /**
     * 变量子类型
     */
    private SubVariableTypeEnum subVariableType;

    /**
     * 请求头集合
     */
    private List<BaseApiTestVariableReq> apiTestHeaderList;

    /**
     * 场景类型
     */
    private UseCaseFactoryTypeEnum sceneType;

    public BatchAddVariableCommand(String aggregateId) {
        super(aggregateId);
    }
}
