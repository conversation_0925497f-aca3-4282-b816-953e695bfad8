package com.zto.devops.qc.client.service.excel.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReadExcelResp<T> implements Serializable {

    @GatewayModelProperty(description = "成功数量", required = false)
    private String successTotal;

    @GatewayModelProperty(description = "失败数量", required = false)
    private String failTotal;

    @GatewayModelProperty(description = "导出的内容", required = true)
    private List<T> datas;


}
