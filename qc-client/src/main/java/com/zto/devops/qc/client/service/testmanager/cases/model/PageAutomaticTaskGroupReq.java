package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ZsmpModel(description = "自动化任务执行记录请求模型（按父任务分组）")
public class PageAutomaticTaskGroupReq extends PageQueryBase implements Serializable {

    @ZModelProperty(description = "登记库code列表", sample = "['SNF987293442717515776']")
    private List<String> automaticCodeList;

    @ZModelProperty(description = "定时任务code", sample = "SNF987293442717515776")
    private String schedulerCode;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "执行结果列表", sample = "['SUBMITTED']")
    private List<AutomaticStatusEnum> statusList;

    @ZModelProperty(description = "版本code列表", sample = "['VER2302168133']")
    private List<String> versionCodeList;

    @ZModelProperty(description = "测试计划code列表", sample = "['TP240305003075']")
    private List<String> testPlanCodeList;

    @ZModelProperty(description = "触发方式列表", sample = "['TEST_PLAN']")
    private List<AutomaticTaskTrigModeEnum> trigModeList;

    @ZModelProperty(description = "执行人id列表", sample = "[5878415]")
    private List<Long> executorIdList;

    @ZModelProperty(description = "开始时间起", sample = "1711987200000")
    private Date startTimeBegin;

    @ZModelProperty(description = "开始时间止", sample = "1711987200000")
    private Date startTimeEnd;

    @ZModelProperty(description = "结束时间起", sample = "1711987200000")
    private Date finishTimeBegin;

    @ZModelProperty(description = "结束时间止", sample = "1711987200000")
    private Date finishTimeEnd;

    @ZModelProperty(description = "任务id", sample = "SNF987293442717515776")
    private String taskId;

    @ZModelProperty(description = "执行环境列表（多选）", sample = "['fat']")
    private List<String> executeEnvList;
}
