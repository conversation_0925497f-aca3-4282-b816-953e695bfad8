package com.zto.devops.qc.client.service.agent.model;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "查询版本是否包含故障注入规则接口入参")
public class IsIncludeChaosRuleReq implements Serializable {

    private static final long serialVersionUID = -5515287544758644517L;

    @ZModelProperty(description = "版本code集合", required = true, sample = "[\"VER24082000202\"]")
    List<String> versionCodes;

}
