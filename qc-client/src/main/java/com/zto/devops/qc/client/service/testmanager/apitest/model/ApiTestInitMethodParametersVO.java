package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "内置函数参数响应模型")
public class ApiTestInitMethodParametersVO implements Serializable {

    @ZsmpModelProperty(description = "参数名称")
    private String name;

    @ZsmpModelProperty(description = "参数是否必填")
    private Boolean required;

    @ZsmpModelProperty(description = "参数提示")
    private String placeholder;

    @ZsmpModelProperty(description = "参数提示")
    private String tips;

    @ZsmpModelProperty(description = "参数长度")
    private Integer length;
}
