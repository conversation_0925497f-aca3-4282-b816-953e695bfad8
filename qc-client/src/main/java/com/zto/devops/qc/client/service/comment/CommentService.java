package com.zto.devops.qc.client.service.comment;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.issue.entity.CommentVO;
import com.zto.devops.qc.client.service.comment.model.AddCommentReq;
import com.zto.devops.qc.client.service.comment.model.ListCommentsByBusinessCodeReq;
import com.zto.devops.qc.client.service.comment.model.RemoveCommentReq;

import java.util.List;


public interface CommentService {

    /**
     * 添加评论
     */
    Result<Void> addComment(AddCommentReq req);

    /**
     * 根据业务编码查询评论列表
     */
    Result<List<CommentVO>> listCommentsByBusinessCode(ListCommentsByBusinessCodeReq req);

    /**
     * 移除评论
     */
    Result<Void> removeComment(RemoveCommentReq req);

}
