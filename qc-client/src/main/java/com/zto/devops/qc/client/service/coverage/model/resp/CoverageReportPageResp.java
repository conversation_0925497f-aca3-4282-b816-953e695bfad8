package com.zto.devops.qc.client.service.coverage.model.resp;

import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageRecordVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/16 15:16
 */
@Data
public class CoverageReportPageResp implements Serializable {
    private static final long serialVersionUID = -143673372790706989L;

    @GatewayModelProperty(description = "覆盖率列表")
    private List<CoverageRecordVO> coverageRecordVOList;

    @GatewayModelProperty(description = "版本分支覆盖率")
    private Integer branchVersionRate;

    @GatewayModelProperty(description = "是否显示编辑不达标原因按钮")
    private Boolean displayButton;

}
