package com.zto.devops.qc.client.service.agent.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.agent.ChaosExceptionTypeEnum;
import com.zto.devops.qc.client.enums.agent.ChaosRuleTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "规则配置接口入参")
public class PageRuleConfigReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = -5515287544758644517L;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    String productCode;

    @ZModelProperty(description = "版本code", sample = "VER24072900127")
    String versionCode;

    @ZModelProperty(description = "应用集合", sample = "[app1,app2]")
    List<String> appIds;

    @ZModelProperty(description = "注入类型集合", sample = "[ServiceClass,DubboClient,HttpClient]")
    List<ChaosRuleTypeEnum> ruleTypes;

    @ZModelProperty(description = "注入规则集合", sample = "[FlowException,DegradeException,TimeoutException,ServerDown,CustomException]")
    List<ChaosExceptionTypeEnum> exceptionTypes;

    @ZModelProperty(description = "状态集", sample = "[0,1,2]")
    List<Integer> statusList;

    @ZModelProperty(description = "注入项", sample = "com.zto")
    String injection;

    @ZModelProperty(description = "注入名称", sample = "test")
    String injectionRuleName;

}
