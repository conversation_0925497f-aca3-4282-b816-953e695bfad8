package com.zto.devops.qc.client.model.testmanager.coverage.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class EditCoverageCommand extends BaseCommand {

    /**
     * 版本code
     */
    private String versionCode;

    /**
     * 备注
     */
    private String comment;

    /**
     * appId
     */
    private String appId;

    /**
     * 产品code
     */
    private String productCode;

    /**
     * 差异类型
     */
    private DiffTypeEnum diffType;

    public EditCoverageCommand(String aggregateId) {
        super(aggregateId);
    }
}
