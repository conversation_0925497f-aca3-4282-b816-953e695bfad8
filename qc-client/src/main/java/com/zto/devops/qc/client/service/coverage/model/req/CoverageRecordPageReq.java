package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/16 14:40
 */
@Data
@ZModel(description = "查询覆盖率报告列表入参")
public class CoverageRecordPageReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 935566555725748029L;

    @Auth(type = AuthTypeConstant.VERSION_CODE)
    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "应用名", sample = "devops-qc")
    private String appId;

    @ZModelProperty(description = "分支生成状态", sample = "SUCCEED")
    private List<String> branchStatus;

    @ZModelProperty(description = "创建人", sample = "123456")
    private Long creatorId;

    @ZModelProperty(description = "创建时间-开始", sample = "2023-12-01 00:00:00")
    private Date gmtCreateStart;

    @ZModelProperty(description = "创建时间-结束", sample = "2023-12-31 23:59:59")
    private Date gmtCreateEnd;

    @ZModelProperty(description = "排序字段 branchRecordRate|masterRecordRate", sample = "branchRecordRate")
    private String orderField;

    @ZModelProperty(description = "排序类型 ASC|DESC", sample = "ASC")
    private String orderType;

    @ZModelProperty(description = "差异类型", sample = "INCREMENT")
    private DiffTypeEnum diffType;

}
