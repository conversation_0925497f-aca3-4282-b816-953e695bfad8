package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DataCenterParameter;
import lombok.Data;

import java.util.List;

@Data
public class DataCenterRequestComponent extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    String dataCenterSceneCode;
    List<DataCenterParameter> inputParameter;
    List<DataCenterParameter> outputParameter;
    String comments="";
    String ossPath;
}
