package com.zto.devops.qc.client.service.agent.model;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "批量操作规则配置请求入参")
public class BatchOptRuleConfigReq  implements Serializable {

    private static final long serialVersionUID = -5815287544758644518L;

    @ZModelProperty(description = "规则编号集合", required = true, sample = "[1,2,3]")
    List<Long> ids;

    @ZModelProperty(description = "状态", sample = "0-停用|1-启用|2-失效|null-不操作")
    Integer status = null;

    @ZModelProperty(description = "逻辑删除", sample = "0-删除|1-正常|null-不操作")
    private Integer enable = null;
}
