package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "查询接口列表请求模型")
@Accessors(chain = true)
public class PageApiInfoReq implements Serializable {

    @ZsmpModelProperty(description = "页数", required = true)
    private int page;

    @ZsmpModelProperty(description = "数据大小", required = true)
    private int size;

    @ZsmpModelProperty(description = "产品code")
    private String productCode;

    @ZsmpModelProperty(description = "接口地址或名称")
    private String nameOrAddress;

    @ZsmpModelProperty(description = "接口code")
    private String apiCode;

    @ZsmpModelProperty(description = "数据来源，SCENE|DATACENTER")
    private String source;

    @ZsmpModelProperty(description = "接口类型：HTTP DUBBO")
    private ApiTypeEnum apiType;

    @ZsmpModelProperty(description = "接口code列表")
    private List<String> apiCodeList;
}
