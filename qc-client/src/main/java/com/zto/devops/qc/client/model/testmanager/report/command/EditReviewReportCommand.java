package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewRenewalVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class EditReviewReportCommand extends BaseReportInfoCommand {

    @GatewayModelProperty(description = "评审信息", required = true)
    private ReviewInfoVO reviewInfo;

    @GatewayModelProperty(description = "评审意见", required = false)
    private List<ReviewOpinionVO> reviewOpinions;

    @GatewayModelProperty(description = "评审更新信息", required = false)
    private List<ReviewRenewalVO> reviewRenewals;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    public EditReviewReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
