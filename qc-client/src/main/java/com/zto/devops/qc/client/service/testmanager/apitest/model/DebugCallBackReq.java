package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DebugCallBackReq implements Serializable {

    @ZModelProperty(description = "任务编号", sample = "SNF967020220159885312", required = true)
    private String taskId;

    @ZModelProperty(description = "节点code", sample = "")
    private String nodeCode;

    @ZModelProperty(description = "请求信息")
    private String requestMessage;

    @ZModelProperty(description = "响应头")
    private String responseHeader;

    @ZModelProperty(description = "响应体")
    private String responseBody;

    @ZModelProperty(description = "断言信息")
    private String assertContent;

    @ZModelProperty(description = "节点状态")
    private String nodeStatus;

    @ZModelProperty(description = "类型")
    private UseCaseFactoryTypeEnum sceneType;

    @ZModelProperty(description = "出参")
    private String outputParameter;

    @ZModelProperty(description = "入参")
    private String inputParameter;

    @ZModelProperty(description = "顺序")
    private String index ;

}
