package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTagEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.RequestMethodEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@ZsmpModel(description = "分页查询api文档Req")
@EqualsAndHashCode(callSuper = true)
@Data
public class PageApiReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 694478404895119198L;

    @ZsmpModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZsmpModelProperty(description = "应用ID列表", sample = "devops-qc")
    private List<String> appIdList;

    @ZsmpModelProperty(description = "接口类型[HTTP;DUBBO;]", sample = "HTTP")
    private List<ApiTypeEnum> apiTypeList;

    @ZsmpModelProperty(description = "请求方式[ GET; POST; PUT; DELETE; OPTIONS; PATCH;]", sample = "GET")
    private List<RequestMethodEnum> reqMethodList;

    @ZsmpModelProperty(description = "状态[删除 DELETED(0); 已发布 ONLINE(1); 已下线 OFFLINE(2);]", sample = "DELETED")
    private List<ApiTestEnableEnum> statusList;

    public List<ApiTestEnableEnum> getStatusList() {
        if (CollectionUtil.isEmpty(this.statusList)) {
            return new ArrayList<>(Arrays.asList(ApiTestEnableEnum.ONLINE, ApiTestEnableEnum.OFFLINE));
        }
        return new ArrayList<>();
    }

    private List<Integer> enableNumList;

    public List<Integer> getEnableNumList() {
        if (CollectionUtil.isNotEmpty(this.statusList)) {
            this.enableNumList = this.statusList
                    .stream()
                    .map(ApiTestEnableEnum::getCode)
                    .collect(Collectors.toList());
        }
        return this.enableNumList;
    }

    @ZsmpModelProperty(description = "接口名称或接口地址（模糊搜索）", sample = "/qc/issue/list")
    private String nameOrAddress;

    @ZsmpModelProperty(description = "文档版本", sample = "default")
    private List<String> docVersionList;

    @ZsmpModelProperty(description = "接口描述（模糊搜索）", sample = "列表")
    private String apiDesc;

    @ZsmpModelProperty(description = "编辑人名字（多选）", sample = "张一")
    private List<String> operatorNameList;

    @ZsmpModelProperty(description = "查询开始时间(更新操作)", sample = "2024-01-01 00:00:00")
    private Date startTime;

    @ZsmpModelProperty(description = "查询结束时间(更新操作)", sample = "2024-01-01 00:00:00")
    private Date endTime;

    @ZsmpModelProperty(description = "是否关联场景", sample = "false")
    private List<Boolean> sceneRelatedList;

    @ZsmpModelProperty(description = "标识列表", sample = "API_UPDATE")
    private List<ApiCaseTagEnum> tagList;
}
