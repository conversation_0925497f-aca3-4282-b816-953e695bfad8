package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap;

import java.io.Serializable;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.lines.Line;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SingleLinkBaseInfo extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    String sceneCode;
    Integer sceneVersion;
    List<ComponentBaseInfo> nodes;
    List<Line> lines;
}
