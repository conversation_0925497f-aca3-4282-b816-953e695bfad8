package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.model.testmanager.plan.entity.BatchTestPlanVO;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "场景批量加入测试计划请求模型")
public class SceneBatchToTestPlanReq implements Serializable {

    @ZModelProperty(description = "产品编号", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "场景编号集合", required = true, sample = "[SNF979388218212352000]")
    private List<String> sceneCodeList;

    @ZModelProperty(description = "测试计划集合", required = true, sample = "{\n" +
            "          \"testPlanCode\":\"TP240325003003\",\n" +
            "          \"testStage\":[\n" +
            "            \"FUNCTIONAL_TEST\"]\n" +
            "        }")
    private List<BatchTestPlanVO> testPlanList;

}
