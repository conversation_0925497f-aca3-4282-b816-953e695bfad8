package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ListRedisQueryVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZsmpModel(description = "redis列表查询响应模型")
public class ListRedisQueryResp implements Serializable {
    private static final long serialVersionUID = -4162064744980340039L;

    @ZsmpModelProperty(description = "集群id")
    private Integer clusterId;

    @ZsmpModelProperty(description = "集群名称")
    private String clusterName;

    @ZsmpModelProperty(description = "集群名称（自定义）")
    private String resourceName;

    @ZsmpModelProperty(description = "集群节点")
    private String nodes;

    public static ListRedisQueryResp buildSelf(ListRedisQueryVO vo) {
        String nodes = vo.getNodeInfo().getOrDefault("spring.redis.cluster.nodes", "");
        if (StringUtils.isBlank(nodes)) {
            return null;
        }
        return ListRedisQueryResp.builder()
                .clusterId(vo.getClusterId())
                .clusterName(vo.getClusterName())
                .resourceName(vo.getResourceName())
                .nodes(nodes)
                .build();

    }

    public static List<ListRedisQueryResp> buildList(List<ListRedisQueryVO> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return new ArrayList<>();
        }
        List<ListRedisQueryResp> resultList = new ArrayList<>(voList.size());
        voList.forEach(vo -> {
            ListRedisQueryResp resp = ListRedisQueryResp.buildSelf(vo);
            if (null != resp) {
                resultList.add(resp);
            }
        });
        return resultList;
    }
}
