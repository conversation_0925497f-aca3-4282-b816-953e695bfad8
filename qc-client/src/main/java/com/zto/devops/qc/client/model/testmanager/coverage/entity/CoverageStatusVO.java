package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoverageStatusVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "覆盖率报告编码")
    private String code;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "应用名")
    private String appId;

    @GatewayModelProperty(description = "标准值")
    private BigDecimal standardRate;

    @GatewayModelProperty(description = "分支覆盖率", required = false)
    private BigDecimal branchRecordRate;

    @GatewayModelProperty(description = "分支生成状态", required = false)
    private String branchStatus;

    @GatewayModelProperty(description = "分支报告地址", required = false)
    private String branchRecordUrl;

    @GatewayModelProperty(description = "分支不达标原因", required = false)
    private String branchRemark;

    @GatewayModelProperty(description = "分支覆盖率报告异常原因", required = false)
    private String branchRecordErrorMsg;

    @GatewayModelProperty(description = "分支创建人", required = false)
    private String branchCreator;

    @GatewayModelProperty(description = "分支创建时间", required = false)
    private String branchGmtCreate;

    @GatewayModelProperty(description = "主干覆盖率", required = false)
    private BigDecimal masterRecordRate;

    @GatewayModelProperty(description = "主干生成状态", required = false)
    private String masterStatus;

    @GatewayModelProperty(description = "主干报告地址", required = false)
    private String masterRecordUrl;

    @GatewayModelProperty(description = "主干不达标原因", required = false)
    private String masterRemark;

    @GatewayModelProperty(description = "主干覆盖率报告异常原因", required = false)
    private String masterRecordErrorMsg;

    @GatewayModelProperty(description = "主干创建人", required = false)
    private String masterCreator;

    @GatewayModelProperty(description = "主干创建时间", required = false)
    private String masterGmtCreate;

    @GatewayModelProperty(description = "备注", required = false)
    private String comment;

    @GatewayModelProperty(description = "分支版本覆盖率", required = false)
    private Integer branchVersionRate;

    @GatewayModelProperty(description = "主干版本覆盖率", required = false)
    private Integer masterVersionRate;
}
