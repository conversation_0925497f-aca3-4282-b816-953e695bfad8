package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "查询异常用例请求模型")
public class QueryApiCaseExceptionReq implements Serializable {
    private static final long serialVersionUID = -2284195261751769911L;

    @ZModelProperty(description = "上级用例code", required = true, sample = "TC231130064892")
    private String parentCaseCode;

    @ZModelProperty(description = "用例code", sample = "TC231130064892")
    private String caseCode;

    @ZModelProperty(description = "检测类型集合", sample = "0")
    private List<Integer> generateRulesList;

    @ZModelProperty(description = "接口用例状态", required = true, sample = "edit")
    private ApiCaseStatusEnum status;

    @ZModelProperty(description = "变量名", sample = "reqBody")
    private String key;

    @ZModelProperty(description = "排序字段:apiConfigType/key", sample = "apiConfigType")
    private String orderField;

    @ZModelProperty(description = "排序顺序：ASC/DESC", sample = "ASC")
    private String orderType;

    @ZModelProperty(description = "页数")
    private int page;

    @ZModelProperty(description = "数据大小")
    private int size;

    public int getSize() {
        return 0 == this.size ? 50 : this.size;
    }
}
