package com.zto.devops.qc.client.service.issue.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlowSqlToIssueResp implements Serializable {

    @GatewayModelProperty(description = "缺陷编号", required = false)
    private String issueCode;

}
