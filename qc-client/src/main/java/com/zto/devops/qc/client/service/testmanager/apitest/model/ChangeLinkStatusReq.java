package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "变更场景/链路状态请求模型")
public class ChangeLinkStatusReq implements Serializable {

    @ZsmpModelProperty(description = "场景code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "场景版本")
    private Integer sceneVersion;

    @ZsmpModelProperty(description = "链路code")
    private String linkMapCode;

    @ZsmpModelProperty(description = "停用/启用", required = true)
    private Boolean enable;

    @ZsmpModelProperty(description = "类型")
    private UseCaseFactoryTypeEnum sceneType;

    @ZsmpModelProperty(description = "产品code")
    private String productCode;

}
