package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class TmAccessReportCommand extends BaseReportInfoCommand {
    public TmAccessReportCommand(String aggregateId) {
        super(aggregateId);
    }

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date planPresentationDate;

    @GatewayModelProperty(description = "实际提测时间", required = false)
    private Date actualPresentationDate;

    @GatewayModelProperty(description = "提测延期天数", required = false)
    private Integer delayDays;

    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;
}
