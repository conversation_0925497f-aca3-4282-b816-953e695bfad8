package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "变更用例责任人请求模型")
@Data
public class ChangeTestcaseDutyUserReq implements Serializable {

    @ZModelProperty(description = "用例唯一标识", required = true, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "责任人", required = false, sample = "责任人")
    private String dutyUser;

    @ZModelProperty(description = "责任人id", required = false, sample = "5984549")
    private Long dutyUserId;
}
