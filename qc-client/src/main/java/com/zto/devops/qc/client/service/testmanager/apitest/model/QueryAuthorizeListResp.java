package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.QueryAuthorizeListVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryAuthorizeListResp implements Serializable {

    private static final long serialVersionUID = 3132580583947451190L;

    @ZsmpModelProperty(description = "是否有按钮权限")
    private Boolean hasPermission;

    @ZsmpModelProperty(description = "授权列表list")
    private List<QueryAuthorizeListVO> list;

}
