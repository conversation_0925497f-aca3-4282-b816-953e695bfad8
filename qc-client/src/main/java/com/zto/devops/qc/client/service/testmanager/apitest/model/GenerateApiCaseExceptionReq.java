package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiFieldConfigVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "生成异常用例数据请求模型")
public class GenerateApiCaseExceptionReq implements Serializable {

    @ZModelProperty(description = "上级用例code", required = true, sample = "TC231130064892")
    private String parentCaseCode;

    @ZModelProperty(description = "接口用例状态", required = true, sample = "edit")
    private ApiCaseStatusEnum status;

    @ZModelProperty(description = "接口字段配置列表")
    private List<ApiFieldConfigVO> apiFieldConfigList;
}
