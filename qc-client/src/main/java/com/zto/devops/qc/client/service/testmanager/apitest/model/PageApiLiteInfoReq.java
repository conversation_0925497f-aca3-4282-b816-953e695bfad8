package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestEnableEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@ZsmpModel(description = "查询api文档（精简版）Req")
@Data
public class PageApiLiteInfoReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 6258445312981363065L;

    @ZModelProperty(description = "产品code",required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "根据名字搜索", required = false, sample = "true")
    private Boolean searchName;

    @ZModelProperty(description = "接口名称或接口地址（模糊搜索）", required = false, sample = "333")
    private String nameOrAddress;

    @ZModelProperty(description = "apiCode", required = false, sample = "1234567890")
    private String apiCode;

    @ZModelProperty(description = "状态[删除 DELETED(0); 已发布 ONLINE(1); 已下线 OFFLINE(2);]", required = false, sample = "DELETED")
    private List<ApiTestEnableEnum> statusList;

    public List<Integer> getStatusList() {
        List<Integer> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(this.statusList)) {
            statusList.forEach(x -> result.add(x.getCode()));
        }
        return result;
    }

}
