package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TestPlanCaseListReq implements Serializable {
    private static final long serialVersionUID = -1207646640627840719L;

    @ZModelProperty(description = "产品Code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "计划Code", required = true, sample = "TP240305003075")
    private String code;

    @ZModelProperty(description = "测试阶段", required = false, sample = "['SMOKE_TEST']")
    private List<TestPlanStageEnum> testPlanStageList;

    @ZModelProperty(description = "父节点code",required = false, sample = "TP240305003075")
    private String parentCode;

    @ZModelProperty(description = "用例类型", required = false, sample = "['MANUAL']")
    private List<TestcaseTypeEnum> caseTypeList;

    @ZModelProperty(description = "搜索code或名称", required = false, sample = "TP240305003075")
    private String codeOrTitle;

    @ZModelProperty(description = "执行结果", required = false, sample = "['PASSED']")
    private List<TestPlanCaseStatusEnum> statusList;

    @ZModelProperty(description = "执行人", required = false, sample = "[5878415]")
    private List<Long> executorIdList;

    @ZModelProperty(description = "用例等级", required = false, sample = "['MIDDLE']")
    private List<TestcasePriorityEnum> priorityList;

    @ZModelProperty(description = "自动化节点类型 ", required = false, sample = "['TestPlan']")
    private List<AutomaticNodeTypeEnum> nodeTypeList;

    @ZModelProperty(description = "框架类型", required = false, sample = "['JMETER']")
    private List<AutomaticRecordTypeEnum> automaticTypeList;

    @ZModelProperty(description = "用例标签", required = false, sample = "[]")
    private List<String> tagList;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "[0]")
    private List<Boolean> setCoreList;
}
