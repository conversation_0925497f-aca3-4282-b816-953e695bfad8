package com.zto.devops.qc.client.model.testmanager.apitest.command;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditPreDataCommand extends EditSceneInfoCommand {

    private String productCode;

    private String sceneName;

    private String sceneInfoDesc;

    private String sceneFrontData;

    private String sceneBackData;

    private List<Integer> dbIds;

    private String parentCode;

    private String sceneType;

    public EditPreDataCommand(String aggregateId) {
        super(aggregateId);
    }
}
