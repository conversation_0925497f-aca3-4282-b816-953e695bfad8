package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTaskRespTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DebugCallBackLogReq implements Serializable {

    private static final long serialVersionUID = 4327526265159612629L;

    @GatewayModelProperty(description = "任务编号")
    private String taskId;

    @GatewayModelProperty(description = "jenkinsBuildId")
    private String buildId;

    @GatewayModelProperty(description = "回调类型")
    private DebugTaskRespTypeEnum type;

    @GatewayModelProperty(description = "异常原因", required = false)
    private String exceptionReason;

}
