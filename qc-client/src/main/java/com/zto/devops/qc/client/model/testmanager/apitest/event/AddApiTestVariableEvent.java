package com.zto.devops.qc.client.model.testmanager.apitest.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddApiTestVariableEvent extends BaseEvent implements ActionEvent {

    private String productCode;

    private String productName;

    private String linkCode;

    private String variableCode;

    private String variableName;

    private String variableKey;

    private String variableValue;

    private VariableTypeEnum type;

    private String variableStatus;

    private Integer sceneType;

    private SubVariableTypeEnum subVariableType;

    private Integer loginValidTime;

    @Override
    public String action() {
        return "新增变量";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
