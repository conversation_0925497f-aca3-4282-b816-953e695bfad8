package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSON;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.DebugNodeInfoVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZsmpModel(description = "查询调试节点详情响应模型")
public class QueryDebugNodeResultResp implements Serializable {
    private static final long serialVersionUID = -5884318655749643082L;

    @ZsmpModelProperty(description = "请求信息")
    private String requestMessage;

    @ZsmpModelProperty(description = "响应头")
    private String responseHeader;

    @ZsmpModelProperty(description = "响应体")
    private String responseBody;

    @ZsmpModelProperty(description = "断言结果")
    private String assertContent;

    @ZsmpModelProperty(description = "节点或者线的code", required = true)
    private String linkComponentCode;

    @ZsmpModelProperty(description = "类型：NODE;LINE", required = true)
    private String linkComponentType;

    @ZsmpModelProperty(description = "是否调试历史")
    private Boolean historyFlag = true;

    @ZsmpModelProperty(description = "出参")
    private String output;

    public static QueryDebugNodeResultResp buildSelf(QueryDebugNodeResultReq req, String linkStr) {
        List<DebugNodeInfoVO> nodeInfoList = JSON.parseArray(linkStr, DebugNodeInfoVO.class);
        if (CollectionUtils.isEmpty(nodeInfoList)) {
            return new QueryDebugNodeResultResp();
        }
        for (DebugNodeInfoVO node : nodeInfoList) {
            if (node.getProductCode().equals(req.getProductCode())
                    && node.getSceneCode().equals(req.getSceneCode())
                    && node.getLinkComponentCode().equals(req.getNodeCode())) {
                return QueryDebugNodeResultResp.builder()
                        .requestMessage(node.getRequestMessage())
                        .responseBody(node.getResponseBody())
                        .responseHeader(node.getResponseHeader())
                        .assertContent(node.getAssertContent())
                        .linkComponentCode(node.getLinkComponentCode())
                        .linkComponentType(node.getLinkComponentType())
                        .output(node.getOutput())
                        .historyFlag(Boolean.TRUE)
                        .build();
            }
        }
        return new QueryDebugNodeResultResp();
    }
}
