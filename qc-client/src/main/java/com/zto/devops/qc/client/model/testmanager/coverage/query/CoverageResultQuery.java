package com.zto.devops.qc.client.model.testmanager.coverage.query;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/20 16:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoverageResultQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @GatewayModelProperty(description = "报告类型", required = true)
    private ReportType reportType;

    @GatewayModelProperty(description = "应用id集合", required = true)
    private List<String> appIdList;

}
