package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ListExecuteCaseReq extends BaseQuery implements Serializable {

    @ZModelProperty(description = "执行人id", sample = "['5984549']")
    private List<String> executorIdList;

    @ZModelProperty(description = "自动化执行详情code", sample = "SNF987293442717515776")
    private String code;

    @ZModelProperty(description = "运行结果", sample = "['PASSED']")
    private List<TestPlanCaseStatusEnum> status;

    @ZModelProperty(description = "开始时间起", sample = "1711987200000")
    private Date startTimeBegin;

    @ZModelProperty(description = "开始时间止", sample = "1711987200000")
    private Date startTimeEnd;

    @ZModelProperty(description = "结束时间起", sample = "1711987200000")
    private Date finishTimeBegin;

    @ZModelProperty(description = "结束时间止", sample = "1711987200000")
    private Date finishTimeEnd;
}
