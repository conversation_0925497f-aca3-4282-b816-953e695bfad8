package com.zto.devops.qc.client.model.testmanager.apitest.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MovePreDataModuleEvent extends BaseEvent implements ActionEvent {

    private String productCode;

    private String code;

    private String parentCode;

    private String parentTestcaseCode;

    private SceneIndexTypeEnum sceneIndexType;

    private String sceneType;

    private String testcaseCode;

    @Override
    public String action() {
        return "移动分组或造数";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
