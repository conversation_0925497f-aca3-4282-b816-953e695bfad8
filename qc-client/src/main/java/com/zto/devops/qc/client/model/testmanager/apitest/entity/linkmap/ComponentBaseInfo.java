package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.LinkMapTypeEnum;
import lombok.Data;

@Data
public class ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    String code;
    String name;
    LinkMapTypeEnum type;
    Boolean displaySampling = false; //设置为true的话，如果是采样器，则不会在jmeterhtml报告中被展示，非采样器及非jmeter组件此字段都无意义
    Boolean enable = true;
}
