package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.testmanager.report.entity.PublishVersionVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
public class TmOnlineSmokeReportDetailResp extends BasicInfoResp{
    private static final long serialVersionUID = -8045207557670727988L;

    @ZModelProperty(description = "是否包含zui应用（true：包含；false：不包含）", required = true)
    private Boolean zuiFlag;

    @ZModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "报告类型", required = false)
    private ReportType reportType;

    @ZModelProperty(description = "报告类型描述", required = false)
    private String reportTypeDesc;

    @ZModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @ZModelProperty(description = "实际上线时间", required = false)
    private Date actualPublishDate;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false)
    private Integer delay;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false)
    private String delayDesc;

    @ZModelProperty(description = "计划上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "延期验收期间发版记录", required = false)
    private List<PublishVersionVO> publishRecordList;

    public String getReportTypeDesc() {
        return Objects.isNull(this.reportType) ? "" : this.reportType.getValue();
    }
}
