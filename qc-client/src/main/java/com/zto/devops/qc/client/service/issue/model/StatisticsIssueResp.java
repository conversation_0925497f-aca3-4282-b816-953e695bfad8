package com.zto.devops.qc.client.service.issue.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class StatisticsIssueResp implements Serializable {

    //所属产品code
    private String productCode;

    /**
     * 紧急优先级缺陷修复时长(秒)
     */
    private Long urgencyRepairTime = 0L;

    /**
     * 高优先级缺陷修复时长(秒)
     */
    private Long highRepairTime = 0L;

    /**
     * 中优先级缺陷开修复长(秒)
     */
    private Long middleRepairTime = 0L;

    /**
     * 紧急优先级缺陷数量
     */
    private Long urgencyNum = 0L;

    /**
     * 高优先级缺陷数量
     */
    private Long highNum = 0L;

    /**
     * 中优先级缺陷数量
     */
    private Long middleNum = 0L;

    /**
     * 低优先级缺陷数量
     */
    private Long lowNum = 0L;

    /**
     * 紧急优先级缺陷按期修复数量
     */
    private Long urgencyPunctualNum = 0L;

    /**
     * 高优先级缺陷按期修复数量
     */
    private Long highPunctualNum = 0L;

    /**
     * 中优先级缺陷按期修复数量
     */
    private Long middlePunctualNum = 0L;

    /**
     * 紧急优先级缺陷reopen数量
     */
    private Long urgencyReopenNum = 0L;

    /**
     * 高优先级缺陷reopen数量
     */
    private Long highReopenNum = 0L;

    /**
     * 中优先级缺陷reopen数量
     */
    private Long middleReopenNum = 0L;

    /**
     * 低优先级缺陷reopen数量
     */
    private Long lowReopenNum = 0L;

    /**
     * 紧急优先级缺陷有效数量
     */
    private Long urgencyEffectiveNum = 0L;

    /**
     * 高优先级缺陷有效数量
     */
    private Long highEffectiveNum = 0L;

    /**
     * 中优先级缺陷有效数量
     */
    private Long middleEffectiveNum = 0L;

    /**
     * 低优先级缺陷有效数量
     */
    private Long lowEffectiveNum = 0L;

    /**
     * 需求问题数量
     */
    private Long requirementIssueNum = 0L;

    /**
     * 生产缺陷数量
     */
    private Long produceIssueNum = 0L;
}
