package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "用例模块树请求模型")
@Data
public class ListTestcaseModuleReq implements Serializable {
    private static final long serialVersionUID = 5935441505194055134L;

    @ZModelProperty(description = "类型 MANUAL AUTO", required = true, sample = "MANUAL")
    private TestcaseTypeEnum type;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "关联计划用例标识", required = false, sample = "0")
    private Boolean planPattern;

    @ZModelProperty(description = "测试计划code", required = false, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "测试阶段", required = false, sample = "SMOKE_TEST")
    private TestPlanStageEnum testStage;

    @ZModelProperty(description = "菜单标识", required = false, sample = "0")
    private Boolean menuPattern;

    @ZModelProperty(description = "版本code", required = false, sample = "VER2302168133")
    private String versionCode;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;

    @ZModelProperty(description = "是否用例工厂", required = false, sample = "0")
    private Boolean factoryPattern;
}
