
package com.zto.devops.qc.client.model.testmanager.scheduler.entity;

import com.zto.devops.framework.client.simple.User;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AutomaticSchedulerDetailVO implements Serializable {

    private static final long serialVersionUID = -7045306141778871289L;

    @ZsmpModelProperty(description = "任务code")
    private String schedulerCode;

    @ZsmpModelProperty(description = "任务名称")
    private String schedulerName;

    @ZsmpModelProperty(description = "所属产品")
    private String productCode;

    @ZsmpModelProperty(description = "crontab表达式")
    private String crontab;

    @ZsmpModelProperty(description = "运行空间")
    private String executeEnv;

    @ZsmpModelProperty(description = "运行空间tag，用于场景用例执行")
    private String executeTag;

    @ZsmpModelProperty(description = "运行空间Code")
    private String executeSpaceCode;

    @ZsmpModelProperty(description = "是否生成覆盖率")
    private Boolean coverageFlag;

    @ZsmpModelProperty(description = "是否消息通知")
    private Boolean messageFlag;

    @ZsmpModelProperty(description = "抄送人列表")
    private List<User> ccList;

}
