package com.zto.devops.qc.client.model.testmanager.report.entity;

import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.report.AutoExecuteResult;
import com.zto.devops.qc.client.enums.report.ReportButtonEnum;
import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testPlan.TestPlanStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportEditFieldEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SimplePlanTestVO;
import com.zto.devops.qc.client.model.report.entity.SimpleReportVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：BaseReqportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/10 5:31 下午
 * 创 建 人：bulecat
 */

@Data
public class BaseReportInfoVO implements Serializable {

    @GatewayModelProperty(description = "编号", required = false)
    private String reportCode;

    @GatewayModelProperty(description = "报告名称", required = true)
    private String reportName;

    @GatewayModelProperty(description = "报告类型", required = false)
    private ReportType reportType;

    @GatewayModelProperty(description = "报告类型描述", required = false)
    private String reportTypeDesc;

    @GatewayModelProperty(description = "计划编号", required = false)
    private String planCode;

    @GatewayModelProperty(description = "计划名称", required = false)
    private String planName;

    @GatewayModelProperty(description = "关联计划编号", required = false)
    private String relationPlanCode;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "版本名称", required = false)
    private String versionName;

    @GatewayModelProperty(description = "部门id", required = false)
    private Long deptId;

    @GatewayModelProperty(description = "部门名称", required = false)
    private String deptName;

    private Product product;

    @GatewayModelProperty(description = "所属产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "所属产品名称", required = false)
    private String productName;

    @GatewayModelProperty(description = "产品类型", required = false)
    private String productSource;

    @GatewayModelProperty(description = "产品类型", required = false)
    private String productSourceDesc;

    @GatewayModelProperty(description = "产品负责人id", required = false)
    private Long productOwnerId;

    @GatewayModelProperty(description = "产品负责人姓名", required = false)
    private String productOwnerName;

    @GatewayModelProperty(description = "报告人Id", required = false)
    private Long reportUserId;

    @GatewayModelProperty(description = "报告人姓名", required = false)
    private String reportUserName;

    @GatewayModelProperty(description = "开发人数", required = false)
    private Integer developerCount;

    @GatewayModelProperty(description = "测试人数", required = false)
    private Integer testerCount;

    @GatewayModelProperty(description = "总体测试结果", required = true)
    private TmTestResultEnum testResult;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private Date startDate;

    @GatewayModelProperty(description = "版本开始时间", required = false)
    private String startDay;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date planPresentationDate;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private String presentationDay;

    @GatewayModelProperty(description = "实际提测时间", required = false)
    private Date actualPresentationDate;

    @GatewayModelProperty(description = "实际提测时间上午、下午", required = false)
    private String actualPresentationDay;

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date planApprovalExitDate;

    @GatewayModelProperty(description = "实际准出时间", required = false)
    private Date actualApprovalExitDate;

    @GatewayModelProperty(description = "实际准出时间 --上下午", required = false)
    private String actualApprovalExitDay;

    @GatewayModelProperty(description = "实际准出时间 --上下午", required = false)
    private String planApprovalExitDay;

    @GatewayModelProperty(description = "计划上线时间", required = false)
    private Date planOnlineDate;

    @GatewayModelProperty(description = "计划上线时间", required = false)
    private String planOnlineDay;

    @GatewayModelProperty(description = "实际上线时间", required = false)
    private Date actualPublishDate;

    @GatewayModelProperty(description = "实际测试开始时间", required = false)
    private Date actualTestStart;

    @GatewayModelProperty(description = "实际测试开始时间--上下午", required = false)
    private String actualTestStartDay;

    @GatewayModelProperty(description = "实际测试结束时间", required = false)
    private Date actualTestEnd;

    @GatewayModelProperty(description = "实际测试结束时间-- 上下午", required = false)
    private String actualTestEndDay;

    @GatewayModelProperty(description = "验收开始时间", required = false)
    private Date checkStartDate;

    @GatewayModelProperty(description = "验收结束时间", required = false)
    private Date checkEndDate;

    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

    @GatewayModelProperty(description = "自动化测试结果-enum", required = false)
    private AutoExecuteResult autoTestResult;

    @GatewayModelProperty(description = "自动化测试结果-enum", required = false)
    private String autoTestResultDesc;

    @GatewayModelProperty(description = "安全测试人ID", required = false)
    private Long securityUserId;

    @GatewayModelProperty(description = "安全测试人名称", required = false)
    private String securityUserName;

    @GatewayModelProperty(description = "相关报告", required = false)
    private List<SimpleReportVO> reports;

    @GatewayModelProperty(description = "相关计划", required = false)
    private List<SimplePlanTestVO> plans;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<EmailMemberVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<EmailMemberVO> ccUsers;

    @GatewayModelProperty(description = "安全测试结果", required = false)
    private SecurityTestResult securityTestResult ;

    @GatewayModelProperty(description = "安全测试结果code", required = false)
    private String securityTestResultCode;

    @GatewayModelProperty(description = "安全测试结果描述", required = false)
    private String securityTestResultDesc;

    @GatewayModelProperty(description = "可操作按钮", required = false)
    private ReportButtonEnum buttonVOS;

    @GatewayModelProperty(description = "可编辑字段", required = false)
    private List<ReportEditFieldEnum> fieldVOS;

    @GatewayModelProperty(description = "预览邮件信息", required = false)
    private String preview;

   /* @GatewayModelProperty(description = "抄送人", required = false)
    private List<QcUserNoticeVO> ccNotice;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<QcUserNoticeVO> recipientNotice;*/

    @GatewayModelProperty(description = "状态", required = false)
    private TestPlanStatusEnum status;
    @GatewayModelProperty(description = "状态", required = false)
    private String statusDesc;

    // 测试人员
    private List<User> testUser;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    @GatewayModelProperty(description = "延期 -1 否，1 是", required = false)
    private Integer delay;

    @GatewayModelProperty(description = "延期 -1 否，1 是", required = false)
    private String delayDesc;

    @GatewayModelProperty(description = "邮件发送时间", required = false)
    private Date sendTime;

    @GatewayModelProperty(description = "安全计划code", required = false)
    private String safePlanCode;

    @GatewayModelProperty(description = "是否需要用户体验测试", required = false)
    private Boolean needUiTest;

    @GatewayModelProperty(description = "用户体验测试结果", required = false)
    private UiTestResultEnum uiTestResult;
}
