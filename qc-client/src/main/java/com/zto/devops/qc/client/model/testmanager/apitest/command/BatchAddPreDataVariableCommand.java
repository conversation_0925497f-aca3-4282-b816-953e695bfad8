package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.devops.qc.client.service.testmanager.apitest.model.BasePreDataVariableReq;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Setter
@Getter
public class BatchAddPreDataVariableCommand extends BaseCommand {
    private static final long serialVersionUID = 4179942059856177773L;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 所属产品名称
     */
    private String productName;

    /**
     * 入参变量集合
     */
    private List<BasePreDataVariableReq> inputParameter;

    /**
     * 出参变量集合
     */
    private List<BasePreDataVariableReq> outputParameter;

    /**
     * 类型：SSO, JDBC_CONFIG, VARIABLE
     */
    private VariableTypeEnum type;

    /**
     * 业务code
     */
    private String linkCode;

    public BatchAddPreDataVariableCommand(String aggregateId) {
        super(aggregateId);
    }

    public List<BasePreDataVariableReq> getParameterList() {
        List<BasePreDataVariableReq> variableParameterList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(this.getInputParameter())) {
            variableParameterList.addAll(this.getInputParameter());
        }
        if (CollectionUtils.isNotEmpty(this.getOutputParameter())) {
            variableParameterList.addAll(this.getOutputParameter());
        }
        return variableParameterList;
    }

    /**
     * 校验变量名是否重复
     *
     * @return true：重复；false：不重复；
     */
    public boolean checkDuplicateKey() {
        List<BasePreDataVariableReq> parameterList = this.getParameterList();
        if (CollectionUtils.isEmpty(parameterList)) {
            return false;
        }
        Set<String> variableKeySet = parameterList.stream().map(BasePreDataVariableReq::getVariableKey).collect(Collectors.toSet());
        return variableKeySet.size() != parameterList.size();
    }
}
