package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class UserCookieMapVO implements Serializable {

    private Map<String, String> resultMap;

    public UserCookieMapVO(String value) {
        Map<String, String> userCookieMap = new HashMap<>();
        userCookieMap.put("Cookie", value);
        this.resultMap = userCookieMap;
    }

    public UserCookieMapVO(Map<String, String> userCookieMap) {
        this.resultMap = userCookieMap;
    }

}
