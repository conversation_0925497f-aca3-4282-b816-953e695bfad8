package com.zto.devops.qc.client.service.issue.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RemoveIssueReq implements Serializable {

    @ZModelProperty(description = "编号", required = true, sample = "ISS230303008063")
    private String code;

    @ZModelProperty(description = " 原因 + 备注 (JSON)", required = false, sample = "原因/备注")
    private String content;

}
