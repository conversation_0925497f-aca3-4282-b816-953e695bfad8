package com.zto.devops.qc.client.service.agent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.service.agent.model.*;

import java.util.List;
import java.util.Map;

public interface AgentService {

    Result<JSONArray> queryExceptionInfo(ExceptionInfoReq req);

    Result<Map<String, Object>> pullConf(RuleConfigReq req);

    Result<RuleConfigVO> queryConfById(RuleConfigReq req);

    Result<Boolean> newConf(RuleConfigReq req);

    Result<Boolean> modifyConf(RuleConfigReq req);

    Result<Boolean> batchOptConf(BatchOptRuleConfigReq req);

    Result<Boolean> removeConf(RuleConfigReq req);

    Result<Void> delete(RuleConfigReq req);

    Result<JSONObject> queryDependencyService(DependencyQueryReq req);

//    Result<JSONArray> queryMethodsByDependService(DependencyServiceReq req);

    Result<Map<String, List<Map<String, String>>>> getChaosExceptionType();

    Result<List<String>> isIncludeChaosRule(IsIncludeChaosRuleReq req);

    PageResult<RuleConfigVO> pageQueryConf(PageRuleConfigReq req);

}
