package com.zto.devops.qc.client.model.testmanager.scheduler.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AutomaticSchedulerExecutionCommand extends BaseCommand {

    private String preCode;

    private AutomaticTaskTrigModeEnum trigMode;


    public AutomaticSchedulerExecutionCommand(String aggregateId) {
        super(aggregateId);
    }

}
