package com.zto.devops.qc.client.service.testmanager.email.model;

import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EmailResp implements Serializable {

    @GatewayModelProperty(description = "邮件code")
    private String emailCode;

    @GatewayModelProperty(description = "邮件名称")
    private String emailName;

    @GatewayModelProperty(description = "报告/计划code")
    private String businessCode;

    @GatewayModelProperty(description = "邮件类型")
    private EmailTypeEnum emailType;

    @GatewayModelProperty(description = "邮件类型描述")
    private String emailTypeDesc;

    @GatewayModelProperty(description = "版本名称")
    private String versionName;

    @GatewayModelProperty(description = "版本Code")
    private String versionCode;

    @GatewayModelProperty(description = "产品Code")
    private String productCode;

    @GatewayModelProperty(description = "产品名称")
    private String productName;

    @GatewayModelProperty(description = "创建时间")
    private Date gmtCreate;

    @GatewayModelProperty(description = "更新时间")
    private Date gmtModified;

    @GatewayModelProperty(description = "创建人")
    private String creator;

    @GatewayModelProperty(description = "创建人id")
    private String creatorId;

    @GatewayModelProperty(description = "更新人")
    private String modifier;

    @GatewayModelProperty(description = "更新人id")
    private String modifierId;

    @GatewayModelProperty(description = "发送人名字")
    private String sender;

    @GatewayModelProperty(description = "发送人id")
    private String senderId;

    @GatewayModelProperty(description = "发送时间")
    private Date sendDate;

    @GatewayModelProperty(description = "数据来源")
    private String source;
}
