package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.DebugNodeInfoVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class SaveDebugInfoCommand extends BaseCommand {
    private static final long serialVersionUID = -549341611960539874L;

    /**
     * code
     */
    private String recordCode;

    /**
     * 所属产品code
     */
    private String productCode;

    /**
     * 场景code
     */
    private String sceneCode;

    /**
     * 桶名称
     */
    private String bucketName;

    /**
     * 调试信息oss地址
     */
    private String logOssPath;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 调试方式
     */
    private String debugType;

    /**
     * 本次任务最终状态
     */
    private String status;

    /**
     * 历史调试日志
     */
    private String debugLog;

    /**
     * 执行时间
     */
    private Date gmtCreate;

    /**
     * 执行人
     */
    private String creator;

    /**
     * 节点信息
     */
    private List<DebugNodeInfoVO> linkBaseInfo;

    public SaveDebugInfoCommand(String aggregateId) {
        super(aggregateId);
    }
}
