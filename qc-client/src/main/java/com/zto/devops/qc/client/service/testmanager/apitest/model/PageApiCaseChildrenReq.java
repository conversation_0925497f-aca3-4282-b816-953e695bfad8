package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@GatewayModel(description = "分页查询api子用例Req")
@Data
public class PageApiCaseChildrenReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = -926860171082938946L;

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "父用例code")
    private String caseCode;

    @GatewayModelProperty(description = "用例名(模糊搜索)", required = false)
    private String caseName;
    public String getCaseName() {
        if (StringUtils.isNotBlank(this.caseName)) {
            return this.caseName
                    .replaceAll("/", "//")
                    .replaceAll("%", "/%")
                    .replaceAll("_", "/_");
        }
        return this.caseName;
    }

    @GatewayModelProperty(description = "最近一次执行结果（多选）", required = false)
    private List<TestPlanCaseStatusEnum> testResultList;
}
