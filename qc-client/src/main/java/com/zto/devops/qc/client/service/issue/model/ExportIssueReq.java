package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.framework.client.enums.OrderByEnum;
import com.zto.devops.framework.client.query.ExpQueryBase;
import com.zto.devops.qc.client.enums.issue.*;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ExportIssueReq extends ExpQueryBase implements Serializable {

    @ZModelProperty(description = "是否有效缺陷",sample = "true")
    private Boolean validFlag;

    @ZModelProperty(description = "当前用户id", required = false, sample = "5305175")
    private Long currentUserId;

    @ZModelProperty(description = "缺陷名称或编号", required = false, sample = "缺陷名称")
    private String codeOrTitle;

    @ZModelProperty(description = "缺陷状态", required = false, sample = "['WAIT_FIX']")
    private List<IssueStatus> statusList;

    @ZModelProperty(description = "缺陷优先级", required = false, sample = "['MIDDLE']")
    private List<IssuePriority> priorityList;

    @ZModelProperty(description = "与我相关", required = false, sample = "['HANDLED_USER']")
    private List<RelatedToMeEnum> relatedList;

    @ZModelProperty(description = "当前处理人", required = false, sample = "[5305175]")
    private List<Long> handleUserIdList;

    @ZModelProperty(description = "开发人员", required = false, sample = "[5305175]")
    private List<Long> developUserIdList;

    @ZModelProperty(description = "测试人员", required = false, sample = "[5305175]")
    private List<Long> testUserIdList;

    @ZModelProperty(description = "发现版本", required = false, sample = "['VER2302168133']")
    private List<String> findVersionList;

    @ZModelProperty(description = "修复的版本", required = false, sample = "['VER2302168133']")
    private List<String> fixVersionList;

    @ZModelProperty(description = "关联需求", required = false, sample = "['FN240416004023']")
    private List<String> relatedRequireList;

    @ZModelProperty(description = "关联项目", required = false, sample = "['399']")
    private List<String> relatedProductList;

    @ZModelProperty(description = "关联迭代", required = false, sample = "['SPT24050600102']")
    private List<String> sprintCode;

    @ZModelProperty(description = "Bug根源", required = false, sample = "['FUNCTIONAL_DEVELOPMENT_BUG']")
    private List<IssueRootCause> rootCauseList;

    @ZModelProperty(description = "Bug类别", required = false, sample = "['FUNCTION_BUG']")
    private List<IssueType> issueTypeList;

    @ZModelProperty(description = "测试方法", required = false, sample = "['FUNCTION_TEST']")
    private List<IssueTestMethod> testMethodList;

    @ZModelProperty(description = "重现概率", required = false, sample = "['OCCASIONALLY']")
    private List<IssueRepetitionRate> repetitionRateList;

    @ZModelProperty(description = "发现阶段", required = false, sample = "['TEST_STAGE']")
    private List<IssueFindStage> findStageList;

    @ZModelProperty(description = "发现环境", required = false, sample = "['FAT_EVN']")
    private List<IssueFindEnv> findEnvList;

    @ZModelProperty(description = "报告人", required = false, sample = "[5305175]")
    private List<Long> findUserIdList;

    @ZModelProperty(description = "标签", required = false, sample = "[]")
    private List<String> tagName;

    @ZModelProperty(description = "创建时间", required = false, sample = "1711987200000")
    private Date createTimeStart;

    @ZModelProperty(description = "创建时间", required = false, sample = "1711987200000")
    private Date createTimeEnd;

    @ZModelProperty(description = "关闭时间", required = false, sample = "1711987200000")
    private Date closeTimeStart;

    @ZModelProperty(description = "关闭时间", required = false, sample = "1711987200000")
    private Date closeTimeEnd;

    @ZModelProperty(description = "缺陷标签code集合", required = false, sample = "[]")
    private List<String> tagList;

    @ZModelProperty(description = "更新人", required = false, sample = "[5305175]")
    private List<Long> updateUserIdList;

    /**
     * 审查状态  1已审查 0未审查
     */
    @ZModelProperty(description = "审查状态", required = false, sample = "[0]")
    private List<Boolean> examination;
    /**
     * 测试遗漏  1为是 0为否
     */
    @ZModelProperty(description = "测试遗漏", required = false, sample = "[0]")
    private List<Boolean> testOmission;
    /**
     * 代码缺陷  1为是 0为否
     */
    @ZModelProperty(description = "代码缺陷", required = false, sample = "[0]")
    private List<Boolean> codeDefect;

    @ZModelProperty(description = "应用类型", required = false, sample = "[WEB]")
    private List<IssueApplicationType> applicationTypeList;

    @ZModelProperty(description = "版本类型", required = false)
    private String versionType;

    @ZModelProperty(description = "拒绝原因集合", sample = "['NO_ISSUE','UNABLE_TO_REPAIRE']", required = false)
    private List<RefuseReason> refuseReasonList;

    @ZModelProperty(description = "排序字段", required = false, sample = "")
    private String orderField;

    @ZModelProperty(description = "排序方式", required = false, sample = "DESC")
    private OrderByEnum orderType = OrderByEnum.DESC;

    @ZModelProperty(description = "抄送人id", required = false, sample = "[5305175]")
    private List<Long> ccUserIdList;

    @ZModelProperty(description = "甬道", required = false, sample = "[DOING]")
    private List<LaneStatusEnum> lane;

    @ZModelProperty(description = "产品群groupID, 与产品code一一对应", required = false, sample = "1667350946149978161")
    private String groupId;

    private int page;

    private int size;

}
