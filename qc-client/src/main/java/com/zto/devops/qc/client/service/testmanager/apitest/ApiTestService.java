package com.zto.devops.qc.client.service.testmanager.apitest;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.rpc.pipeline.ApplicationResp;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.*;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DebugTaskBaseInfo;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.SingleLinkBaseInfo;
import com.zto.devops.qc.client.service.testmanager.apitest.model.*;
import com.zto.devops.qc.client.service.testmanager.cases.model.ApiTestCaseExecuteDetailReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ApiTestCaseExecuteDetailResp;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteApiCaseReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteApiTestReq;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/5/24 17:23
 */
public interface ApiTestService {

    /**
     * 新增变量
     *
     * @param req
     * @return
     */
    Result<Void> addApiTestVariable(AddApiTestVariableReq req);

    /**
     * 编辑变量
     *
     * @param req
     * @return
     */
    Result<Void> editApiTestVariable(EditApiTestVariableReq req);

    /**
     * 分页查询变量
     *
     * @param req
     * @return
     */
    PageResult<PageApiTestVariableResp> queryApiTestVariablePage(PageApiTestVariableReq req);

    /**
     * 更新变量状态
     *
     * @param req
     * @return
     */
    Result<Void> updateApiTestVariableStatus(UpdateApiTestVariableStatusReq req);

    /**
     * 删除变量
     *
     * @param req
     * @return
     */
    Result<Void> deleteApiTestVariable(DeleteApiTestVariableReq req);

    /**
     * 调试变量
     *
     * @param req
     * @return
     */
    Result<String> getApiTestVariableResult(GetApiTestVariableResultReq req);

    Result<Void> executeApiTest(ExecuteApiTestReq req);

    Result<Void> executeApiCase(ExecuteApiCaseReq req);

    Result<Boolean> executeCallBack(ExecuteApiCallBackReq req);

    Result<List<ListExecuteDetailResp>> listExecuteDetail(ListExecuteDetailReq req);

    Result<Void> syncApiTestDoc(SyncApiTestDocReq req);

    Result<ApiCaseDetailResp> getApiCaseDetail(ApiCaseDetailReq req);

    /**
     * 接口列表
     *
     * @param req {@link PageApiReq}
     * @return {@link ApiVO}
     */
    PageResult<ApiVO> pageApi(PageApiReq req);

    /**
     * 查询接口信息（精简版）
     *
     * @param req {@link PageApiLiteInfoReq}
     * @return {@link ApiLiteInfoVO}
     */
    PageResult<ApiLiteInfoVO> pageApiLiteInfo(PageApiLiteInfoReq req);

    /**
     * 查询测试用例列表
     *
     * @param req
     * @return
     */
    PageResult<ApiCaseVO> pageApiCase(PageApiCaseReq req);

    /**
     * 查询测试用例列表（子用例）
     *
     * @param req {@link PageApiCaseChildrenReq}
     * @return {@link ApiCaseChildVO}
     */
    PageResult<ApiCaseChildVO> pageApiCaseChildren(PageApiCaseChildrenReq req);

    /**
     * 查询应用id列表
     *
     * @param req {@link ListAppIdReq}
     * @return {@link ApplicationResp}
     */
    Result<List<ApplicationResp>> listAppId(ListAppIdReq req);

    /**
     * 分页查询文档版本号
     *
     * @param req {@link PageApiDocVersionReq}
     * @return {@link ApiDocVersionVO}
     */
    PageResult<ApiDocVersionVO> pageApiDocVersion(PageApiDocVersionReq req);

    /**
     * 根据父用例code，统计启用用例个数，用于执行记录弹框
     *
     * @param req {@link CountCaseReq}
     * @return
     */
    Result<Integer> countCase(CountCaseReq req);

    Result<Void> updateApiCaseUser(UpdateApiCaseUserReq req);

    Result<Void> addSceneBasicInfo(AddSceneBasicInfoReq req);

    Result<Void> editSceneBasicInfo(EditSceneBasicInfoReq req);

    Result<List<ApiTestInitMethodResp>> getInitMethod(ApiTestInitMethodReq req);

    Result<EditSceneBasicInfoResp> editSceneInfo(EditSceneInfoReq req);

    Result<EditSceneBasicInfoResp> publishSceneInfo(EditSceneInfoReq req);

    Result<EditSceneBasicInfoResp> publishDataCenter(PublishDataCenterReq req);

    PageResult<SingleLinkBaseInfo> querySceneLink(PageLinkMapReq req);

    Result<DebugTaskBaseInfo> debugOnNode(DebugOnNodeReq req);

    /**
     * 查询场景列表
     *
     * @param req
     * @return
     */
    PageResult<SceneInfoResp> querySceneInfoPage(PageSceneInfoReq req);

    /**
     * 场景图查询
     *
     * @param req
     * @return
     */
    Result<SceneInfoResp> querySceneInfo(SceneInfoReq req);

    /**
     * 查询最新场景草稿详情
     *
     * @param req
     * @return
     */
    Result<SceneInfoResp> queryLatestEditSceneInfo(QueryLatestEditSceneInfoReq req);


    /**
     * 进入登记库提示
     *
     * @param req
     * @return
     */
    Result<EnterAutoSourceTipResp> autoSourceEnterTip(EnterAutoSourceTipReq req);

    /**
     * 查询进入登记库当前按钮状态
     *
     * @param req
     * @return
     */
    Result<SourceCurrentStatusResp> queryCurrentStatus(SourceCurrentStatusReq req);

    PageResult<PageApiInfoResp> pageApiInfo(PageApiInfoReq req);

    Result<Void> changeLinkStatus(ChangeLinkStatusReq req);

    /**
     * 查询登记库信息（精简版），用于执行记录下拉选择
     *
     * @param req {@link PageAutomaticRecordLiteInfoReq}
     * @return {@link AutomaticRecordLiteInfoVO}
     */
    PageResult<AutomaticRecordLiteInfoVO> pageAutomaticLiteInfo(PageAutomaticRecordLiteInfoReq req);

    /**
     * 进入登记库-生成
     *
     * @param req
     * @return
     */
    Result<Void> generateAutomaticSource(AutomaticSourceGenerateReq req);

    Result<CheckDisableSceneResp> checkDisableScene(CheckDisableSceneReq req);

    Result<JSONArray> queryProductDb(QueryProductDbReq req);

    Result<String> copySceneInfo(CopySceneInfoReq req);

    Result<ApiDebugLogResp> queryApiDebugLog(ApiDebugLogReq req);

    Result<Void> pushApiDebugLog(ApiDebugLogPushReq req);

    Result<Void> abortDebugTask(AbortDebugTaskReq req);

    Result<Void> debugCallBack(DebugCallBackReq req);

    Result<Void> debugCallBackEnd(DebugCallBackEndReq req);

    Result<Void> debugCallBackLog(DebugCallBackLogReq req);

    Result<Void> deleteSceneInfo(DeleteSceneInfoReq req);

    Result<JSONObject> getVariable(GetVariableReq req);

    /**
     * 编辑场景鉴权
     *
     * @param req
     * @return
     */
    Result<EditSceneAuthenticationResp> editSceneAuthentication(EditSceneAuthenticationReq req);


    Result<String> getPlainVariable(QueryApiTestVariableValueReq req);

    Result<QueryApiMockUrlResp> getApiMockUrl(QueryApiMockUrlReq req);

    Result<Void> sceneInitial(SceneInitialReq req);

    Result<List<SceneModuleQueryResp>> listModule(SceneModuleQueryReq req);

    Result<DeleteSceneModuleTipsResp> deleteSceneModuleTips(DeleteSceneModuleReq req);

    Result<Void> deleteSceneModule(DeleteSceneModuleReq req);

    Result<Void> addSceneModule(AddSceneModuleReq req);

    Result<Void> editSceneModule(EditSceneModuleReq req);

    Result<Void> moveSceneModule(MoveSceneModuleReq req);

    Result<Set<Long>> queryAuthorizedUsers(QueryAuthorizedUsersReq req);

    Result<Void> batchDeleteScene(BatchDeleteSceneReq req);

    Result<Void> batchMoveScene(BatchMoveSceneReq req);

    Result<Void> batchExecute(SceneBatchExecuteReq req);

    Result<Void> batchAddVariable(BatchAddVariableReq req);

    Result<List<BaseApiTestVariableResp>> querySceneVariable(QuerySceneVariableReq req);

    Result<Void> updateSceneStepRecord(UpdateSceneStepRecordReq req);

    Result<Void> editPreDataBasicInfo(EditDataBasicInfoReq req);

    Result<Void> editPreDataModule(EditPreDataModuleReq req);

    Result<Void> addPreDataModule(AddPreDataModuleReq req);

    Result<Void> movePreDataModule(MovePreDataModuleReq req);

    Result<EditPreDataBasicInfoResp> editPreDataInfo(EditPreDataInfoReq req);

    Result<Void> batchAddParameter(BatchAddPreDataVariableReq req);

    Result<Void> sharePreData(SharePreDataReq req);

    Result<Integer> batchSharePreData(BatchSharePreDataReq req);

    Result<List<SharedSceneModuleQueryResp>> listSharedModule(SharedSceneModuleQueryReq req);

    Result<List<ListRedisQueryResp>> listRedis(listRedisQueryReq req);

    Result<List<ListESQueryResp>> listES(listESQueryReq req);

    PageResult<PageMessageTopicResp> pageMessageTopic(PageMessageTopicReq req);

    Result<Boolean> checkUserPermission(CheckUserPermisionReq req);

    Result<Void> copyModule(SceneModuleCopyReq req);

    PageResult<PageApiLiteResp> pageApiLite(PageApiLiteReq req);

    Result<Void> authorize(ApiTestAuthorizeReq req);

    Result<Void> revoke(ApiTestRevokeReq req);

    Result<QueryAuthorizeListResp> queryAuthorizeList(QueryAuthorizeListReq req);

    Result<QueryDebugLogResp> queryDebugLog(QueryDebugLinkReq req);

    Result<QueryDebugLinkResp> queryDebugLink(QueryDebugLinkReq req);

    Result<QueryDebugNodeResultResp> queryDebugNodeResult(QueryDebugNodeResultReq req);

    Result<Void> saveDebugRecord(SaveDebugLinkReq req);

    Result<Void> saveApiGlobalConfiguration(AddApiGlobalConfigurationReq req);

    Result<QueryApiGlobalConfigurationResp> queryApiGlobalConfiguration(String productCode);

    PageResult<ApiCaseExceptionResp> queryApiCaseException(QueryApiCaseExceptionReq req);

    Result<Void> updateApiCaseException(UpdateApiCaseExceptionReq req);

    Result<Void> batchDeleteApiCaseException(BatchDeleteApiCaseExceptionReq req);

    Result<List<QueryApiFieldConfigResp>> queryApiFieldConfig(QueryApiFieldConfigReq req);

    Result<Void> generateApiCaseException(GenerateApiCaseExceptionReq req);

    Result<String> addApiTestCase(AddApiTestCaseReq req);

    Result<Date> editApiTestCase(EditApiTestCaseReq req);

    Result<Void> publishApiTestCase(PublishApiTestCaseReq req);

    Result<Void> batchPublishApiTestCase(BatchPublishApiTestCaseReq req);

    Result<DebugApiTaskInfo> apiDebug(ApiDebugReq req);

    PageResult<PageApiTestCaseVO> pageApiTestCase(PageApiTestCaseReq req);

    PageResult<PageApiTestCaseVO> pageApiTestCaseChildren(PageApiTestCaseChildrenReq req);

    Result<CheckBatchOperationVO> verifyBatchOperation(VerifyBatchOperationReq req);

    Result<Void> changeApiCaseStatus(ChangeApiCaseStatusReq req);

    Result<Void> batchChangeApiCaseStatus(BatchChangeApiCaseStatusReq req);

    Result<List<ApiTestCaseExecuteDetailResp>> queryApiTestCaseExecuteDetail(ApiTestCaseExecuteDetailReq req);

    Result<Void> favoritesPreData(FavoritesPreDataReq req);

    Result<Void> refreshSceneApiRelation(String productCode);

    Result<Void> refreshSceneApiCode(String productCode);

    Result<Void> batchGenerateApiCase(BatchGenerateApiCaseReq req);

    Result<Void> uploadJmxFile(JmxFileUploadReq req);


    Result<Void> refreshApiCasePreDataUpdate();

    Result<Void> deleteSceneApiRelation();

    Result<Void> sceneBatchAddToTestPlan(SceneBatchToTestPlanReq req);

    Result<Void> refreshApiTestField(RefreshApiTestFieldReq req);
    Result<Void> addSceneTag(AddSceneTagReq req);

    Result<Void> copyApiTestCase(CopyApiTestCaseReq req);

    List<ApiCaseDocVO> listApiCaseByDocIds(String productCode,List<Long> docIds);

    Result<String> addApiTestCaseByDocInvokeHistory(DocInvokeHistoryToApiTestCaseVO req);
}
