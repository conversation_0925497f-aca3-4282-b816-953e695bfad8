
package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchSetCoreReq implements Serializable {

    @ZModelProperty(description = "用例集合", required = true, sample = "['TC231130064892']")
    private List<String> codeList;

    @ZModelProperty(description = "目标模块编码，[未分组：NO_GROUP]", required = false, sample = "NO_GROUP")
    private String parentCode;

    @ZModelProperty(description = "是否复用原来分组", required = true, sample = "0")
    private Boolean useOriginalGroup;

}
