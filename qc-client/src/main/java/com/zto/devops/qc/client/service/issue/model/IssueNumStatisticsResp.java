package com.zto.devops.qc.client.service.issue.model;

import java.io.Serializable;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

@Data
public class IssueNumStatisticsResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "缺陷总数", required = false)
    private Integer issueCount;
    @GatewayModelProperty(description = "有效缺陷总数", required = false)
    private Integer validIssueCount;
    @GatewayModelProperty(description = "遗留效缺陷总数", required = false)
    private Integer legacyIssueCount;
    @GatewayModelProperty(description = "遗留效缺陷总数--P012", required = false)
    private Integer legacyIssueHigh;

//    @GatewayModelProperty(description = "有效缺陷率", required = false)
//    private Double validIssueRate;
//    @GatewayModelProperty(description = "遗留效缺陷--遗留率", required = false)
//    private Double legacyIssueHighRate;
}
