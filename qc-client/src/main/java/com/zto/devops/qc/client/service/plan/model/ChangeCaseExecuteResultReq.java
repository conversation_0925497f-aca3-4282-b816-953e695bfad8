package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class ChangeCaseExecuteResultReq implements Serializable {
    @NotEmpty(message = "执行结果不能为空")
    @ZsmpModelProperty(description = "执行结果", required = true)
    private TestPlanCaseStatusEnum executeStatus;

    @NotEmpty(message = "关联用例不为空")
    @ZsmpModelProperty(description = "关联用例id", required = true)
    private List<String> caseIds;

    @NotEmpty(message = "测试阶段不为空")
    @ZsmpModelProperty(description = "测试阶段", required = true)
    private TestPlanStageEnum testStage;

    @NotEmpty(message = "测试计划code不为空")
    @ZsmpModelProperty(description = "测试计划code", required = true)
    private String planCode;
}
