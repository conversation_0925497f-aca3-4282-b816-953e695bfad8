package com.zto.devops.qc.client.service.coverage;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.rpc.pipeline.event.ReleasedQcEvent;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageVersionRateVO;
import com.zto.devops.qc.client.service.coverage.model.req.*;
import com.zto.devops.qc.client.service.coverage.model.resp.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/9/9 18:25
 */
public interface CoverageService {

    /**
     * 查询覆盖率报告列表
     *
     * @param req
     * @return
     */
    Result getCoverageReportListPage(CoverageRecordPageReq req);

    /**
     * 前置校验生成条件
     *
     * @param req {@link VerifyGenerateConditionReq}
     * @return {@link VerifyGenerateConditionResp}
     */
    Result<VerifyGenerateConditionResp> verifyGenerateCondition(VerifyGenerateConditionReq req);

    /**
     * 批量生成覆盖率报告
     *
     * @param req
     * @return
     */
    Result<Void> batchCoverageReport(BatchCoverageReq req);

    /**
     * 查询代码覆盖率结果
     *
     * @param req 入参
     * @return {@link TmCodeCoverResultResp}
     */
    Result<TmCodeCoverResultResp> queryCodeCoverResult(TmCodeCoverResultReq req);

    /**
     * 检查多版本下代码覆盖率结果
     *
     * @param req
     * @return
     */
    Result<TmCodeCoverResultResp> queryCodeCoverResultList(TmCodeCoverResultListReq req);


    Result<Void> addCodeCoverResultReason(AddCodeCoverResultReasonReq req);

    /**
     * 获取预签名地址
     *
     * @param req
     * @return
     */
    Result<String> generatePreSignedUrl(OssBasicReq req);

    /**
     * 删除OSS对象
     *
     * @param req
     * @return
     */
    Result<Boolean> cleanObject(OssBasicReq req);

    /**
     * 覆盖率报告修改备注
     *
     * @param req
     * @return
     */
    Result<Void> editCoverage(EditCoverageReq req);

    /**
     * 覆盖率-任务查询接口
     *
     * @param req
     * @return
     */
    Result getCoverageTask(CoverageTaskReq req);

    /**
     * 触发生成exec文件
     *
     * @param event
     * @return
     */
    Result<Void> generateCoverageExec(ReleasedQcEvent event);

    /**
     * 获取覆盖率报告地址
     *
     * @param req
     * @return
     */
    Result<String> getReportUrl(OssBasicReq req);

    Result<Void> editReason(CoverageReasonEditReq req);

    Result<QueryReasonListResp> queryReasonList(QueryReasonListReq req);

    Result<CoverageRecordResp> getLatestCoverageReport(CoverageRecordReq req);

    Result<CoverageReportClassInfoResp> getCoverageReportClassInfo(CoverageReportClassInfoReq req);

    /********************* 给其他域调用都接口  **********************************/

    /**
     * 根据版本编码查询覆盖率
     * @param versionCodes 版本编码
     * @return 覆盖率
     */
    List<CoverageVersionRateVO> getBranchVersionRateByVersionCodes(List<String> versionCodes);
}
