package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiConfigTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "查询接口字段配置响应模型")
public class QueryApiFieldConfigResp implements Serializable {

    @ZModelProperty(description = "接口字段")
    private String key;

    @ZModelProperty(description = "接口配置列表")
    private List<ApiConfigTypeEnum> apiConfigTypeList;
}
