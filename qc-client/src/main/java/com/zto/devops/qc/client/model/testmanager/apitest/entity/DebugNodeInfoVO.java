package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ZsmpModel(description = "节点调试-节点结果VO")
@EqualsAndHashCode(callSuper = true)
@Data
public class DebugNodeInfoVO extends DebugLinkVO {

    private static final long serialVersionUID = -1618454527609346347L;
    @ZsmpModelProperty(description = "请求信息")
    private String requestMessage;

    @ZsmpModelProperty(description = "响应头")
    private String responseHeader;

    @ZsmpModelProperty(description = "响应体")
    private String responseBody;

    @ZsmpModelProperty(description = "断言结果")
    private String assertContent;

    @ZsmpModelProperty(description = "节点状态")
    private String nodeStatus;

    @ZsmpModelProperty(description = "节点code(节点结果)")
    private String nodeCode;

    @ZsmpModelProperty(description = "出参")
    private String output;
}
