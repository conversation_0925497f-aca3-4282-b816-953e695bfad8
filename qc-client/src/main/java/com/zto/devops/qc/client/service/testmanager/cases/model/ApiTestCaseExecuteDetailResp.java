package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ApiTestCaseExecuteDetailTiledVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ApiTestCaseExecuteDetailVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@ZsmpModel(description = "Api用例执行明细响应模型")
public class ApiTestCaseExecuteDetailResp implements Serializable {
    private static final long serialVersionUID = -2567872074246597881L;

    @ZsmpModelProperty(description = "接口code")
    private String code;

    @ZsmpModelProperty(description = "接口名称")
    private String name;

    @ZsmpModelProperty(description = "属性（模块/用例）")
    private TestcaseAttributeEnum attribute;

    @ZsmpModelProperty(description = "用例数量")
    private Integer testcaseCount;

    @ZsmpModelProperty(description = "用例节点")
    private List<ApiTestCaseExecuteDetailVO> children;

    public static List<ApiTestCaseExecuteDetailResp> buildSelf(List<ApiTestCaseExecuteDetailTiledVO> doList) {
        if (CollectionUtil.isEmpty(doList)) {
            return new ArrayList<>();
        }

        Map<String, List<ApiTestCaseExecuteDetailTiledVO>> voMap = doList.stream()
                .collect(Collectors.groupingBy(ApiTestCaseExecuteDetailTiledVO::getParentCode));
        if (CollectionUtil.isEmpty(voMap)) {
            return new ArrayList<>();
        }

        List<ApiTestCaseExecuteDetailVO> children = new ArrayList<>(voMap.size());
        voMap.forEach((key, value) -> children.add(ApiTestCaseExecuteDetailVO.buildSelf(value)));
        Integer caseCount = CollectionUtil.isNotEmpty(children)
                ? children.stream().mapToInt(ApiTestCaseExecuteDetailVO::getTestcaseCount).sum()
                : 0;
        ApiTestCaseExecuteDetailTiledVO sample = doList.get(0);
        ApiTestCaseExecuteDetailResp resp = new ApiTestCaseExecuteDetailResp();
        resp.setCode(sample.getApiCode());
        resp.setName(sample.getApiName());
        resp.setAttribute(TestcaseAttributeEnum.MODULE);
        resp.setChildren(children);
        resp.setTestcaseCount(caseCount);
        return Collections.singletonList(resp);
    }

}
