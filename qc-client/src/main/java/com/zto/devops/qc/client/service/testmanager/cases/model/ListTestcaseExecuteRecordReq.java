package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ListTestcaseExecuteRecordReq extends PageQueryBase implements Serializable {

    @ZModelProperty(description = "用例code", required = true, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "是否心跳记录", required = false, sample = "TEST_PLAN")
    private AutomaticTaskTrigModeEnum type;

}
