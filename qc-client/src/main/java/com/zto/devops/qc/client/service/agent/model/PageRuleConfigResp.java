
package com.zto.devops.qc.client.service.agent.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@GatewayModel(description = "分页查询规则列表")
@Data
public class PageRuleConfigResp implements Serializable {
    private static final long serialVersionUID = 3581464620675792144L;

    @GatewayModelProperty(description = "总数")
    private Long total;

    @GatewayModelProperty(description = "规则列表")
    private List<RuleConfigVO> list;

    public static PageRuleConfigResp buildSelf(List<RuleConfigVO> doList, Long total) {
        PageRuleConfigResp result = new PageRuleConfigResp();
        result.setList(CollectionUtil.isEmpty(doList) ? new ArrayList<>() : doList);
        result.setTotal(CollectionUtil.isEmpty(doList) ? 0l : total);
        return result;
    }
}
