package com.zto.devops.qc.client.model.testmanager.scheduler.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SchedulerCaseCodeListQuery extends BaseQuery {

    private String productCode;

    private String schedulerCode;

    private String parentCode;

    private String search;

    private List<TestcasePriorityEnum> priorityList;

    private List<AutomaticNodeTypeEnum> nodeTypeList;

    private List<AutomaticRecordTypeEnum> automaticTypeList;

}
