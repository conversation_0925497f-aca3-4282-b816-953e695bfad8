
package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ChangeVersionReq implements Serializable {

    @ZModelProperty(description = "用例code", required = true, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "目标版本code，[未分组：NONE_VERSION]", required = true, sample = "NONE_VERSION")
    private String targetVersionCode;

    @ZModelProperty(description = "目标模块编码，[未分组：NO_GROUP]", sample = "NO_GROUP")
    private String targetParentCode;

    @ZModelProperty(description = "是否复用原来分组", sample = "0")
    private Boolean useOriginalGroup;

}
