package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：AddOnlineSmokeReportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/10 5:57 下午
 * 创 建 人：bulecat
 */
@Data
public class SendSimpleTestReportReq extends BasicInfoReq implements Serializable {

    @ZModelProperty(description = "zui测试结果", required = false, sample = "PASS")
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "开发人数", required = false, sample = "1")
    private Integer developerCount;

    @ZModelProperty(description = "测试人数", required = false, sample = "1")
    private Integer testerCount;

    @ZModelProperty(description = "是否按计划范围上线", required = false, sample = "1")
    private Integer asPlanedOnline;

    @ZModelProperty(description = "是否延期", required = false, sample = "-1")
    private Integer delay;

    @ZModelProperty(description = "版本开始时间", required = false, sample = "1711987200000")
    private Date startDate;

    @ZModelProperty(description = "计划提测时间", required = false, sample = "1711987200000")
    private Date planPresentationDate;

    @ZModelProperty(description = "计划提测时间-上下午", required = false, sample = "上午")
    private String planPresentationDay;

    @ZModelProperty(description = "实际提测时间", required = false, sample = "1711987200000")
    private Date actualPresentationDate;

    @ZModelProperty(description = "计划准出时间", required = false, sample = "1711987200000")
    private Date planApprovalExitDate;

    @ZModelProperty(description = "计划准出时间--上下午", required = false, sample = "上午")
    private String planApprovalExitDay;

    @ZModelProperty(description = "实际准出时间", required = false, sample = "1711987200000")
    private Date actualApprovalExitDate;

    @ZModelProperty(description = "实际提测时间上午、下午", required = false, sample = "上午")
    private String actualPresentationDay;

    @ZModelProperty(description = "实际准出时间上午、下午", required = false, sample = "上午")
    private String actualApprovalExitDay;

    @ZModelProperty(description = "实际上线时间", required = false, sample = "1711987200000")
    private Date actualOnlineDate;

    @ZModelProperty(description = "计划发布时间", required = false, sample = "1711987200000")
    private Date planOnlineDate;

    @ZModelProperty(description = "测试信息", required = true, sample = "{}")
    private CaseExecuteResultVO caseExecuteResultVO;

//    @GatewayModelProperty(description = "线上冒烟执行结果")
//    private List<StatisticCaseResultVO> statisticCaseResultVOS;

    @ZModelProperty(description = "缺陷信息统计", required = false, sample = "[]")
    private List<IssueInfoVO> issueInfoVOS;

    @ZModelProperty(description = "附件", required = false, sample = "[]")
    private List<AttachmentVO> attachments;

    @ZModelProperty(description = "覆盖率结果", required = false, sample = "SUBSTANDARD")
    private CodeCoverResult codeCoverResult;

    @ZModelProperty(description = "代码覆盖率不达标原因", required = false, sample = "[]")
    private List<CoverageReasonVO> coverageReasonVOS;

    @ZModelProperty(description = "ui测试结果", required = false, sample = "PASS")
    private UiTestResultEnum uiTestResult;
}
