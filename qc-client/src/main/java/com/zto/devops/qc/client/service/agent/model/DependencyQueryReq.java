package com.zto.devops.qc.client.service.agent.model;


import com.zto.devops.qc.client.enums.agent.DependencyTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZModel(description = "批量操作规则配置请求入参")
public class DependencyQueryReq implements Serializable {
    private static final long serialVersionUID = -5816287586758699518L;

    @ZModelProperty(description = "应用id", required = true, sample = "devops-qc")
    String appid;

    @ZModelProperty(description = "分页page", sample = "1")
    Integer page = 1;

    @ZModelProperty(description = "分页大小pageSize", sample = "20")
    Integer pageSize = 200;

    @ZModelProperty(description = "api入口类型type", sample = "HttpService")
    DependencyTypeEnum apiType;

    @ZModelProperty(description = "入口服务serviceName", sample = "/flowCenterManage/v1/workOrder/getMyToDoTask")
    String serviceName;

    @ZModelProperty(description = "注入类型type", sample = "DubboClient")
    DependencyTypeEnum dependencyType = DependencyTypeEnum.DubboClient;

    @ZModelProperty(description = "依赖服务dependencyName", sample = "ITaskManageQueryService.xxx")
    String dependencyName;

}
