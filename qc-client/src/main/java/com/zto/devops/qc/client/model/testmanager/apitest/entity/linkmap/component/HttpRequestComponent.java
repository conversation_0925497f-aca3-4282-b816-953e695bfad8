package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.HttpRequestBodyTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;

import java.util.List;

@Data
public class HttpRequestComponent  extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    String protocol="http";
    String mockProtocol="http";
    String serverNameOrIp;
    String mockServerNameOrIp;
    String portNumber;
    String mockPortNumber;
    String method;
    String pathUrl;
    String mockPathUrl;
    Boolean mockSwitch=false;
    String contentEncoding="utf-8";
    String responseEncoding="utf-8";
    Boolean redirectAutomatically=false;
    Boolean followRedirects=true;
    Boolean userKeepAlive=true;
    Boolean userMultipartFormData=false;
    Boolean browserCompatibleHeaders = false;
    HttpRequestBodyTypeEnum bodyType= HttpRequestBodyTypeEnum.JSON;
    String bodyData;
    Integer connectTimout;
    List<HttpParameter> parameters;
    JSONArray httpRequestHeader;//{"name","xxx","value":"xxx"}
    JSONArray pathParams;//{"name","xxx","value":"xxx"}
    JSONArray httpFiles;//{"name":"fileName","path":"包括文件名的oss全路径","paramname":"xxx","mimetype":"xxx"}
}
