package com.zto.devops.qc.client.service.testmanager.cases.model;


import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "统计用例状态Req")
public class StatisticVersionCaseReq implements Serializable {

    @ZModelProperty(description = "版本code", sample = "VER2302168133")
    private String versionCode;

    @ZModelProperty(description = "手工or自动化", sample = "MANUAL")
    private TestcaseTypeEnum type;
}
