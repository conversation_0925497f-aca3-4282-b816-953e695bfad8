package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EditIssueByLaneReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZModelProperty(description = "编号", required = true, sample = "ISS230303008063")
    private String code;

    @ZModelProperty(description = "标题", required = false, sample = "标题")
    private String title;

    @ZModelProperty(description = "优先级", required = false, sample = "MIDDLE")
    private IssuePriority priority;

    @ZModelProperty(description = "开发人员ID", required = false, sample = "5305175")
    private Long developUserId;

    @ZModelProperty(description = "开发人员名称", required = false, sample = "开发人")
    private String developUserName;

    @ZModelProperty(description = "测试人员ID", required = false, sample = "5305175")
    private Long testUserId;

    @ZModelProperty(description = "测试人员名称", required = false, sample = "测试人")
    private String testUserName;

    @ZModelProperty(description = "计划开始时间", required = false, sample = "1711987200000")
    private Date planStartDate;

    @ZModelProperty(description = "计划结束时间", required = false, sample = "1711987200000")
    private Date planEndDate;

}
