package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ZsmpModel(description = "查询接口用例执行详情Req")
@EqualsAndHashCode(callSuper = true)
@Data
public class ApiTestCaseExecuteDetailReq extends BaseQuery implements Serializable {
    private static final long serialVersionUID = -4392692523785592308L;

    @ZsmpModelProperty(description = "产品code", sample = "399", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "任务code集合", sample = "SNF794208432184885248", required = true)
    private List<String> taskCodeList;

    @ZsmpModelProperty(description = "执行人id", sample = "12345")
    private List<String> executorIdList;

    @ZsmpModelProperty(description = "运行结果", sample = "INITIAL")
    private List<TestPlanCaseStatusEnum> status;

    @ZsmpModelProperty(description = "开始时间起", sample = "2024-01-01 23:59:59")
    private Date startTimeBegin;

    @ZsmpModelProperty(description = "开始时间止", sample = "2024-01-31 23:59:59")
    private Date startTimeEnd;

    @ZsmpModelProperty(description = "结束时间起", sample = "2024-01-01 23:59:59")
    private Date finishTimeBegin;

    @ZsmpModelProperty(description = "结束时间止", sample = "2024-01-31 23:59:59")
    private Date finishTimeEnd;
}
