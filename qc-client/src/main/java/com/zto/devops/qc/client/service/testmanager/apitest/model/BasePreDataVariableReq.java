package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BasePreDataVariableReq implements Serializable {
    private static final long serialVersionUID = 2780923217880262213L;

    @GatewayModelProperty(description = "显示名称")
    private String variableName;

    @GatewayModelProperty(description = "变量名称")
    private String variableKey;

    @GatewayModelProperty(description = "变量值", required = false)
    private String variableValue;

    @GatewayModelProperty(description = "是否必填", required = false)
    private Boolean requiredStatus;

    public Boolean getRequiredStatus() {
        return null == this.requiredStatus ? Boolean.FALSE : this.requiredStatus;
    }

}
