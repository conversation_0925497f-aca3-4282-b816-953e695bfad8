package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class AddApiTestVariableCommand extends BaseCommand {

    private String productCode;

    private String linkCode;

    private String variableName;

    private String variableKey;

    private String variableValue;

    private VariableTypeEnum type;

    private String variableStatus;

    private Integer sceneType;

    private SubVariableTypeEnum subVariableType;

    private Integer loginValidTime;

    public AddApiTestVariableCommand(String aggregateId) {
        super(aggregateId);
    }
}
