package com.zto.devops.qc.client.model.testmanager.coverage.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CoverageReasonEditEvent extends BaseEvent implements ActionEvent {

    @GatewayModelProperty(description = "版本code")
    private String versionCode;

    @GatewayModelProperty(description = "覆盖率结果")
    private CodeCoverResult coverageResult;

    @GatewayModelProperty(description = "代码覆盖率不达标原因")
    private List<CoverageReasonVO> coverageReasonVOS;

    @Override
    public String action() {
        return "编辑覆盖率不达标原因";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }

}
