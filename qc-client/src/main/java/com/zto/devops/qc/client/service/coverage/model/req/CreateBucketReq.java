package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022/10/30 15:25
 */
@Data
public class CreateBucketReq implements Serializable {

    @GatewayModelProperty(description = "存储桶名称")
    @URL(message = "存储桶名称")
    private String bucketName;

}
