package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class BatchMoveSceneCommand extends BaseCommand {

    private String productCode;

    private List<String> sceneCodeList;

    private String parentCode;

    private UseCaseFactoryTypeEnum sceneType;

    public BatchMoveSceneCommand(String aggregateId) {
        super(aggregateId);
    }
}
