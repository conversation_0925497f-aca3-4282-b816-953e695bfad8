package com.zto.devops.qc.client.model.testmanager.apitest.command;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CheckUserPermisionCommand extends EditSceneInfoCommand {

    private String productCode;

    public CheckUserPermisionCommand(String aggregateId) {
        super(aggregateId);
    }
}
