package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiFieldConfigVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GenerateApiCaseExceptionCommand extends BaseCommand {

    private String parentCaseCode;

    private ApiCaseStatusEnum status;

    private List<ApiFieldConfigVO> apiFieldConfigList;

    public GenerateApiCaseExceptionCommand(String aggregateId) {
        super(aggregateId);
    }
}
