package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "查询调试日志请求模型")
public class QueryDebugNodeResultReq implements Serializable {
    private static final long serialVersionUID = -8731984761695796130L;

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "场景code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "节点code", required = true)
    private String nodeCode;
}
