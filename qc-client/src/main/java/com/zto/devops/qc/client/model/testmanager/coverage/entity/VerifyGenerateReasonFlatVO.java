package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.zto.devops.qc.client.enums.testmanager.coverage.RecordErrorMsgEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerifyGenerateReasonFlatVO implements Serializable {
    private static final long serialVersionUID = -4678504200205591737L;

    /**
     * 应用id集合
     */
    private String appId;

    /**
     * 原因
     */
    private String reason;

    /**
     * 状态
     */
    private RecordStatusEnum status;

    public static VerifyGenerateReasonFlatVO buildSelf(RecordStatusEnum status, String appId, RecordErrorMsgEnum reason) {
        return VerifyGenerateReasonFlatVO.builder()
                .status(status)
                .appId(appId)
                .reason(null == reason ? Strings.EMPTY : reason.getValue())
                .build();
    }

    public static List<VerifyGenerateReasonFlatVO> buildList(RecordStatusEnum status, Set<String> appIdSet, RecordErrorMsgEnum reason) {
        List<VerifyGenerateReasonFlatVO> resultList = new ArrayList<>(appIdSet.size());
        appIdSet.forEach(appId -> resultList.add(buildSelf(status, appId, reason)));
        return resultList;
    }
}
