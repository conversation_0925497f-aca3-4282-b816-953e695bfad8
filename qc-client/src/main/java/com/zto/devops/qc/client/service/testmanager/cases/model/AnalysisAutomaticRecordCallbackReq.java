package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.AnalysisAutomaticRecordVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "解析结果回调请求模型")
public class AnalysisAutomaticRecordCallbackReq implements Serializable {

    @ZModelProperty(description = "登记库code", required = true, sample = "SNF994641594810368000")
    private String code;

    @ZModelProperty(description = "Jenkins构建id", sample = "11111")
    private String buildId;

    @ZModelProperty(description = "任务状态", required = true, sample = "NOT_STARTED")
    private AutomaticStatusEnum status;

    @ZModelProperty(description = "解析结果文件", sample = "222")
    private String filename;

    @ZModelProperty(description = "Git提交id", sample = "111")
    private String commitid;

    @ZModelProperty(description = "预留字段", sample = "branchName")
    private AnalysisAutomaticRecordVO ext;

    @ZModelProperty(description = "oss地址", sample = "222")
    private String ossPath;
}
