package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiConfigTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "更新异常用例请求模型")
public class UpdateApiCaseExceptionReq implements Serializable {

    @ZModelProperty(description = "用例code", required = true, sample = "TC231130064892")
    private String caseCode;

    @ZModelProperty(description = "变量名", required = true, sample = "name")
    private String key;

    @ZModelProperty(description = "变量值", required = true, sample = "null")
    private String value;

    @ZModelProperty(description = "断言", required = true, sample = "assert result.code != 200")
    private String asserts;

    @ZModelProperty(description = "接口配置类型", required = true, sample = "Required")
    private ApiConfigTypeEnum apiConfigType;
}
