package com.zto.devops.qc.client.service.testmanager.email.model;

import com.zto.devops.qc.client.enums.testmanager.email.EmailSourceEnum;
import com.zto.devops.qc.client.enums.testmanager.report.EmailButtonEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class DetailEmailResp implements Serializable {

    @GatewayModelProperty(description = "邮件记录Code", required = false)
    private String emailCode;

    @GatewayModelProperty(description = "预览邮件信息", required = false)
    private String preview;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "可操作按钮", required = false)
    private EmailButtonEnum buttonVOS;

    @GatewayModelProperty(description = "数据来源(报告、计划)", required = false)
    private EmailSourceEnum emailSource;

    @GatewayModelProperty(description = "产品/计划code", required = false)
    private String businessCode;

    @GatewayModelProperty(description = "报告code", required = false)
    private String reportCode;

    public String getReportCode() {
        if (null != this.emailSource && this.emailSource.equals(EmailSourceEnum.TEST_REPORT)) {
            this.reportCode = this.businessCode;
        }
        return this.reportCode;
    }

    @GatewayModelProperty(description = "计划code", required = false)
    private String planCode;


    public String getPlanCode() {
        if (null != this.emailSource && this.emailSource.equals(EmailSourceEnum.TEST_PLAN)) {
            this.planCode = this.businessCode;
        }
        return this.planCode;
    }

    @GatewayModelProperty(description = "产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "关联计划code", required = false)
    private String relatePlanCode;

    @GatewayModelProperty(description = "发送时间", required = false)
    private Date sendTime;

    @GatewayModelProperty(description = "发送人名字", required = false)
    private String sendUserName;

    @GatewayModelProperty(description = "发送人id", required = false)
    private String sendUserId;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;
}
