package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class Scene implements Serializable {

    private static final long serialVersionUID = 1L;

    String sceneCode;
    String productCode;
    /**
     * {"nodeId":Node,...}*
     */
    JSONObject nodes;
    /**
     * {"lineId":Line,...}*
     */
    JSONObject lines;

    List<String> startNode; //起始节点Code
    List<String> endNode;//终节点Code

    Set<String> dbIds;

    /**
     * 造数中心出入参数
     */
    JSONArray inputParameter;
    List<DataCenterParameter> outputParameter;
}
