package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "执行自动化任务请求模型")
public class ExecuteAutomaticTaskReq implements Serializable {

    @ZModelProperty(description = "执行环境", required = true, sample = "fat")
    private String env;

    @ZModelProperty(description = "执行环境tag", required = true, sample = "tag")
    private String tag;

    @ZModelProperty(description = "产品code", sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "用例code", required = true, sample = "['TC231130064892']")
    private List<String> testcaseCodeList;

    @ZModelProperty(description = "测试计划", sample = "TP240305003075")
    private String testPlanCode;

    @ZModelProperty(description = "测试计划阶段", sample = "FUNCTIONAL_TEST")
    private TestPlanStageEnum testStage;

    @ZModelProperty(description = "版本code", sample = "VER2306058015")
    private String versionCode;

    @ZModelProperty(description = "触发方式", required = true, sample = "TEST_PLAN")
    private AutomaticTaskTrigModeEnum trigMode;

    @ZModelProperty(description = "是否部署过", sample = "0")
    private Boolean isDeploy;

    @ZModelProperty(description = "是否动态多环境", sample = "0")
    private Boolean autoEnv;
}
