package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ZsmpModel(description = "新增场景分组请求模型")
public class AddSceneModuleReq implements Serializable {

    @ZsmpModelProperty(description = "产品code", required = true)
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZsmpModelProperty(description = "父分组code", required = true)
    private String parentCode;

    @ZsmpModelProperty(description = "分组名称", required = true)
    @Size(max = 60, min = 1)
    private String sceneIndexName;

    @ZsmpModelProperty(description = "类型", required = false)
    private String sceneType;


}
