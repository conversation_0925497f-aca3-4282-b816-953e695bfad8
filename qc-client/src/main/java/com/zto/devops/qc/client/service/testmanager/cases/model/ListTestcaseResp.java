package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.HeartCaseSwitchEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@ZsmpModel(description = "用例列表响应模型")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListTestcaseResp extends TestcaseResp implements Serializable {
    private static final long serialVersionUID = -4285569437744266267L;

    @ZsmpModelProperty(description = "执行次数")
    private Integer executeNum;

    @ZsmpModelProperty(description = "用例数量")
    private Integer testcaseCount;

    @ZsmpModelProperty(description = "子节点")
    private List<ListTestcaseResp> children;

    @ZsmpModelProperty(description = "开关按钮状态")
    private HeartCaseSwitchEnum switchStatus;

    @ZsmpModelProperty(description = "是否可勾选")
    private Boolean enableChecked;

    @ZsmpModelProperty(description = "是否关联场景")
    private Boolean sceneFlag;
}
