package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ZsmpModel(description = "节点调试-日志VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DebugLogVO implements Serializable {
    private static final long serialVersionUID = 9064389866663434120L;

    @ZsmpModelProperty(description = " 本次任务最终状态")
    private String status;

    @ZsmpModelProperty(description = " 调试日志")
    private String debugLog;

    @ZsmpModelProperty(description = "是否调试历史")
    private Boolean historyFlag = true;

    public static DebugLogVO buildSelf(String status, String debugLog) {
        return DebugLogVO.builder()
                .status(status)
                .debugLog(debugLog)
                .historyFlag(Boolean.TRUE)
                .build();
    }
}
