package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.zto.devops.qc.client.enums.testmanager.apitest.RequestMethodEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExecuteHttpApiTestExcelVO implements Serializable {

    @ColumnWidth(25)
    @ExcelProperty("taskCode")
    private String taskCode;

    @ColumnWidth(25)
    @ExcelProperty("productCode")
    private String productCode;

    @ColumnWidth(25)
    @ExcelProperty("apiCode")
    private String apiCode;

    @ColumnWidth(25)
    @ExcelProperty("apiName")
    private String apiName;

    @ColumnWidth(25)
    @ExcelProperty("caseCode")
    private String caseCode;

    @ColumnWidth(25)
    @ExcelProperty("caseName")
    private String caseName;

    @ColumnWidth(25)
    @ExcelProperty("method")
    private String method;

    @ColumnWidth(25)
    @ExcelProperty("reqUrl")
    private String reqUrl;

    @ColumnWidth(50)
    @ExcelProperty("parameter")
    private String parameter;

    @ColumnWidth(50)
    @ExcelProperty("header")
    private String header;

    @ColumnWidth(50)
    @ExcelProperty("repAssert")
    private String repAssert;

    @ColumnWidth(50)
    @ExcelProperty("variable")
    private String variable;

}
