package com.zto.devops.qc.client.model.testmanager.apitest.entity.apitest;

import java.io.Serializable;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.DataCenterParameter;
import lombok.Data;

import java.util.List;

@Data
public class ApiTestCaseDataCenter implements Serializable {

    private static final long serialVersionUID = 1L;


    private String dataCenterSceneCode;

    private List<DataCenterParameter> inputParameter;

    private List<DataCenterParameter> outputParameter;

    private String variableName;

    private String ossPath;
}
