package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
@ZModel(description = "任务查询接口入参")
public class CoverageTaskReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1870509636783316888L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", sample = "PRO2207128000")
    private String productCode;

    @ZModelProperty(description = "应用id", sample = "zto-qamp")
    private String appId;

    @ZModelProperty(description = "任务Id", sample = "VER230809803720231226092056")
    private String taskId;

    @ZModelProperty(description = "生成状态", sample = "[SUCCEED]")
    private List<RecordStatusEnum> statusList;

    @ZModelProperty(description = "覆盖率类型", sample = "[BRANCH]")
    private List<RecordTypeEnum> recordTypeList;

    @ZModelProperty(description = "差异类型", sample = "[INCREMENT]")
    private List<DiffTypeEnum> diffTypeList;

    @ZModelProperty(description = "版本编码", sample = "[VER2308098037]")
    private List<String> versionCodeList;

    @ZModelProperty(description = "生成人", sample = "[5431563]")
    private List<Long> creatorIdList;

    @ZModelProperty(description = "生成时间-开始", sample = "1701360000000")
    private Date recordCreateStart;

    @ZModelProperty(description = "生成时间-结束", sample = "1704038399000")
    private Date recordCreateEnd;

    @ZModelProperty(description = "更新时间-开始", sample = "1701360000000")
    private Date recordModifiedStart;

    @ZModelProperty(description = "更新时间-结束", sample = "1704038399000")
    private Date recordModifiedEnd;

}
