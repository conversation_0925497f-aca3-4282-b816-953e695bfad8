package com.zto.devops.qc.client.service.relevantUser.model;


import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.enums.OrderByEnum;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.issue.RelevantUserTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目名称：user-parent
 * 类 名 称：MyTaskResp
 * 类 描 述：TODO
 * 创建时间：2021/10/12 5:08 下午
 * 创 建 人：bulecat
 */
@Data
public class MyTaskReq extends PageQueryBase implements Serializable {

    private List<RelevantUserTypeEnum> relevantUserTypes;

    private DomainEnum domain;

    private String orderField;

    private OrderByEnum orderType = OrderByEnum.DESC;

    private Long userId;

    private Long modifierId;

    private List<String> status;

    private String name;

    private String businessCode;

    private List<String> projectLevel;

    private List<String> projectScale;

    private List<String> worthLevel;

    @GatewayModelProperty(description = "版本编码",required = false)
    private List<String> versionCodeList;

    @GatewayModelProperty(description = "宝盒群id", required = false)
    private String groupId;

}
