package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ZsmpModel(description = "批量用例加入测试计划请求模型")
@Data
public class BatchAddCaseInTestPlanReq implements Serializable {

    @ZModelProperty(description = "用例唯一标识", required = true, sample = "['TC231130064892']")
    private List<String> caseCodeList;

    @ZModelProperty(description = "测试计划唯一标识", required = true, sample = "TP240305003075")
    private String testPlanCode;

    @ZModelProperty(description = "用例类型", required = false, sample = "MANUAL")
    private TestcaseTypeEnum caseType;

    @ZModelProperty(description = "测试阶段", required = true, sample = "SMOKE_TEST")
    private TestPlanStageEnum testStage;
}
