package com.zto.devops.qc.client.model.relevantUser.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目名称：project-parent
 * 类 名 称：TaskCreateQuery
 * 类 描 述：TODO
 * 创建时间：2021/12/15 7:31 下午
 * 创 建 人：bulecat
 */
@Data
public class MyIssueTotalQuery extends TaskBaseQuery implements Serializable {
    private List<String> issueStatuses;

    private String productCode;
}
