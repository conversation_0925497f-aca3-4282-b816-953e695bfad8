package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerifyGenerateVO implements Serializable {
    private static final long serialVersionUID = -4678504200205591737L;

    /**
     * 原因列表
     */
    private List<VerifyGenerateReasonVO> reasonList;

    /**
     * 状态
     */
    private RecordStatusEnum status;

    public static VerifyGenerateVO buildSelfForSuccess(RecordStatusEnum status, String reason, List<String> appIdList) {
        return VerifyGenerateVO.builder()
                .reasonList(Arrays.asList(VerifyGenerateReasonVO.buildSelf(reason, appIdList)))
                .status(status)
                .build();
    }

    public static VerifyGenerateVO buildSelf(RecordStatusEnum status, List<VerifyGenerateReasonVO> reasonList) {
        return VerifyGenerateVO.builder()
                .reasonList(reasonList)
                .status(status)
                .build();
    }
}
