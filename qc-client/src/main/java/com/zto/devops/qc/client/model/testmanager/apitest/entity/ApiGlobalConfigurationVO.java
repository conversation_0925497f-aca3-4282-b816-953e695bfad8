package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseSourceTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiConfigTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@GatewayModel(description = "api全局配置VO")
@Data
public class ApiGlobalConfigurationVO implements Serializable {

    @ZsmpModelProperty(description = "勾选状态0删除1未勾选2勾选使用中", required = true)
    private Integer enable;

    @ZsmpModelProperty(description = "配置类型1必填校验2最大值校验3最小值校验4字段类型不符5数据不存在", required = true)
    private Integer apiConfigType;

    @ZsmpModelProperty(description = "配置类型名称", required = true)
    private String apiConfigTypeName;

    @ZsmpModelProperty(description = "api全局配置VO", required = true)
    private List<ApiConfigVO> apiConfigVOList;

    public String getApiConfigTypeName() {
        if (null != this.apiConfigType) {
            ApiConfigTypeEnum apiConfigTypeEnum = ApiConfigTypeEnum.codeOf(this.apiConfigType);
            if (null != apiConfigTypeEnum) {
                this.apiConfigTypeName = apiConfigTypeEnum.getDesc();
            }
        }
        return apiConfigTypeName;
    }
}
