package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.XmindOperationEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "xmind编辑模型")
public class XmindCaseEditReq implements Serializable {

    @ZModelProperty(description = "用例code", sample = "TC231130064892")
    private String id;

    @ZModelProperty(description = "节点名称", sample = "111")
    private String topic;

    @ZModelProperty(description = "父节点code", sample = "TC231130064892")
    private String parentCode;

    @ZModelProperty(description = "产品code", sample = "399")
    private String productCode;

    @ZModelProperty(description = "节点标签", sample = "1")
    private String tag;

    @ZModelProperty(description = "节点操作", sample = "UPDATE")
    private XmindOperationEnum operation;

    @ZModelProperty(description = "节点属性", sample = "TESTCASE")
    private TestcaseAttributeEnum attribute;

    @ZModelProperty(description = "所选分组父分组编号", required = false, sample = "111")
    private String oldParentCode;

    @ZModelProperty(description = "移动前路径", required = false, sample = "1")
    private String oldPath;

    @ZModelProperty(description = "移动后路径", required = false, sample = "11")
    private String newPath;

    @ZModelProperty(description = "版本code", required = false, sample = "VER2302168133")
    private String versionCode;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;
}
