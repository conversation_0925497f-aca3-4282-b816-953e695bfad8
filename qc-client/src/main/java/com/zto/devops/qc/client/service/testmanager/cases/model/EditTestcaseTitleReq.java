package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EditTestcaseTitleReq implements Serializable {
    @ZModelProperty(description = "code", required = false, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "用例标题", required = false, sample = "用例标题")
    private String name;

    @ZModelProperty(description = "用例等级 HIGH MIDDLE LOW", required = false, sample = "MIDDLE")
    private TestcasePriorityEnum priority;

    @ZModelProperty(description = "版本编号", required = false, sample = "VER2302168133")
    private String versionCode;
}
