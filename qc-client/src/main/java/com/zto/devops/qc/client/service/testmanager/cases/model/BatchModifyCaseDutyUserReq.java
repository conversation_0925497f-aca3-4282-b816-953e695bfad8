package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.model.testmanager.cases.entity.BatchOperateCaseVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchModifyCaseDutyUserReq implements Serializable {

    @ZModelProperty(description = "用例code", required = false, sample = "[]")
    private List<BatchOperateCaseVO> codeList;

    @ZModelProperty(description = "责任人", required = false, sample = "5878415")
    private Long dutyUserId;

    @ZModelProperty(description = "责任人名称", required = false, sample = "责任人")
    private String dutyUser;

}
