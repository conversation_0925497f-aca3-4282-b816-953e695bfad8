package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewRenewalVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AddAndSendReviewReportCommand extends BaseReportInfoCommand {

    @GatewayModelProperty(description = "评审信息", required = true)
    private ReviewInfoVO reviewInfo;

    @GatewayModelProperty(description = "评审意见", required = false)
    private List<ReviewOpinionVO> reviewOpinions;

    @GatewayModelProperty(description = "评审更新信息", required = false)
    private List<ReviewRenewalVO> reviewRenewals;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    @GatewayModelProperty(description = "发送人Id", required = false)
    private Long sendUserId;

    @GatewayModelProperty(description = "发送人姓名", required = false)
    private String sendUserName;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

    public AddAndSendReviewReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
