package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class AddAndSendExternalTestReportCommand extends BaseReportInfoCommand {

    @GatewayModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    @GatewayModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;

    @GatewayModelProperty(description = "计划提测时间上午/下午", required = false)
    private String presentationDay;

    @GatewayModelProperty(description = "计划上线时间", required = false)
    private Date publishDate;

    @GatewayModelProperty(description = "计划上线时间--上下午", required = false)
    private String publishDay;

    @GatewayModelProperty(description = "验收开始时间", required = false)
    private Date checkStartDate;

    @GatewayModelProperty(description = "验收结束时间", required = false)
    private Date checkEndDate;

    @GatewayModelProperty(description = "实际上线时间", required = false)
    private Date actualPublishDate;

    @GatewayModelProperty(description = "延期 -1 否，1 是", required = false)
    private Integer delay;

    @GatewayModelProperty(description = "延期 -1 否，1 是", required = false)
    private String delayDesc;

    @GatewayModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    @GatewayModelProperty(description = "发送人Id", required = false)
    private Long sendUserId;

    @GatewayModelProperty(description = "发送人姓名", required = false)
    private String sendUserName;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

    public AddAndSendExternalTestReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
