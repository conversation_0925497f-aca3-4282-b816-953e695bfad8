package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@GatewayModel(description = "分页查询api文档VO")
@Data
public class PageApiVO implements Serializable {
    private static final long serialVersionUID = -1696252078897055888L;

    @GatewayModelProperty(description = "总数")
    private Long total;

    @GatewayModelProperty(description = "接口数据")
    private List<ApiVO> list;

    public static PageApiVO buildSelf(List<ApiVO> doList, Long total) {
        PageApiVO result = new PageApiVO();
        result.setList(CollectionUtil.isEmpty(doList) ? new ArrayList<>() : doList);
        result.setTotal(CollectionUtil.isEmpty(doList) ? 0l : total);
        return result;
    }
}
