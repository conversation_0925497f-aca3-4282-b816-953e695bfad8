package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.ListXmindDetailVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ZsmpModel(description = "xmind展示模型")
@Data
public class ListXmindDetailResp implements Serializable {

    @ZsmpModelProperty(description = "用例code")
    private String id;

    @ZsmpModelProperty(description = "节点描述")
    private String topic;

    @ZsmpModelProperty(description = "父节点code")
    private String parentCode;

    @ZsmpModelProperty(description = "节点在左边(右边)")
    private DirectionEnum direction;

    @ZsmpModelProperty(description = "是否允许编辑(topic)")
    private Boolean disabled;

    @ZsmpModelProperty(description = "是否展开")
    private Boolean expanded;

    @ZsmpModelProperty(description = "标签名字中文")
    private String tagName;

    @ZsmpModelProperty(description = "标签名字")
    private TestCaseTagNameEnum tagValue;

    @ZsmpModelProperty(description = "用例数量")
    private Long nums;

    @ZsmpModelProperty(description = "标签编辑权限，默认false")
    private Boolean tagEdit;

    @ZsmpModelProperty(description = "子节点")
    private List<ListXmindDetailVO> children;

    @ZsmpModelProperty(description = "节点属性")
    private TestcaseAttributeEnum attribute;

    @ZsmpModelProperty(description = "是否有下级节点")
    private Boolean hasChilds;

    @ZsmpModelProperty(description = "等级")
    private TestcasePriorityEnum priority;

    @ZsmpModelProperty(description = "等级描述")
    private String priorityDesc;

    @ZsmpModelProperty(description = "用例数量")
    private Integer testcaseCount;

    @ZsmpModelProperty(description = "路径")
    private String path;

    @ZsmpModelProperty(description = "执行状态")
    private TestPlanCaseStatusEnum executionStatus;

    @ZsmpModelProperty(description = "执行状态中文")
    private String executionStatusDesc;

    @ZsmpModelProperty(description = "用例状态")
    private TestcaseStatusEnum testcaseStatus;

    @ZsmpModelProperty(description = "用例状态中文")
    private String testcaseStatusDesc;

}
