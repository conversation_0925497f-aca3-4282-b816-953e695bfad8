package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@GatewayModel(description = "查询api文档版本号Req")
@Data
public class PageApiDocVersionReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 4236165376896831949L;

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "接口版本号（模糊搜索）", required = false)
    private String docVersion;
}
