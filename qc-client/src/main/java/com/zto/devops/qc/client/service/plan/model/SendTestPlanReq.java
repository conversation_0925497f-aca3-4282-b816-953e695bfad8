package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.email.EmailTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SendTestPlanReq implements Serializable {

    @ZModelProperty(description = "邮件类型", required = false, sample = "TEST_PLAN")
    private EmailTypeEnum emailType;

    @ZModelProperty(description = "报告/计划code", required = false, sample = "TP240305003075")
    private String businessCode;

    @ZModelProperty(description = "报告/计划名称", required = false, sample = "V1.0.0测试计划")
    private String businessName;

    @ZModelProperty(description = "关联计划code", required = false, sample = "TP240305003075")
    private String relationPlanCode;

    @ZModelProperty(description = "关联计划名称", required = false, sample = "V1.0.0移动测试计划")
    private String relationPlanName;

    @ZModelProperty(description = "所属产品id", required = false, sample = "399")
    private String productCode;

    @ZModelProperty(description = "产品名称", required = false, sample = "一站式研发平台")
    private String productName;

    @ZModelProperty(description = "关联版本code", required = false, sample = "VER2306058015")
    private String versionCode;

    @ZModelProperty(description = "关联版本名称", required = false, sample = "V1.0.0")
    private String versionName;

    @ZModelProperty(description = "计划提测/准入时间", required = false, sample = "1711987200000")
    private Date accessDate;

    @ZModelProperty(description = "计划提测时间:上午-AM | 下午-PM", required = false, sample = "AM")
    private TestPlanDatePartitionEnum accessDatePartition;

    @ZModelProperty(description = "计划准出时间", required = false, sample = "1711987200000")
    private Date permitDate;

    @ZModelProperty(description = "计划提测时间:上午-AM | 下午-PM", required = false, sample = "AM")
    private TestPlanDatePartitionEnum permitDatePartition;

    @ZModelProperty(description = "收件人", required = false, sample = "[]")
    private List<SendUserInfoVO> receiveUsers;

    @ZModelProperty(description = "抄送人", required = false, sample = "[]")
    private List<SendUserInfoVO> ccUsers;

    @ZModelProperty(description = "预览html", required = false, sample = "")
    private String preview;

}
