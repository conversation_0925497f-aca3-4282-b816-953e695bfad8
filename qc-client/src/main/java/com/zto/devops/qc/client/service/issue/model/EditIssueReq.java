package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.qc.client.enums.issue.*;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class EditIssueReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZModelProperty(description = "编号", required = true, sample = "ISS230303008063")
    private String code;

    @ZModelProperty(description = "标题", required = false, sample = "标题")
    private String title;

    @ZModelProperty(description = "描述", required = false, sample = "描述")
    private String description;

    @ZModelProperty(description = "发现环境", required = false, sample = "FAT_EVN")
    private IssueFindEnv findEnv;

    @ZModelProperty(description = "发现阶段", required = false, sample = "TEST_STAGE")
    private IssueFindStage findStage;

    @ZModelProperty(description = "优先级", required = false, sample = "MIDDLE")
    private IssuePriority priority;

    @ZModelProperty(description = "复现概率", required = false, sample = "OCCASIONALLY")
    private IssueRepetitionRate repetitionRate;

    @ZModelProperty(description = "缺陷根源", required = false, sample = "FUNCTIONAL_DEVELOPMENT_BUG")
    private IssueRootCause rootCause;

    @ZModelProperty(description = "缺陷类型", required = false, sample = "FUNCTION_BUG")
    private IssueType type;

    @ZModelProperty(description = "测试方法", required = false, sample = "FUNCTION_TEST")
    private IssueTestMethod testMethod;

    @ZModelProperty(description = "需求编码", required = false, sample = "FN240416004023")
    private String requirementCode;
    @ZModelProperty(description = "需求名称", required = false, sample = "需求名称")
    private String requirementName;
    @ZModelProperty(description = "需求等级", required = false, sample = "REQUIREMENT")
    private RequirementLevel requirementLevel;
    @ZModelProperty(description = "发现版本编码", required = false, sample = "VER2302168133")
    private String findVersionCode;
    @ZModelProperty(description = "发现版本名称", required = false, sample = "V1.0.0")
    private String findVersionName;

    @ZModelProperty(description = "修复版本编码", required = false, sample = "VER2302168133")
    private String fixVersionCode;
    @ZModelProperty(description = "修复版本名称", required = false, sample = "V1.0.0")
    private String fixVersionName;

    @ZModelProperty(description = "开发人员ID", required = false, sample = "5305175")
    private Long developUserId;
    @ZModelProperty(description = "开发人员名称", required = false, sample = "开发人")
    private String developUserName;

    @ZModelProperty(description = "测试人员ID", required = false, sample = "5305175")
    private Long testUserId;
    @ZModelProperty(description = "测试人员名称", required = false, sample = "测试人")
    private String testUserName;
    @ZModelProperty(description = "关联迭代code", required = false, sample = "SPT24050600102")
    private String sprintCode;
    @ZModelProperty(description = "关联迭代名称", required = false, sample = "sprint24-关联迭代名称")
    private String sprintName;

    @ZModelProperty(description = "应用类型", required = false, sample = "WEB")
    private IssueApplicationType applicationType;

    @ZModelProperty(description = "关联用例code", required = false, sample = "[]")
    private List<String> testcaseCodes;

    @ZModelProperty(description = "计划开始时间", required = false, sample = "1711987200000")
    private Date planStartDate;
    @ZModelProperty(description = "计划结束时间", required = false, sample = "1711987200000")
    private Date planEndDate;

}
