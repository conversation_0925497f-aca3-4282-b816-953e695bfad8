package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ZModel(description = "编辑造数基础信息")
public class EditDataBasicInfoReq implements Serializable {
    private static final long serialVersionUID = 4293462778632180834L;

    @ZModelProperty(description = "造数code", sample = "SNF954001974792028160", required = true)
    private String sceneCode;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", sample = "399", required = true)
    private String productCode;

    @ZModelProperty(description = "造数名称", sample = "造数名称", required = true)
    @Size(min = 1, max = 60)
    private String sceneName;

    @ZModelProperty(description = "造数描述", sample = "造数描述")
    @Size(max = 500)
    private String sceneInfoDesc;

    @ZModelProperty(description = "父分组code", sample = "ALL", required = true)
    private String parentCode;
}
