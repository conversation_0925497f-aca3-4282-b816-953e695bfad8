package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableUsageTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryPreDataVariableVO implements Serializable {
    private static final long serialVersionUID = -2711408711697398092L;

    private String variableCode;

    private String variableName;

    private String variableKey;

    private String variableValue;

    private VariableTypeEnum type;

    private VariableUsageTypeEnum usageType;

    private Boolean requiredStatus;

    private String variableStatus;

    private Integer sceneType;
}
