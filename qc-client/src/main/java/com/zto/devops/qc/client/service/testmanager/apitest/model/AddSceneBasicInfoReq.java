package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ZModel(description = "新增场景请求模型")
public class AddSceneBasicInfoReq implements Serializable {

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", sample = "399", required = true)
    private String productCode;

    @ZModelProperty(description = "场景名称", sample = "场景名称", required = true)
    @Size(min = 1, max = 60)
    private String sceneName;

    @ZModelProperty(description = "场景描述", sample = "场景描述")
    @Size(max = 500)
    private String sceneInfoDesc;

    @ZModelProperty(description = "分组", sample = "ALL")
    private String parentCode;

    @ZModelProperty(description = "类型", sample = "CREATE")
    private String sceneType;

}
