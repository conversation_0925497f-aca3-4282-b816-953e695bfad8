package com.zto.devops.qc.client.service.plan.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "查询测试计划用例上一条/下一条响应模型")
public class FindSortedPlanCaseResp implements Serializable {
    private static final long serialVersionUID = -5656803386971377481L;

    @ZsmpModelProperty(description = "上一条用例code")
    private String previous;

    @ZsmpModelProperty(description = "下一条用例code")
    private String next;
}