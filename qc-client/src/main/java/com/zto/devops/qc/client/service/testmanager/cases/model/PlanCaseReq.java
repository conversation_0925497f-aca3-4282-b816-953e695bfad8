package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "测试计划用例详情请求模型")
@Data
public class PlanCaseReq implements Serializable {
    private static final long serialVersionUID = -9220814310770929790L;

    @ZModelProperty(description = "用例code", required = true, sample = "TC231130064892")
    private String caseCode;

    @ZModelProperty(description = "测试计划code", required = true, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "测试计划阶段", required = true, sample = "SMOKE_TEST")
    private TestPlanStageEnum testStage;
}
