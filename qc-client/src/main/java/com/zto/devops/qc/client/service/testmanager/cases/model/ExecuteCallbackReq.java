package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "执行自动化任务回调请求模型")
public class ExecuteCallbackReq implements Serializable {

    @ZModelProperty(description = "自动化任务code", sample = "SNF987293442717515776")
    private String code;

    @ZModelProperty(description = "Jenkins构建id", sample = "111")
    private String buildId;

    @ZModelProperty(description = "任务状态", sample = "NOT_STARTED")
    private AutomaticStatusEnum status;

    @ZModelProperty(description = "oss地址", sample = "111")
    private String ossPath;

}
