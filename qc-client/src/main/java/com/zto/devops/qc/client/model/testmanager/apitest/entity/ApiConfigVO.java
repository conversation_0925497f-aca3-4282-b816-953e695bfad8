package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

@GatewayModel(description = "api配置VO")
@Data
public class ApiConfigVO implements Serializable {

    @ZsmpModelProperty(description = "配置应用范围 0应用 1接口", required = true)
    private Integer apiConfigScope;

    @ZsmpModelProperty(description = "配置应用内容", required = true)
    private String apiConfigValue;

    @ZsmpModelProperty(description = "产品名称", required = true)
    private String asserts;
}
