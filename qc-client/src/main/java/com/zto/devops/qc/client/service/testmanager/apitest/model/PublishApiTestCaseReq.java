package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ZModel(description = "发布接口测试用例入参")
public class PublishApiTestCaseReq implements Serializable {

    private static final long serialVersionUID = 5227273168744954464L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "用例编码", required = true, sample = "SNF")
    @Size(min = 1, max = 100)
    private String caseCode;


}
