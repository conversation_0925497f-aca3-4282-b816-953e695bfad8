package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ListSchedulerCaseReq implements Serializable {

    @ZModelProperty(description = "产品Code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "定时任务Code", required = true, sample = "SNF975268955453128704")
    private String schedulerCode;

    @ZModelProperty(description = "父节点code", required = false, sample = "SNF975268955453128704")
    private String parentCode;

    @ZModelProperty(description = "搜索code或名称", required = false, sample = "定时任务名称")
    private String codeOrTitle;

    @ZModelProperty(description = "执行结果，INITIAL-未执行|PASSED-通过|FAILED-失败|BLOCK-阻塞|SKIP-跳过|RETEST-重测", required = false, sample = "['PASSED']")
    private List<TestPlanCaseStatusEnum> statusList;

    @ZModelProperty(description = "用例等级，HIGH-高|MIDDLE-中|LOW-低", required = false, sample = "['MIDDLE']")
    private List<TestcasePriorityEnum> priorityList;

    @ZModelProperty(description = "自动化节点类型", required = false, sample = "['ThreadGroup']")
    private List<AutomaticNodeTypeEnum> nodeTypeList;

    @ZModelProperty(description = "用例类型", required = false, sample = "MANUAL")
    private TestcaseTypeEnum type;

    @ZModelProperty(description = "框架类型", required = false, sample = "['JMETER']")
    private List<AutomaticRecordTypeEnum> automaticTypeList;

}
