package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SchedulerCaseCodeListReq implements Serializable {

    @ZModelProperty(description = "产品Code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "定时任务Code", required = true, sample = "SNF975268955453128704")
    private String schedulerCode;

    @ZModelProperty(description = "父节点code", required = true, sample = "SNF975268955453128704")
    private String parentCode;

    @ZModelProperty(description = "搜索code或名称", required = false, sample = "任务名称")
    private String codeOrTitle;

    @ZModelProperty(description = "用例等级，HIGH-高|MIDDLE-中|LOW-低", required = false, sample = "['MIDDLE']")
    private List<TestcasePriorityEnum> priorityList;

    @ZModelProperty(description = "自动化节点类型 ", required = false, sample = "['TestPlan']")
    private List<AutomaticNodeTypeEnum> nodeTypeList;

    @ZModelProperty(description = "框架类型", required = false, sample = "['JMETER']")
    private List<AutomaticRecordTypeEnum> automaticTypeList;

}
