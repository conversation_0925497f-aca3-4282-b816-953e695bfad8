package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.model.testmanager.cases.entity.BatchOperateCaseVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchMoveTestcaseModelReq implements Serializable {

    @ZModelProperty(description = "用例集合", required = true, sample = "[]")
    private List<BatchOperateCaseVO> codeList;

    @ZModelProperty(description = "所属模块编码，不传为未分组用例", required = false, sample = "TC231130064892")
    private String parentCode;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;

}
