package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseBatchOperationEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZModel(description = "修改用例状态Req")
public class ChangeApiCaseStatusReq implements Serializable {
    private static final long serialVersionUID = -5788631906273223412L;

    @ZModelProperty(description = "用例code", required = true, sample = "TC123457988923")
    private String apiCaseCode;

    @ZModelProperty(description = "用例状态", required = true, sample = "edit")
    private ApiCaseStatusEnum status;

    @ZModelProperty(description = "操作", required = true, sample = "DELETED")
    private ApiCaseBatchOperationEnum operation;

}
