package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "造数共享/取消共享请求模型")
public class BatchSharePreDataReq implements Serializable {

    private static final long serialVersionUID = 1780979749106585307L;

    @ZModelProperty(description = "造数code列表", required = true, sample = "SNF954755053657260032")
    private List<String> sceneCodeList;

    @ZModelProperty(description = "共享状态(true-共享，false-取消)", required = true, sample = "true")
    private Boolean shareStatus;

}
