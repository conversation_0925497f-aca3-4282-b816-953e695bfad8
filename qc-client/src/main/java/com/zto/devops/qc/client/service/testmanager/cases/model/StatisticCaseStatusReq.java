package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "统计用例状态Req")
public class StatisticCaseStatusReq implements Serializable {

    @ZModelProperty(description = "用例code", sample = "['TC231130064892']")
    private List<String> caseCodeList;

}
