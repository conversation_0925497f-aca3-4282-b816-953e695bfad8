package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "获取授权用户请求模型")
public class QueryAuthorizedUsersReq implements Serializable {

    @ZsmpModelProperty(description = "产品code", required = true)
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

}
