package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "查询自动化任务执行空间请求模型")
@Data
public class ListExecuteEnvReq implements Serializable {
    private static final long serialVersionUID = -2114947231126744577L;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "空间名称", sample = "fat")
    private String keyWord;
}
