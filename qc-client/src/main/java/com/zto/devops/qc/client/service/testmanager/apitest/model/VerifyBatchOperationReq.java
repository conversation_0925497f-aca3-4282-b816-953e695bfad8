package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseBatchOperationEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ZModel(description = "校验批量操作Req")
@Data
public class VerifyBatchOperationReq implements Serializable {
    private static final long serialVersionUID = -1594753390590910853L;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "用例code结合", required = true, sample = "TC12345678965")
    private List<String> apiCaseCodeList;

    @ZModelProperty(description = "操作", required = true, sample = "DELETED")
    private ApiCaseBatchOperationEnum operation;

}
