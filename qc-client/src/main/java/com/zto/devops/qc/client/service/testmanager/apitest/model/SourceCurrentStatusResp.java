package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SourceCurrentStatusResp implements Serializable {

    @GatewayModelProperty(description = "登记库状态")
    private AutomaticStatusEnum sourceStatus;

    @GatewayModelProperty(description = "失败原因")
    private String failReason;

    @GatewayModelProperty(description = "登记库code")
    private String automaticSourceCode;

}
