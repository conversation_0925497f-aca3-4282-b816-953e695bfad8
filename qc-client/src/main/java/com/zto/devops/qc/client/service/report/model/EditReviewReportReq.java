package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewOpinionVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.ReviewRenewalVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EditReviewReportReq extends SaveBasicInfoReq implements Serializable {

    @ZModelProperty(description = "评审信息", required = true, sample = "{}")
    private ReviewInfoVO reviewInfo;

    @ZModelProperty(description = "评审观点", required = false, sample = "[]")
    private List<ReviewOpinionVO> reviewOpinions;

    @ZModelProperty(description = "评审更新信息", required = false, sample = "[]")
    private List<ReviewRenewalVO> reviewRenewals;

    @ZModelProperty(description = "附件", required = false, sample = "[]")
    private List<AttachmentVO> attachments;

}
