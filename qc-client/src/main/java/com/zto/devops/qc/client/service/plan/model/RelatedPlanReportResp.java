package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.model.testmanager.plan.entity.RelatedPlanVO;
import com.zto.devops.qc.client.model.testmanager.plan.entity.RelatedReportVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RelatedPlanReportResp implements Serializable {
    private static final long serialVersionUID = -6951796780394100301L;

    @GatewayModelProperty(description = "相关计划", required = false)
    private List<RelatedPlanVO> testPlan;

    @GatewayModelProperty(description = "相关报告", required = false)
    private List<RelatedReportVO> testReport;

}
