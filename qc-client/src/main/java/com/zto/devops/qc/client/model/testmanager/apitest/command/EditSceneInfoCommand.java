package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditSceneInfoCommand extends BaseCommand {

    private String productCode;

    private String sceneName;

    private String sceneInfoDesc;

    private String sceneFrontData;

    private String sceneBackData;

    private List<Integer> dbIds;

    private String parentCode;

    private String sceneType;

    public EditSceneInfoCommand(String aggregateId) {
        super(aggregateId);
    }
}
