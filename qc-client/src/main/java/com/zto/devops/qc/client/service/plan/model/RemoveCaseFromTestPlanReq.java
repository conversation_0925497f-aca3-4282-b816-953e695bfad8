package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class RemoveCaseFromTestPlanReq implements Serializable {

    @NotEmpty(message = "关联用例不为空")
    @ZModelProperty(description = "关联用例id", required = true, sample = "['111']")
    private List<String> caseIds;

    @NotEmpty(message = "测试阶段不为空")
    @ZModelProperty(description = "测试阶段", required = true, sample = "SMOKE_TEST")
    private TestPlanStageEnum testStage;

    @NotEmpty(message = "测试计划code不为空")
    @ZModelProperty(description = "测试计划code", required = true, sample = "TP240305003075")
    private String planCode;


}
