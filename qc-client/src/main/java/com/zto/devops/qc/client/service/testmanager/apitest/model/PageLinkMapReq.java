package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.RequestMethodEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@GatewayModel(description = "分页查询链路数据")
@Data
public class PageLinkMapReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 896678904895119198L;

    @GatewayModelProperty(description = "场景code")
    private String sceneCode;

    @GatewayModelProperty(description = "状态")
    private SceneInfoStatusEnum status;

    @GatewayModelProperty(description = "场景版本")
    private Integer sceneVersion;
}
