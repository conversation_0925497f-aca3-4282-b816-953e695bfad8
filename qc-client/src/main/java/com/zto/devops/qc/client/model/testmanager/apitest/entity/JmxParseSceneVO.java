package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.SceneBackData;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;


@Data
public class JmxParseSceneVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String productCode;

    private String sceneCode;

    private User user;

    private UseCaseFactoryTypeEnum type;

    // 全局变量
    private JSONObject globalVariable;

    // 全局头
    private JSONObject globalHeader;

    // scene
    private SceneBackData scene;

    private JSONObject schemaIdObj;

    private JSONArray postComponents;

    private JSONArray preComponents;

    public JmxParseSceneVO() {
        this.globalVariable = new JSONObject();
        this.globalHeader = new JSONObject();
        this.postComponents = new JSONArray();
        this.preComponents = new JSONArray();
        this.schemaIdObj = new JSONObject();
        SceneBackData scene = new SceneBackData();
        scene.setNodes(new JSONArray());
        scene.setLines(new JSONObject());
        scene.setStartNode(new ArrayList<>());
        scene.setEndNode(new ArrayList<>());
        scene.setDbIds(new HashSet<>());
        scene.setInputParameter(new ArrayList<>());
        scene.setOutputParameter(new ArrayList<>());
        this.scene = scene;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
        this.scene.setProductCode(productCode);
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
        this.scene.setSceneCode(sceneCode);
    }

}
