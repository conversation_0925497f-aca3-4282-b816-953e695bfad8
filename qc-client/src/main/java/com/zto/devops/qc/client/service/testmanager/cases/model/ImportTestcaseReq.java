package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.constants.ImportFileTypeEunm;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ImportTestcaseReq implements Serializable {

    @ZModelProperty(description = "所属产品code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "所属版本code", required = true, sample = "VER2302168133")
    private String versionCode;

    @ZModelProperty(description = "计划code", required = false, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "文件地址", required = false, sample = "111")
    private String fileUrl;

    @ZModelProperty(description = "文件名", required = true, sample = "111")
    private String fileName;

    @ZModelProperty(description = "私有组文件名", required = true, sample = "111")
    private String remoteFileId;

    @ZModelProperty(description = "文件类型", required = true, sample = "EXCEL")
    private ImportFileTypeEunm fileType;

    @ZModelProperty(description = "是否核心用例", required = true, sample = "0")
    private Boolean setCore;

    @ZModelProperty(description = "user", sample = "{}")
    private User user;

}
