package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.rpc.FlowEventEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "校验测试通过是否满足条件Req")
@Data
public class VerifyTestPassConditionReq implements Serializable {
    private static final long serialVersionUID = -8043110289540761109L;

    @ZModelProperty(description = "版本code", required = true, sample = "VER2401118036")
    private String versionCode;

    @ZModelProperty(description = "泳道事件", required = true, sample = "SMOKE_REPORT")
    private FlowEventEnum event;
}
