package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "批量发布接口测试用例入参")
public class BatchPublishApiTestCaseReq implements Serializable {
    private static final long serialVersionUID = 658076823866076707L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", required = true, sample = "399")
    @Size(min = 1, max = 100)
    private String productCode;

    @ZModelProperty(description = "用例编码集合", sample = "TC2402270190005")
    @Size(min = 1, max = 100)
    private List<String> caseCodeList;

    @ZModelProperty(description = "接口编码集合", sample = "SNF953691162659520512")
    @Size(min = 1, max = 100)
    private List<String> apiCodeList;

    @ZModelProperty(description = "是否按用例维度发布", required = true, sample = "true")
    private Boolean publishCaseFlag;

}
