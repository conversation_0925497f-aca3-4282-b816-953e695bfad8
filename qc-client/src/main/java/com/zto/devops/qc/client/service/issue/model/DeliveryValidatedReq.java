package com.zto.devops.qc.client.service.issue.model;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DeliveryValidatedReq implements Serializable {

    @ZModelProperty(description = "编号", required = true, sample = "ISS230303008063")
    private String code;

    @ZModelProperty(description = "备注 (JSON)", required = false, sample = "备注")
    private String content;

    @ZModelProperty(description = "版本名称", required = true, sample = "V1.0.0")
    private String fixVersion;

    @ZModelProperty(description = "版本Code", required = true, sample = "VER2302168133")
    private String fixVersionCode;

}
