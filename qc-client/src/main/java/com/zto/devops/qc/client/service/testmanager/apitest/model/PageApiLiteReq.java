package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@ZModel(description = "查询接口列表（场景图列表筛选）请求模型")
@Accessors(chain = true)
public class PageApiLiteReq implements Serializable {

    @ZModelProperty(description = "页数", required = true, sample = "1")
    private int page;

    @ZModelProperty(description = "数据大小", required = true, sample = "100")
    private int size;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "接口地址或名称", sample = "com.zto")
    private String nameOrAddress;

    @ZModelProperty(description = "接口类型：HTTP DUBBO", sample = "HTTP")
    private ApiTypeEnum apiType;

    @ZModelProperty(description = "应用id", sample = "devops-qc")
    private String appId;
}
