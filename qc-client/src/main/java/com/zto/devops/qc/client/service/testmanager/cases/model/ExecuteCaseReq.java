package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "执行用例详情请求模型")
@Data
public class ExecuteCaseReq implements Serializable {

    @ZModelProperty(description = "用例code", required = true, sample = "TC231130064892")
    private String testcaseCode;

    @ZModelProperty(description = "自动化任务code", required = false, sample = "SNF975268955453128704")
    private String automaticTaskCode;

}
