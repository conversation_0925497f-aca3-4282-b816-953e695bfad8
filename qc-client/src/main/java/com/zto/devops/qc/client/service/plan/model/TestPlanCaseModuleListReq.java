package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TestPlanCaseModuleListReq implements Serializable {
    private static final long serialVersionUID = 4691791308726083307L;

    @ZModelProperty(description = "计划Code", required = true, sample = "TP240305003075")
    private String code;

    @ZModelProperty(description = "产品Code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "测试阶段", required = false, sample = "['SMOKE_TEST']")
    private List<TestPlanStageEnum> testPlanStageList;

    @ZModelProperty(description = "用例类型", required = false, sample = "['MANUAL']")
    private List<TestcaseTypeEnum> caseTypeList;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;
}
