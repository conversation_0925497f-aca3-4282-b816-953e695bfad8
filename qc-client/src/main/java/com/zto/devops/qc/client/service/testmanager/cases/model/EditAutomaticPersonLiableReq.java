package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EditAutomaticPersonLiableReq implements Serializable {
    @ZModelProperty(description = "登记库code", required = true, sample = "SNF994641594810368000")
    private String code;
    /**
     * 更新人编码
     */
    @GatewayModelProperty(description = "责任人id", required = true, sample = "5878415")
    private Long personLiableId;

    /**
     * 更新人
     */
    @GatewayModelProperty(description = "责任人", required = true, sample = "责任人")
    private String personLiable;
}
