package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSON;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.DebugLinkVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.DebugNodeInfoVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZsmpModel(description = "查询调试链路响应模型")
public class QueryDebugLinkResp implements Serializable {
    private static final long serialVersionUID = 2585703645744919242L;

    @ZsmpModelProperty(description = "节点信息")
    private List<DebugLinkVO> linkBaseInfo;

    public static QueryDebugLinkResp buildSelf(String linkStr) {
        List<DebugNodeInfoVO> nodeInfoList = JSON.parseArray(linkStr, DebugNodeInfoVO.class);
        if (CollectionUtils.isEmpty(nodeInfoList)) {
            return new QueryDebugLinkResp();
        }
        List<DebugLinkVO> linkBaseInfo = new ArrayList<>(nodeInfoList.size());
        nodeInfoList.forEach(node -> {
            DebugLinkVO link = new DebugLinkVO();
            BeanUtils.copyProperties(node, link);
            linkBaseInfo.add(link);
        });

        return QueryDebugLinkResp.builder().linkBaseInfo(linkBaseInfo).build();
    }
}
