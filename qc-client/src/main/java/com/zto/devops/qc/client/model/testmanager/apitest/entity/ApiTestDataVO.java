package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ApiTestDataVO implements Serializable {

    private List<ApiTestFieldVO> data;
    private String dubboAppId;
    private String docName;
    private ApiTypeEnum type;
    private JSONObject rawResponse;
    private ApiTestEnableEnum apiEnable;

    /* http参数 */
    private List<ApiTestFieldVO> headers;
    private List<ApiTestFieldVO> params;
    private List<ApiTestFieldVO> pathParams;
    private List<ApiTestFieldVO> request;
    private String requestUrl;
    private JSONObject requestHeader;
    private JSONObject requestParam;
    private String requestBody;
    private String gatewayUrl;
    private String path;
    private Boolean gatewaySource;
    private String mockUrl;
    private String pathUrl;

    /* dubbo参数 */
    private List<ApiTestFieldVO> args;
    private JSONObject attachmentArgs;
    private String interfaceName;
    private String methodName;
    private String group;
    private String version;

    /* zms参数 */
    private String msgBody;

    /* datacenter参数 */
    private JSONArray inputParams;
    private List<ApiTestFieldVO> outputParams;
    private String dataCenterSceneCode;
}
