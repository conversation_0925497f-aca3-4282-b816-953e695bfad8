package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZModel(description = "jmx脚本导入模型")
public class JmxFileUploadReq implements Serializable {

    @ZModelProperty(description = "桶名", required = true)
    private String bucketName;

    @ZModelProperty(description = "地址", required = true)
    private String address;

    @ZModelProperty(description = "文件名", required = true)
    private String fileName;

    @ZModelProperty(description = "场景/造数code", required = true)
    private String sceneCode;

    @ZModelProperty(description = "场景/造数", required = true)
    private UseCaseFactoryTypeEnum type;

    @ZModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZModelProperty(description = "产品名", required = true)
    private String productName;

}
