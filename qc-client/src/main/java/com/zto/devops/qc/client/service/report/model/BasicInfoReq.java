package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BasicInfoReq implements Serializable {
    private static final long serialVersionUID = 7601508909914160508L;

    @ZModelProperty(description = "报告编号", required = false, sample = "TR240416001001")
    private String reportCode;

    @ZModelProperty(description = "报告名称", required = true, sample = "报告名称")
    private String reportName;

    @ZModelProperty(description = "所属产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "所属产品名称", required = true, sample = "一站式")
    private String productName;

    @ZModelProperty(description = "版本code", required = true, sample = "VER2401108039")
    private String versionCode;

    @ZModelProperty(description = "版本名称", required = true, sample = "版本名称")
    private String versionName;

    @ZModelProperty(description = "计划编号", required = true, sample = "TP240111008090")
    private String planCode;

    @ZModelProperty(description = "计划名称", required = true, sample = "计划名称")
    private String planName;

    @ZModelProperty(description = "收件人", required = true, sample = "[]")
    private List<SendUserInfoVO> receiveUsers;

    @ZModelProperty(description = "抄送人", required = false, sample = "[]")
    private List<SendUserInfoVO> ccUsers;

    @ZModelProperty(description = "预览邮件信息", required = true, sample = "预览邮件信息")
    private String preview;

    @ZModelProperty(description = "总结、分析、描述", required = false, sample = "总结")
    private String summary;

    @ZModelProperty(description = "总体测试结果", required = false, sample = "PASS")
    private String testResult;
}
