package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import java.io.Serializable;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Future;

public class CoverageThreadVO implements Serializable {

    private static final long serialVersionUID = 1L;


    public static ConcurrentMap<String, Future> coverageThreadMap = new ConcurrentHashMap<>();

}
