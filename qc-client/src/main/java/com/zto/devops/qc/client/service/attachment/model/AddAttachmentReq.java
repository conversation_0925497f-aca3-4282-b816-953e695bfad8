package com.zto.devops.qc.client.service.attachment.model;

import com.zto.devops.qc.client.enums.issue.AttachmentFileTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@GatewayModel(description = "附件模型")
public class AddAttachmentReq implements Serializable {

    @ZModelProperty(description = "编码", required = true, sample = "SNF987293442717515776")
    private String businessCode;

    @ZModelProperty(description = "附件路径", required = true, sample = "111")
    private String url;

    @ZModelProperty(description = "附件名称", required = true, sample = "111")
    private String name;

    @ZModelProperty(description = "私有组文件名", required = false, sample = "111")
    private String remoteFileId;

    @ZModelProperty(description = "附件类型 FILE 文件, URL 链接", required = true, sample = "FILE")
    private AttachmentTypeEnum type;

    @ZModelProperty(description = "文件类型 zip 文件, jpg 链接", required = false, sample = "zip")
    private AttachmentFileTypeEnum fileType;

    @ZModelProperty(description = "文件大小", required = false, sample = "10")
    private String size;
}