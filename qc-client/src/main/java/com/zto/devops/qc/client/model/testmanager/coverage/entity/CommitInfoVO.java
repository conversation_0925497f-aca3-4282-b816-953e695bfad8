package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import java.util.Objects;

public class CommitInfoVO {

    private Integer line;
    private String authorName;
    private String authoredDate;

    public Integer getLine() {
        return line;
    }

    public void setLine(Integer line) {
        this.line = line;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getAuthoredDate() {
        return authoredDate;
    }

    public void setAuthoredDate(String authoredDate) {
        this.authoredDate = authoredDate;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) return true;
        if (object == null || getClass() != object.getClass()) return false;
        CommitInfoVO that = (CommitInfoVO) object;
        return Objects.equals(line, that.line) && Objects.equals(authorName, that.authorName) && Objects.equals(authoredDate, that.authoredDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(line, authorName, authoredDate);
    }
}
