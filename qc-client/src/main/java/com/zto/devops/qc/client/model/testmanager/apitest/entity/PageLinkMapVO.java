package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.SingleLinkBaseInfo;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@GatewayModel(description = "分页查询链路数据")
@Data
public class PageLinkMapVO implements Serializable {
    private static final long serialVersionUID = -1696252078897976888L;

    @GatewayModelProperty(description = "总数")
    private Long total;

    @GatewayModelProperty(description = "链路数据")
    private List<SingleLinkBaseInfo> list;

    public static PageLinkMapVO buildSelf(List<SingleLinkBaseInfo> linkTableInfo, Long total) {
        PageLinkMapVO result = new PageLinkMapVO();
        result.setList(CollectionUtil.isEmpty(linkTableInfo) ? new ArrayList<>() : linkTableInfo);
        result.setTotal(CollectionUtil.isEmpty(linkTableInfo) ? 0l : total);
        return result;
    }
}
