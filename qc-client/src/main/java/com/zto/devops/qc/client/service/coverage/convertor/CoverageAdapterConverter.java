package com.zto.devops.qc.client.service.coverage.convertor;

import com.zto.devops.qc.client.model.parameter.CoverageRecordEditParameter;
import com.zto.devops.qc.client.model.parameter.CoverageRecordGenerateParameter;
import com.zto.devops.qc.client.model.testmanager.coverage.command.*;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageAppInfoVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageResultVO;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageTaskVO;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageResultListQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageResultQuery;
import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageTaskQuery;
import com.zto.devops.qc.client.service.coverage.model.req.*;
import com.zto.devops.qc.client.service.coverage.model.resp.CoverageTaskResp;
import com.zto.devops.qc.client.service.coverage.model.resp.TmCodeCoverResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CoverageAdapterConverter {

    GenerateCoverageCommand convertor(GenerateCoverageReq req);

    BatchCoverageCommand convertor(BatchCoverageReq req);

    @Mapping(target = "coverageReasonVOS", source = "appList")
    TmCodeCoverResultResp convertCodeCoverResult(CoverageResultVO vo);

    @Mapping(target = "appId", source = "appId")
    CoverageReasonVO convertReason(CoverageAppInfoVO infoVO);

    CoverageResultQuery convertResultQuery(TmCodeCoverResultReq req);

    @Mapping(target = "appIdList", expression = "java(java.util.Arrays.asList(req.getAppId()))")
    CoverageRecordGenerateParameter converter(GenerateCoverageReq req);

    CoverageRecordGenerateParameter converter(BatchCoverageReq req);

    CoverageRecordEditParameter converter(EditCoverageReq req);

    EditCoverageCommand convert(EditCoverageReq req);

    CoverageNotStandardReasonEditCommand convert(CoverageReasonEditReq req);

    CoverageTaskQuery converter(CoverageTaskReq req);

    List<CoverageTaskResp> convertList(List<CoverageTaskVO> list);

    CoverageTaskResp converter(CoverageTaskVO vo);

    CoverageResultListQuery convertResultListQuery(TmCodeCoverResultListReq req);

    VerifyCoverageConditionCommand convertor(VerifyGenerateConditionReq req);
}
