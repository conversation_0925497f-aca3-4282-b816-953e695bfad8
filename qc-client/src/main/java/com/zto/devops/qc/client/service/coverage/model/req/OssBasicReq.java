package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022/11/17 14:20
 */
@Data
@ZModel(description = "oss基础接口入参")
public class OssBasicReq implements Serializable {

    private static final long serialVersionUID = 1870509636783316888L;

    @ZModelProperty(description = "存储桶名称", required = true, sample = "coverage-report-bucket")
    private String bucketName;

    @ZModelProperty(description = "文件名", required = true, sample = "VER2308098037-release-V2.92.0-zto-qamp/release-V2.92.0/b19147c71afa4ccf628bb07527f6472822170bba/20231226092112/coveragereport/index.html")
    private String fileName;

    @ZModelProperty(description = "产品code", sample = "399")
    private String productCode;

}
