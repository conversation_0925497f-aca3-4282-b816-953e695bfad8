package com.zto.devops.qc.client.service.tag.model;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@GatewayModel(description = "模型")
public class BatchAddTagReq implements Serializable {

    @GatewayModelProperty(description = "业务编码: 业务实例 如 缺陷编码等集合", required = false)
    private List<String> businessCodes;

    @GatewayModelProperty(description = "标签名称")
    private List<String> tagNames;
}