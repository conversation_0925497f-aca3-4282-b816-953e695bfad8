package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.RequestMethodEnum;
import lombok.Data;

@Data
public class ApiSampleCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;


    private String apiCode;

    private String apiName;

    private RequestMethodEnum reqMethod;

    private String apiData;

    private Long docId;

    private String docProductCode;

    private String productCode;

    private String appId;

    private ApiTypeEnum apiType;

    private String apiAddress;

    private String docVersion;
}
