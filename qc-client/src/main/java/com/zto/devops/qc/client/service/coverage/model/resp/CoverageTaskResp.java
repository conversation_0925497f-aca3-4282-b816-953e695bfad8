package com.zto.devops.qc.client.service.coverage.model.resp;

import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageTaskVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CoverageTaskResp {

    @ZsmpModelProperty(description = "任务id")
    private String taskId;

    @ZsmpModelProperty(description = "AppId")
    private String appId;

    @ZsmpModelProperty(description = "标准值")
    private BigDecimal standardRate;

    @ZsmpModelProperty(description = "覆盖率值")
    private BigDecimal recordRate;

    @ZsmpModelProperty(description = "生成状态")
    private RecordStatusEnum recordStatus;

    @ZsmpModelProperty(description = "生成状态描述")
    private String recordStatusDesc;

    @ZsmpModelProperty(description = "应用数")
    private Integer appNums;

    @ZsmpModelProperty(description = "覆盖率类型")
    private RecordTypeEnum recordType;

    @ZsmpModelProperty(description = "覆盖率类型描述")
    private String recordTypeDesc;

    @ZsmpModelProperty(description = "差异类型")
    private DiffTypeEnum diffType;

    @ZsmpModelProperty(description = "差异类型描述")
    private String diffTypeDesc;

    @ZsmpModelProperty(description = "失败原因")
    private String recordErrorMsg;

    @ZsmpModelProperty(description = "版本code")
    private String versionCode;

    @ZsmpModelProperty(description = "版本名称")
    private String versionName;

    @ZsmpModelProperty(description = "空间")
    private String envName;

    @ZsmpModelProperty(description = "生成人")
    private String recordCreator;

    @ZsmpModelProperty(description = "生成时间")
    private Date recordCreate;

    @ZsmpModelProperty(description = "更新时间")
    private Date recordModified;

    @ZsmpModelProperty(description = "备注")
    private String comment;

    @ZsmpModelProperty(description = "子节点覆盖率")
    private List<CoverageTaskVO> children;

    @ZsmpModelProperty(description = "存储桶名")
    private String bucketName;

    @ZsmpModelProperty(description = "文件名")
    private String fileName;

}
