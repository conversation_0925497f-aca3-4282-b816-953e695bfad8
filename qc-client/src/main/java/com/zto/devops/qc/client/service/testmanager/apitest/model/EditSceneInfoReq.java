package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSONObject;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "场景保存请求模型")
public class EditSceneInfoReq implements Serializable {

    @ZsmpModelProperty(description = "场景code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "前端数据")
    private String sceneFrontData;

    @ZsmpModelProperty(description = "后端数据")
    private JSONObject sceneBackData;

    @ZsmpModelProperty(description = "数据库编号")
    private List<Integer> dbIds;

}
