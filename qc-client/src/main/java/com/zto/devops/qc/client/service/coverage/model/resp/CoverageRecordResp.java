package com.zto.devops.qc.client.service.coverage.model.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class CoverageRecordResp implements Serializable {

    private static final long serialVersionUID = 1L;

    public CoverageRecordResp() {

    }

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * appId
     */
    private String appId;

    /**
     * 报告或错误日志地址
     */
    private String recordUrl;

    /**
     * 分支名称
     */
    private String branchName;

    /**
     * git提交id
     */
    private String commitId;

    /**
     * 基准分支名称
     */
    private String basicBranchName;

    /**
     * 基准commitId
     */
    private String basicCommitId;

}
