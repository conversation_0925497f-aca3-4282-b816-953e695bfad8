package com.zto.devops.qc.client.service.agent.model;

import com.zto.devops.qc.client.enums.agent.ChaosExceptionTypeEnum;
import com.zto.devops.qc.client.enums.agent.ChaosRuleTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ZModel(description = "规则配置接口入参")
public class RuleConfigReq implements Serializable {

    private static final long serialVersionUID = -5515287544758644517L;

    @ZModelProperty(description = "规则id", sample = "1")
    Long id;

    @ZModelProperty(description = "产品编号", sample = "399")
    String productCode;

    @ZModelProperty(description = "注入应用", sample = "devops-qc")
    String appid;

    @ZModelProperty(description = "版本tag", sample = "z4692v16163700fat")
    String dynEnv;

    @ZModelProperty(description = "注入规则名称", sample = "规则名称")
    String injectionRuleName;

    @ZModelProperty(description = "agentIp", sample = "127.0.0.1")
    String agentIp;

    @ZModelProperty(description = "版本code", sample = "VER24072900127")
    String versionCode;

    @ZModelProperty(description = "注入项类名", sample = "com.zto.qamp.service.impl.DubboTestServiceImpl")
    String className;

    @ZModelProperty(description = "注入项方法名", sample = "methodName")
    String methodName;

    @ZModelProperty(description = "注入类型:DubboClient,HttpClient,ServiceClass,Other", sample = "ServiceClass")
    ChaosRuleTypeEnum ruleType;

    @ZModelProperty(description = "注入规则:FlowException,DegradeException,TimeoutException,ServerDown,CustomException,ResultMock", sample = "CustomException")
    ChaosExceptionTypeEnum exceptionType;

    @ZModelProperty(description = "注入规则体", sample = "")
    Map<String, Object> ruleBody;

    @ZModelProperty(description = "删除原因", sample = "未匹配到异常信息，系统自动删除")
    String reason;

    @ZModelProperty(description = "状态", sample = "0-停用|1-启用|2-失效")
    Integer status;

    @ZModelProperty(description = "状态集", sample = "[0,1,2]")
    List<Integer> statusList;

}
