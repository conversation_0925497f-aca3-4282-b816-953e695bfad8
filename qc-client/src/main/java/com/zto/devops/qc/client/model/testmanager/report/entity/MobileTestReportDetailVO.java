package com.zto.devops.qc.client.model.testmanager.report.entity;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MobileTestReportDetailVO extends BaseReportInfoVO implements Serializable {

    private static final long serialVersionUID = 6958614171337711266L;

    @GatewayModelProperty(description = "实际测试开始时间", required = false)
    private Date actualTestStart;

    @GatewayModelProperty(description = "实际测试开始时间--上下午", required = false)
    private String actualTestStartDay;

    @GatewayModelProperty(description = "实际测试结束时间", required = false)
    private Date actualTestEnd;

    @GatewayModelProperty(description = "实际测试结束时间-- 上下午", required = false)
    private String actualTestEndDay;

    @GatewayModelProperty(description = "更新测试结果时间", required = false)
    private Date updateTestResultDate;

    @GatewayModelProperty(description = "关联计划编号", required = false)
    private String relationPlanCode;

    @GatewayModelProperty(description = "模块测试结果", required = false)
    private List<TmModuleTestVO> moduleTestVOS;
}
