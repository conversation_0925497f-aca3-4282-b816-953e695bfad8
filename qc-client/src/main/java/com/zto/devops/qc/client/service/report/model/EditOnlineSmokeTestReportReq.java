package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EditOnlineSmokeTestReportReq extends SaveBasicInfoReq implements Serializable {

    private static final long serialVersionUID = 7869825442153132054L;

    @ZModelProperty(description = "总结、分析、描述", required = false, sample = "生产冒烟通过")
    private String summary;

    @ZModelProperty(description = "实际上线时间", required = false, sample = "1711987200000")
    private Date actualPublishDate;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false, sample = "-1")
    private Integer delay;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false, sample = "否")
    private String delayDesc;

    @ZModelProperty(description = "是否按计划范围上线", required = false, sample = "1")
    private Integer asPlanedOnline;

    @ZModelProperty(description = "总体测试结果", required = false, sample = "ONCE_SUCCESS")
    private TmTestResultEnum testResult;

    @ZModelProperty(description = "zui测试结果", required = false, sample = "PASS")
    private ZUITestResultEnum zuiTestResult;

}
