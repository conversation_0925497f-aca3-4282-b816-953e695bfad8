package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@GatewayModel(description = "查询可执行用例数Req")
@Data
public class CountCaseReq implements Serializable {
    private static final long serialVersionUID = 1724149356469360583L;

    @ZsmpModelProperty(description = "接口code集合", required = true, sample = "SNF944588832618053632")
    private List<String> apiCodeList;

    @ZsmpModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;
}
