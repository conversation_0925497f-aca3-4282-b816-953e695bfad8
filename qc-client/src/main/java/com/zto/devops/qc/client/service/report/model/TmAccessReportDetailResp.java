package com.zto.devops.qc.client.service.report.model;

import cn.hutool.core.date.DateUtil;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanDatePartitionEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@ZModel(description = "查询准入详情Resp")
@Data
public class TmAccessReportDetailResp extends BasicInfoResp {

    @ZModelProperty(description = "报告类型", required = false)
    private ReportType reportType;

    @ZModelProperty(description = "报告类型描述", required = false)
    private String reportTypeDesc;

    @ZModelProperty(description = "计划提测时间", required = false)
    private Date presentationDate;

    @ZModelProperty(description = "计划提测时间-上下午", required = false)
    private String presentationDay;

    @ZModelProperty(description = "计划提测时间-上下午", required = false)
    private TestPlanDatePartitionEnum accessDatePartition;

    @ZModelProperty(description = "实际提测时间", required = false)
    private Date actualPresentationDate;

    @ZModelProperty(description = "实际提测时间-上下午", required = false)
    private String actualPresentationDay;

    public String getPresentationDay() {
        return Objects.isNull(this.accessDatePartition) ? "" : TestPlanDatePartitionEnum.PM.equals(this.accessDatePartition) ? "18:00:00" : "09:00:00";
    }

    public String getActualPresentationDay() {
        return Objects.isNull(this.actualPresentationDate) ? "" : DateUtil.isPM(this.actualPresentationDate) ? "18:00:00" : "09:00:00";
    }

    public String getReportTypeDesc() {
        return Objects.isNull(this.reportType) ? "" : this.reportType.getValue();
    }
}
