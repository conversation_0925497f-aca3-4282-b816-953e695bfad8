package com.zto.devops.qc.client.service.report;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.report.entity.TestReportDateByVersionCodeVO;
import com.zto.devops.qc.client.model.report.entity.TestReportResultVO;
import com.zto.devops.qc.client.model.report.query.ListReportDateByVersionCodesQuery;
import com.zto.devops.qc.client.model.report.query.ReportTestResultQuery;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.devops.qc.client.service.report.model.*;

import java.util.List;

/**
 * report service
 *
 * <AUTHOR>
 * @date 2023-03-30 14:25
 **/
public interface ITestReportService {

    /**
     * query report result
     *
     * @param query query
     * @return report result
     */
    List<TestReportResultVO> queryTestReportResult(ReportTestResultQuery query);

    Result<OperatedTestManageResp> operatedTestManage(OperatedTestManageReq req);

    Result<TmAccessReportDetailResp> detailAccessReport(QueryTestReportDetailReq req);

    Result<TmOnlineSmokeReportDetailResp> detailOnlineSmokeReport(QueryTestReportDetailReq req);

    Result<TmPermitReportDetailResp> detailPermitReport(QueryTestReportDetailReq req);

    Result<CaseExecuteResultVO> queryCaseExecuteResult(QueryCaseExecuteResultReq req);

    Result<ExternalTestReportResp> queryExternalTestReportDetail(QueryReportDetailReq req);

    Result<Void> editAccessReport(EditAccessTestReportReq req);

    Result<Void> editOnlineSmokeReport(EditOnlineSmokeTestReportReq req);

    Result<Void> editSendReviewReport(EditAndSendReviewReportReq req);

    Result<Void> editPermitReport(EditPermitTestReportReq req);

    Result<Void> sendSimpleTestReport(SendSimpleTestReportReq req);

    Result<ReviewReportDetailResp> detailReviewReport(QueryReviewReportReq req);

    Result<Void> addReviewReport(EditReviewReportReq req);

    Result<MobileTestReportDetailResp> queryMobileTestReportDetail(QueryReportDetailReq req);

    Result<Void> addExternalTestReport(EditExternalTestReportReq req);

    Result<Void> addMobileTestReport(EditMobileTestReportReq req);

    Result<SecurityTestResultResp> querySecurityTestResult(String versionCode);

    Result<SimpleTestReportDetailResp> querySimpleTestReportDetail(QueryTestReportDetailReq req);

    Result<Void> saveSimpleTestReport(SaveSimpleTestReportReq req);

    Result<Void> sendAccessReport(AddAccessTestReportReq req);

    Result<Void> editAndSendExternalTestReport(AddExternalTestReportReq req);

    Result<Void> editAndSendMobileTestReport(AddMobileTestReportReq req);

    Result<Void> sendOnlineSmokeReport(AddOnlineSmokeTestReportReq req);

    Result<Void> sendPermitReport(AddPermitTestReportReq req);

    Result<List<AllReportDateResp>> queryReportList(PageReportReq req);

    /**
     * 发送线上冒烟报告前，校验准出报告测试结果
     *
     * @param req {@link QueryPermitResultReq}
     * @return {@link QueryPermitResultResp}
     */
    Result<QueryPermitResultResp> queryPermitResult(QueryPermitResultReq req);

    Result<Void> computeMetrics(ComputeMetricsReq req);

    /**
     * 根据版本code，查测试报告时间节点 （devops-project更新版本字段）
     * @param query {@link ListReportDateByVersionCodesQuery}
     * @return {@link TestReportDateByVersionCodeVO}
     */
    List<TestReportDateByVersionCodeVO> ListReportDateByVersionCodes(ListReportDateByVersionCodesQuery query);
}
