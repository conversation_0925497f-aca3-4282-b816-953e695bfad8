package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class ListRedisQueryVO implements Serializable {
    private static final long serialVersionUID = 5355648609393425247L;

    /**
     * 集群id
     */
    private Integer clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群名称（自定义）
     */
    private String resourceName;

    /**
     * 集群节点
     */
    private Map<String, String> nodeInfo;

    /**
     * 密码
     */
    private String password;

    public static Map<String, Map<String, String>> buildConfigMap(Set<String> clusterIdList, List<ListRedisQueryVO> voList) {
        if (CollectionUtil.isEmpty(clusterIdList) || CollectionUtil.isEmpty(voList)) {
            return new HashMap<>();
        }
        Map<String, Map<String, String>> resultMap = new HashMap<>();
        clusterIdList.forEach(clusterId -> {
            voList.forEach(vo -> {
                if (String.valueOf(vo.getClusterId()).equals(clusterId)) {
                    Map<String, String> nodeInfoMap = new HashMap<>();
                    nodeInfoMap.put("nodes", vo.getNodeInfo().getOrDefault("spring.redis.cluster.nodes", ""));
                    nodeInfoMap.put("password", vo.getPassword());
                    resultMap.put(clusterId, nodeInfoMap);
                }
            });
        });
        return resultMap;
    }
}
