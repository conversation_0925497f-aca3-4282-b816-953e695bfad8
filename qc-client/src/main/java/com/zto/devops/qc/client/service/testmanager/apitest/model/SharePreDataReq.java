package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "造数共享/取消共享请求模型")
public class SharePreDataReq implements Serializable {

    @ZsmpModelProperty(description = "造数code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "共享状态", required = true)
    private Boolean shareStatus;
}
