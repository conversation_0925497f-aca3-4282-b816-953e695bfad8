package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PageSchedulerReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 2195578419839913893L;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "任务名", required = false, sample = "任务名")
    private String schedulerName;

    public String getSchedulerName() {
        if (StringUtil.isNotBlank(this.schedulerName)) {
            return this.schedulerName.replaceAll("%", "/%").replaceAll("_", "/_");
        }
        return this.schedulerName;
    }

    @ZModelProperty(description = "运行结果", required = false, sample = "['NOT_STARTED']")
    private List<AutomaticStatusEnum> resultList;

    @ZModelProperty(description = "是否开启定时任务", required = false, sample = "[0]")
    private List<Boolean> switchFlagList;

    @ZModelProperty(description = "运行环境", required = false, sample = "['fat']")
    private List<String> envList;

    @ZModelProperty(description = "创建人", required = false, sample = "[5984549]")
    private List<Long> creatorList;

    @ZModelProperty(description = "运行开始时间", required = false, sample = "1711987200000")
    private Date executeStartTime;

    @ZModelProperty(description = "运行结束时间", required = false, sample = "1711987200000")
    private Date executeEndTime;

}
