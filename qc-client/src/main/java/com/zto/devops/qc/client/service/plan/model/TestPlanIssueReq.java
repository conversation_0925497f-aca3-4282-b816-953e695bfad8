package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.issue.IssuePriority;
import com.zto.devops.qc.client.enums.issue.IssueStatus;
import com.zto.devops.qc.client.enums.issue.RelatedToMeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TestPlanIssueReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 592954220240997586L;

    @ZModelProperty(description = "主键Code", required = true, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "缺陷名称或编号", required = false, sample = "ISS230303008063")
    private String codeOrTitle;

    @ZModelProperty(description = "缺陷状态", required = false, sample = "['START']")
    private List<IssueStatus> statusList;

    @ZModelProperty(description = "当前处理人", required = false, sample = "[5984549]")
    private List<Long> handleUserIdList;

    @ZModelProperty(description = "开发人员", required = false, sample = "[5984549]")
    private List<Long> developUserIdList;

    @ZModelProperty(description = "测试人员", required = false, sample = "[5984549]")
    private List<Long> testUserIdList;

    @ZModelProperty(description = "与我相关", required = false, sample = "['CREATOR']")
    private List<RelatedToMeEnum> relatedList;

    @ZModelProperty(description = "缺陷优先级", required = false, sample = "['MIDDLE']")
    private List<IssuePriority> priorityList;

}
