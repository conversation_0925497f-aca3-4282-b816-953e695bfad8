package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseRelationVO;
import com.zto.devops.qc.client.model.testmanager.cases.entity.TestcaseStepVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddTestcaseReq implements Serializable {

    @ZModelProperty(description = "code", required = false, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "所属产品code", required = false, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "所属模块or上级code", required = false, sample = "TC231130064892")
    private String parentCode;

    @ZModelProperty(description = "标题", required = false, sample = "111")
    private String name;

    @ZModelProperty(description = "属性 测试用例/模块", required = false, sample = "TESTCASE")
    private TestcaseAttributeEnum attribute;

    @ZModelProperty(description = "用例等级 HIGH MIDDLE LOW", required = false, sample = "MIDDLE")
    private TestcasePriorityEnum priority;

/*    @GatewayModelProperty(description = "用例状态 NORMAL DISABLE", required = false)
    private TestcaseStatusEnum status;*/

    @ZModelProperty(description = "前置条件", required = false, sample = "前置条件")
    private String precondition;

    @ZModelProperty(description = "责任人编码", required = false, sample = "5984549")
    private Long dutyUserId;

    @ZModelProperty(description = "责任人", required = false, sample = "责任人")
    private String dutyUser;

    @ZModelProperty(description = "备注", required = false, sample = "备注")
    private String comment;

    @ZModelProperty(description = "层级", required = false, sample = "1")
    private Integer layer;

    @ZModelProperty(description = "所有父code", required = false, sample = "TC231130064892")
    private String path;

    @ZModelProperty(description = "附件", required = false, sample = "[]")
    private List<AttachmentVO> attachments;

    @ZModelProperty(description = "标签", required = false, sample = "[]")
    private List<TagVO> tags;

    @ZModelProperty(description = "关联类型及code", required = false, sample = "[]")
    private List<TestcaseRelationVO> vos;

    @ZModelProperty(description = "测试步骤", required = false, sample = "[]")
    private List<TestcaseStepVO> testSteps;

    @ZModelProperty(description = "类型", required = false, sample = "MANUAL")
    private TestcaseTypeEnum type;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;

    @ZModelProperty(description = "版本code", required = false, sample = "VER2302168133")
    private String versionCode;
}
