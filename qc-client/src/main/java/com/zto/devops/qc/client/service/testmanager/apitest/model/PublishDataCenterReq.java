package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PublishDataCenterReq implements Serializable {

    private static final long serialVersionUID = 5227273168744954464L;

    @ZsmpModelProperty(description = "场景code", required = true)
    private String sceneCode;

    @ZsmpModelProperty(description = "产品code", required = true)
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

}
