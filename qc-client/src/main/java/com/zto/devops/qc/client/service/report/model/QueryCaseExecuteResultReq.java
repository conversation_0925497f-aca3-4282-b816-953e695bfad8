package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryCaseExecuteResultReq implements Serializable {

    @ZModelProperty(description = "报告code", required = false, sample = "TR240416001001")
    private String reportCode;

    @ZModelProperty(description = "计划code", required = true, sample = "TP240416003003")
    private String planCode;

    @ZModelProperty(description = "报告类型", required = true, sample = "TEST_ACCESS")
    private TestReportTypeEnum reportType;
}
