package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.model.testmanager.cases.entity.XmindDeleteVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchXmindDeleteReq implements Serializable {

    private static final long serialVersionUID = 7988614729258574027L;

    @ZModelProperty(description = "用例或分组List", required = true, sample = "[]")
    private List<XmindDeleteVO> codeList;

}
