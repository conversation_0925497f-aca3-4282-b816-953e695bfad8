package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageApiTestVariableReq extends PageQueryBase implements Serializable {

    @GatewayModelProperty(description = "所属产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "变量名称", required = false)
    private String variableName;

    @GatewayModelProperty(description = "变量键", required = false)
    private String variableKey;

    @GatewayModelProperty(description = "类型", required = false)
    private List<VariableTypeEnum> typeList;

    @GatewayModelProperty(description = "变量状态", required = false)
    private List<Boolean> statusList;

}
