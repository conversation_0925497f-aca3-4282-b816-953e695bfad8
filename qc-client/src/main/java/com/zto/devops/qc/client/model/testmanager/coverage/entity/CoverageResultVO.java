package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/20 16:52
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoverageResultVO implements Serializable {

    @GatewayModelProperty(description = "不达标应用集")
    private List<CoverageAppInfoVO> appList;

    @GatewayModelProperty(description = "代码覆盖率是否达标结果-enum", required = false)
    private CodeCoverResult codeCoverResult;

    @GatewayModelProperty(description = "代码覆盖率是否达标结果-描述", required = false)
    private String codeCoverResultDesc;

    @ZsmpModelProperty(description = "代码覆盖率不达标原因枚举列表")
    private List<String> reasonList;

    /**
     * 初始化参数.
     *
     * @return {@link CoverageResultVO}
     */
    public static CoverageResultVO init() {
        return CoverageResultVO.builder()
                .appList(new ArrayList<>())
                .reasonList(new ArrayList<>())
                .codeCoverResult(CodeCoverResult.STANDARD)
                .codeCoverResultDesc(CodeCoverResult.STANDARD.getName())
                .build();
    }

    /**
     * 组装返回值
     *
     * @param appList 不达标appid列表
     * @return {@link CoverageResultVO}
     */
    public static CoverageResultVO buildSelf(List<CoverageAppInfoVO> appList, String codeCoverageReason) {
        if (null == appList || appList.size() < 1) {
            return init();
        }

        //不达标原因
        List<String> reasonList = new ArrayList<>();
        if (null != codeCoverageReason && !"".equals(codeCoverageReason) && !" ".equals(codeCoverageReason)) {
            reasonList = Arrays.asList(codeCoverageReason.split(";"));
        }

        return CoverageResultVO.builder()
                .appList(appList)
                .reasonList(reasonList)
                .codeCoverResult(CodeCoverResult.SUBSTANDARD)
                .codeCoverResultDesc(CodeCoverResult.SUBSTANDARD.getName())
                .build();
    }
}