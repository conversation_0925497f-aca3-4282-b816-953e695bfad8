package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import java.io.Serializable;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;

import lombok.Data;

import java.util.List;

@Data
@ZsmpModel(description = "单接口调试响应模型")
public class DebugApiTaskInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZModelProperty(description = "任务id")
    private String taskId;

    @ZModelProperty(description = "用例code")
    private String caseCode;

    @ZModelProperty(description = "用例名称")
    private String caseName;

    @ZModelProperty(description = "执行环境")
    private String ztoenv;

    @ZModelProperty(description = "执行环境")
    private String envName;

    @ZModelProperty(description = "产品名称")
    private String productCode;

    @ZModelProperty(description = "执行用例列表")
    private List<DebugApiCaseData> caseDataList;
}
