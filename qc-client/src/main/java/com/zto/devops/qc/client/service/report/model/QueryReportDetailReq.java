package com.zto.devops.qc.client.service.report.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryReportDetailReq implements Serializable {

    @ZModelProperty(description = "报告code", required = false, sample = "TR240416001001")
    private String reportCode;

    @ZModelProperty(description = "计划code", required = true, sample = "TP240416002001")
    private String planCode;

}
