package com.zto.devops.qc.client.model.testmanager.apitest.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PreDataSharedEvent extends BaseEvent implements ActionEvent {

    private String sceneCode;

    private boolean shareStatus;

    @Override
    public String action() {
        return shareStatus ? "共享了造数" : "取消共享了造数";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }

    @Override
    public String getAggregateId() {
        return this.sceneCode;
    }
}
