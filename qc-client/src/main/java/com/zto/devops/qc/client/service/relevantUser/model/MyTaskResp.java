package com.zto.devops.qc.client.service.relevantUser.model;

import java.io.Serializable;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 项目名称：user-parent
 * 类 名 称：MyTaskResp
 * 类 描 述：TODO
 * 创建时间：2021/10/12 5:08 下午
 * 创 建 人：bulecat
 */
@Data
public class MyTaskResp implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "状态")
    private String status;

    @GatewayModelProperty(description = "状态")
    private String statusDesc;

    @GatewayModelProperty(description = "名称")
    private String name;

    @GatewayModelProperty(description = "优先级")
    private String priority;

    @GatewayModelProperty(description = "优先级")
    private String priorityDesc;

    @GatewayModelProperty(description = "业务域")
    private String domain;

    @GatewayModelProperty(description = "业务域")
    private String domainDesc;

    @GatewayModelProperty(description = "起止时间")
    private String spannedTime;

    @GatewayModelProperty(description = "计划工时")
    private String planWorkHours;

    @GatewayModelProperty(description = "提醒")
    private String warn;

    @GatewayModelProperty(description = "天数")
    private double warnDay;

    @GatewayModelProperty(description = "所属产品")
    private Product product;

    @GatewayModelProperty(description = "所属产品")
    private String productCode;

    @GatewayModelProperty(description = "所属产品")
    private String productName;

    @GatewayModelProperty(description = "所属产品")
    private String level;

    @GatewayModelProperty(description = "发现版本")
    private Version findVersion;
    @GatewayModelProperty(description = "发现版本")
    private String findVersionCode;
    @GatewayModelProperty(description = "发现版本")
    private String findVersionName;

    @GatewayModelProperty(description = "开发人员")
    private User developer;
    @GatewayModelProperty(description = "开发人员")
    private String developerUserName;
    @GatewayModelProperty(description = "开发人员")
    private Long developerUserId;

    @GatewayModelProperty(description = "测试人员")
    private User tester;
    @GatewayModelProperty(description = "测试人员")
    private String testerUserName;
    @GatewayModelProperty(description = "测试人员")
    private Long testerUserId;

    @GatewayModelProperty(description = "开始时间")
    private Date startDate;

    @GatewayModelProperty(description = "发布时间")
    private Date publishDate;

    @GatewayModelProperty(description = "结束时间")
    private Date endTime;

    @GatewayModelProperty(description = "项目规模")
    private String projectScale;

    @GatewayModelProperty(description = "项目类型")
    private String projectType;

    @GatewayModelProperty(description = "关联版本")
    private Version associatedVersion;
    @GatewayModelProperty(description = "关联版本")
    private String associatedVersionCode;
    @GatewayModelProperty(description = "关联版本")
    private String associatedVersionName;

    @GatewayModelProperty(description = "业务code")
    private String businessCode;

    @GatewayModelProperty(description = "处理日期")
    private List<Button> buttonVOs;

    @GatewayModelProperty(description = "创建时间")
    private Date gmtCreate;

}
