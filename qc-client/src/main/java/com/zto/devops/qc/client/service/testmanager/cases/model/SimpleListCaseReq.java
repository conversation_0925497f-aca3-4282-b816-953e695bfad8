package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/10/27
 * @Version 1.0
 */
@Data
public class SimpleListCaseReq extends PageQueryBase implements Serializable {

    @ZModelProperty(description = "搜索的关键字", required = true, sample = "111")
    private String searchKey;

    @ZModelProperty(description = "产品编码", required = true, sample = "399")
    private String productCode;
}
