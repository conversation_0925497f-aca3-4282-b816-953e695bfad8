package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.alibaba.fastjson.JSONArray;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;

@Data
public class IfAssertComponent extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

//    private String condition; 条件表达式
//    private String expected;  断言脚本
    /**
     * [{"condition":"condition","expected":"expected"},...]*
     */
    JSONArray conditionExpected;
}
