package com.zto.devops.qc.client.service.issue.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FindUnClosedReq implements Serializable {

    @ZModelProperty(description = "版本CODE集合", required = true, sample = "['VER2302168133']")
    private List<String> versionCodes;
}
