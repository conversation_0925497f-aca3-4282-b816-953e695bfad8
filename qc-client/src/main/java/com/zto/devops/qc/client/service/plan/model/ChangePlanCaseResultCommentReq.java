package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.service.testmanager.cases.model.PlanCaseReq;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@ZsmpModel(description = "变更测试计划用例结果备注")
@Getter
@Setter
public class ChangePlanCaseResultCommentReq extends PlanCaseReq implements Serializable {

    @ZModelProperty(description = "结果备注", required = false, sample = "结果备注")
    private String resultComment;

    @ZModelProperty(description = "日志记录code", required = true, sample = "SNF974191934769725441")
    private String operateCaseCode;
}
