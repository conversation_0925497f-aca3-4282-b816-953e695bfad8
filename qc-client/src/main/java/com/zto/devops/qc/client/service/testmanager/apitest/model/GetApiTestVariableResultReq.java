package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class GetApiTestVariableResultReq implements Serializable {

    @GatewayModelProperty(description = "所属产品code", required = true)
    private String productCode;

    @GatewayModelProperty(description = "变量键", required = true)
    private String variableKey;

    @GatewayModelProperty(description = "链路编号", required = true)
    private String linkCode;

    @GatewayModelProperty(description = "命名空间", required = true)
    private String nameSpace;

}
