package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TransitionNodeResp implements Serializable {

    /**
     * 编码
     */
    private String code;

    /**
     * 所属领域
     */
    private String domain;
//    private DomainEnum domain;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 当前节点
     */
    private String curStatus;
//    private IssueStatus curStatus;

    /**
     * 节点内容json存储
     */
    private String content;

    private String reason;
//    private Reason reason;

    /**
     * next节点
     */
    private String nextStatus;
//    private IssueStatus nextStatus;

    /**
     * 是否删除 1 未删除, 0 已删除
     */
    private Boolean enable;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

    public void preCreate(BaseEvent baseEvent) {
        this.setGmtCreate(new Date());
        this.setGmtModified(new Date());
        User user = baseEvent.getTransactor();
        if (null != user) {
            this.setCreatorId(user.getUserId());
            this.setCreator(user.getUserName());
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
        }
        Date occurred = baseEvent.getOccurred();
        if(occurred != null){
            this.setGmtCreate(occurred);
            this.setGmtModified(occurred);
        }

    }

}
