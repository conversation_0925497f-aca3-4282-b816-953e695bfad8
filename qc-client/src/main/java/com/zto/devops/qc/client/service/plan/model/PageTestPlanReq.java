package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PageTestPlanReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = -5388526950417034554L;

    @ZModelProperty(description = "计划名称", required = false, sample = "V1.0.0测试计划")
    private String name;

    @ZModelProperty(description = "产品code", required = false, sample = "399")
    private String productCode;

}
