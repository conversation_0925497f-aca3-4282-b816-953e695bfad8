package com.zto.devops.qc.client.service.report.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryReviewReportReq extends QueryReportDetailReq implements Serializable {

    @ZModelProperty(description = "产品code", required = false, sample = "399")
    private String productCode;

    @ZModelProperty(description = "报告人id", required = false, sample = "5305175")
    private Long reportUserId;
}
