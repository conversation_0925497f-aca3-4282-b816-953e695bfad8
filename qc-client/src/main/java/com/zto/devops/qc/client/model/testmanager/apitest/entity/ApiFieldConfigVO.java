package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import java.io.Serializable;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiConfigTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ApiFieldConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZModelProperty(description = "接口字段", required = true, sample = "req.code")
    private String key;

    @ZModelProperty(description = "接口配置列表", required = true)
    private List<ApiConfigTypeEnum> apiConfigTypeList;
}
