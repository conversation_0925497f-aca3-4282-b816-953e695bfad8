package com.zto.devops.qc.client.model.testmanager.coverage.query;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022/10/25 20:19
 */
@Data
public class ReportCoveredDto implements Serializable {

    private static final long serialVersionUID = 1L;


    public ReportCoveredDto() {
    }

    public ReportCoveredDto(String name, Integer missed, Integer covered) {
        super();
        this.name = name;
        this.missed = missed;
        this.covered = covered;
    }

    private String name;

    private Integer missed;

    private Integer covered;

}
