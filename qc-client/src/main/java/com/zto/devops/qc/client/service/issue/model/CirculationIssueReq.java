package com.zto.devops.qc.client.service.issue.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CirculationIssueReq implements Serializable{

    @ZModelProperty(description = "编号", required = true, sample = "ISS230303008063")
    private String code;
    @ZModelProperty(description = "开发人员", required = false, sample = "5305175")
    private Long developId;
    @ZModelProperty(description = "开发人员", required = false, sample = "开发人")
    private String developName;
    @ZModelProperty(description = "测试人员", required = false, sample = "5305175")
    private Long testId;
    @ZModelProperty(description = "测试人员", required = false, sample = "测试人")
    private String testName;
    @ZModelProperty(description = "备注 (JSON)", required = false, sample = "备注")
    private String content;


}
