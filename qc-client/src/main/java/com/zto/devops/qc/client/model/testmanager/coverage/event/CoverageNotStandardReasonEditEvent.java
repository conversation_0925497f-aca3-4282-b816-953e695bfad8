package com.zto.devops.qc.client.model.testmanager.coverage.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CoverageNotStandardReasonEditEvent extends BaseEvent implements ActionEvent {

    private String versionCode;

    private String appId;

    private String productCode;

    private List<String> reasonList;

    private String customReason;

    @Override
    public String action() {
        return "修改覆盖率不达标原因";
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }
}
