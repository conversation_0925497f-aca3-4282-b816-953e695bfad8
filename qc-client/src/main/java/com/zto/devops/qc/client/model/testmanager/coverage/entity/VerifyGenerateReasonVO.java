package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VerifyGenerateReasonVO implements Serializable {
    private static final long serialVersionUID = -4678504200205591737L;

    /**
     * 应用id集合
     */
    private List<String> appIdList;

    /**
     * 原因
     */
    private String reason;

    public static VerifyGenerateReasonVO buildSelf(String reason, List<String> appIdList) {
        return VerifyGenerateReasonVO.builder()
                .reason(reason)
                .appIdList(appIdList)
                .build();
    }
}
