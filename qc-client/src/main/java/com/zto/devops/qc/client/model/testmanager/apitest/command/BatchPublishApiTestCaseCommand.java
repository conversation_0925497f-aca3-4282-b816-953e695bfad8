package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BatchPublishApiTestCaseCommand extends BaseCommand {
    private static final long serialVersionUID = 282182734646879685L;

    private String productCode;

    private List<String> caseCodeList;

    private List<String> apiCodeList;

    private Boolean publishCaseFlag;

    private Integer batchSize;

    public Integer getBatchSize() {
        return publishCaseFlag ? caseCodeList.size() : apiCodeList.size();
    }

    public BatchPublishApiTestCaseCommand(String aggregateId) {
        super(aggregateId);
    }
}
