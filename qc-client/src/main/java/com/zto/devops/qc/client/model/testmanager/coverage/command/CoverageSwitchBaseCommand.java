package com.zto.devops.qc.client.model.testmanager.coverage.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2022/10/17 13:17
 */
@Setter
@Getter
public class CoverageSwitchBaseCommand extends BaseCommand {

    @GatewayModelProperty(description = "版本编码", required = true)
    private String versionCode;
    @GatewayModelProperty(description = "版本名称", required = true)
    private String versionName;
    @GatewayModelProperty(description = "应用名称", required = true)
    private String appId;

    public CoverageSwitchBaseCommand(String aggregateId) {
        super(aggregateId);
    }

}
