package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZsmpModel(description = "校验当前阶段是否完成")
public class VerifyTestPassConditionResp implements Serializable {
    private static final long serialVersionUID = -6104189106908834640L;

    @ZsmpModelProperty(description = "测试完成标志（true：测试阶段已完成；false：测试阶段未完成）", sample = "false")
    private Boolean testCompletedFlag;

    @ZsmpModelProperty(description = "发送报告标志（true：已发送；false：未发送）", sample = "false")
    private Boolean sendReportFlag;

    @ZsmpModelProperty(description = "发送安全测试计划标志（true：已发送；false：未发送）", sample = "false")
    private Boolean sendPlanFlag;

    @ZsmpModelProperty(description = "用例测试完成标志（true：完成；false：未完成）", sample = "false")
    private Boolean caseCompleteFlag;

    @ZsmpModelProperty(description = "计划code", sample = "TP240122008040")
    private String planCode;

    @ZsmpModelProperty(description = "安全测试计划code", sample = "TP240122008040")
    private String safetyPlanCode;

    @ZsmpModelProperty(description = "报告类型", sample = "TEST_ACCESS")
    private ReportType reportType;

    public static VerifyTestPassConditionResp buildPassed() {
        return VerifyTestPassConditionResp.builder()
                .testCompletedFlag(Boolean.TRUE)
                .sendReportFlag(Boolean.TRUE)
                .sendPlanFlag(Boolean.TRUE)
                .caseCompleteFlag(Boolean.TRUE)
                .planCode("")
                .safetyPlanCode("")
                .reportType(null)
                .build();
    }

    public static VerifyTestPassConditionResp buildSelf(Boolean testCompletedFlag,
                                                        Boolean sendReportFlag,
                                                        Boolean sendPlanFlag,
                                                        Boolean caseCompleteFlag,
                                                        String planCode,
                                                        String safetyPlanCode,
                                                        ReportType reportType) {
        return VerifyTestPassConditionResp.builder()
                .testCompletedFlag(testCompletedFlag)
                .sendReportFlag(sendReportFlag)
                .sendPlanFlag(sendPlanFlag)
                .caseCompleteFlag(caseCompleteFlag)
                .planCode(planCode)
                .safetyPlanCode(safetyPlanCode)
                .reportType(reportType)
                .build();
    }
}