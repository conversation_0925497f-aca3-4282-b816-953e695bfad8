package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SceneModuleCopyReq implements Serializable {

    private static final long serialVersionUID = -2304175773851065540L;

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "产品名称", required = true)
    private String productName;

    @ZsmpModelProperty(description = "分组code", required = true)
    private String moduleCode;

    @ZsmpModelProperty(description = "类型", required = true)
    private UseCaseFactoryTypeEnum sceneType;

}
