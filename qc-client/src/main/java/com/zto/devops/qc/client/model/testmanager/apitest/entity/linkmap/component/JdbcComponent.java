package com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.component;

import java.io.Serializable;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.linkmap.ComponentBaseInfo;
import lombok.Data;

@Data
public class JdbcComponent extends ComponentBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    String connectionConfiguration;//dataSource
    String queryType;
    String sql;//query
    String queryArgumentsTypes;
    String queryArguments;
    Integer queryTimeout;
    String resultSetHandler;
    Integer resultSetMaxRows;
    String resultVariable;
    String variableNames;
}
