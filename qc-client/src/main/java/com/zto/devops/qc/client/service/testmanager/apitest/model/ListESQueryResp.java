package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ListESQueryVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZsmpModel(description = "ES列表查询响应模型")
public class ListESQueryResp implements Serializable {
    private static final long serialVersionUID = 6760032070757764927L;

    @ZsmpModelProperty(description = "集群id")
    private Integer clusterId;

    @ZsmpModelProperty(description = "默认集群名称")
    private String clusterName;

    @ZsmpModelProperty(description = "用户自定义集群名称")
    private String customName;

    public static List<ListESQueryResp> buildList(List<ListESQueryVO> voList) {
        if (CollectionUtil.isEmpty(voList)) {
            return new ArrayList<>();
        }
        List<ListESQueryResp> resultList = new ArrayList<>(voList.size());
        voList.forEach(vo -> resultList.add(ListESQueryResp.buildSelf(vo.getClusterId(), vo.getName(), vo.getRealName())));
        return resultList;
    }

    private static ListESQueryResp buildSelf(Integer clusterId, String name, String realName) {
        return ListESQueryResp.builder().clusterId(clusterId).clusterName(realName).customName(name).build();
    }
}
