package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryPermitResultResp implements Serializable {
    private static final long serialVersionUID = -4425276116263214416L;

    @ZModelProperty(description = "准出报告测试结果")
    private TmTestResultEnum testResult;

    @ZModelProperty(description = "是否发过准出报告")
    private Boolean sentFlag;

}
