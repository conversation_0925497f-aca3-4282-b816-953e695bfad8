package com.zto.devops.qc.client.model.testmanager.coverage.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class EditCoverageReasonCommand extends BaseCommand {

    @GatewayModelProperty(description = "代码覆盖率不达标原因")
    private List<CoverageReasonVO> coverageReasonVOS;

    public EditCoverageReasonCommand(String aggregateId) {
        super(aggregateId);
    }
}
