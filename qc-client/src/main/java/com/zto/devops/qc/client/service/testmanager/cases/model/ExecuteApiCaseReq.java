package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "批量执行用例请求模型")
public class ExecuteApiCaseReq implements Serializable {

    @ZModelProperty(description = "所属产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "触发方式", required = true, sample = "SINGLE_API_MANUAL")
    private AutomaticTaskTrigModeEnum trigMode;

    @ZModelProperty(description = "执行环境", required = true, sample = "base")
    private String env;

    @ZModelProperty(description = "环境名称", required = true)
    private String envName;

    @ZModelProperty(description = "用例code列表", required = true)
    private List<String> caseCodes;

    @ZModelProperty(description = "是否部署过", required = true)
    private Boolean isDeploy;

    @ZModelProperty(description = "是否动态多环境", required = true)
    private Boolean autoEnv;
}
