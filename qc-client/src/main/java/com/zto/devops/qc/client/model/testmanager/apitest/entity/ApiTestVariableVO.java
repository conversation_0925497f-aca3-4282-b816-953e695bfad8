package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.client.entity.BaseModel;
import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableUsageTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ApiTestVariableVO extends BaseModel implements Serializable {

    private Long id;

    private String productCode;

    private String productName;

    private String linkCode;

    private String variableCode;

    private String variableName;

    private String variableKey;

    private String variableValue;

    private VariableTypeEnum type;

    private Integer sceneType;

    /**
     * 使用类型(OUTPUT(2),INPUT(1))
     */
    private VariableUsageTypeEnum usageType;

    /**
     * 是否必填
     */
    private Boolean requiredStatus;

    private Boolean variableStatus;

    private Boolean enable;

    private String variableDesc;

    private SubVariableTypeEnum subVariableType;

    private Integer loginValidTime;

    public static ApiTestVariableVO buildForSearchCookie(String variableValue) {
        ApiTestVariableVO vo = new ApiTestVariableVO();
        vo.setVariableValue(variableValue);
        return vo;
    }
}
