package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class IssueTagResp implements Serializable {

    /**
     * 编码
     */
    private String code;

    /**
     * 所属领域
     */
    private String domain;
//    private DomainEnum domain;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 标签类型
     */
    private String type;
//    private TagTypeEnum type;

    /**
     * 标签别名
     */
    private String tagAlias;

    /**
     * 标签编码
     */
    private String tagCode;

    /**
     * 标签名称
     */
    private String tagName;
    /**
     * 是否删除 1 未删除, 0 已删除
     */
    private Boolean enable;

    /**
     * 创建人编码
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新人编码
     */
    private Long modifierId;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

    public void preCreate(BaseEvent baseEvent) {
        this.setGmtCreate(new Date());
        this.setGmtModified(new Date());
        User user = baseEvent.getTransactor();
        if (null != user) {
            this.setCreatorId(user.getUserId());
            this.setCreator(user.getUserName());
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
        }
        Date occurred = baseEvent.getOccurred();
        if(occurred != null){
            this.setGmtCreate(occurred);
            this.setGmtModified(occurred);
        }

    }

    public void preUpdate(BaseEvent baseEvent) {
        this.setGmtModified(new Date());
        User user = baseEvent.getTransactor();
        if (null != user) {
            this.setModifierId(user.getUserId());
            this.setModifier(user.getUserName());
        }
        Date occurred = baseEvent.getOccurred();
        if(occurred != null){
            this.setGmtModified(occurred);
        }

    }

}
