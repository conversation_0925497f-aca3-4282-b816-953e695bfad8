package com.zto.devops.qc.client.service.testmanager.cases;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.*;
import com.zto.devops.qc.client.model.testmanager.cases.query.ListRelationTestCaseQuery;
import com.zto.devops.qc.client.model.testmanager.cases.query.MoveModuleReq;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;

import java.util.List;

public interface ITestcaseService {

    Result<ExecuteCaseResp> executeCaseDetail(ExecuteCaseReq req);

    PageResult<TestcaseExecuteRecordVO> getExecuteRecordList(ListTestcaseExecuteRecordReq req);

    Result<Void> dragModule(DragModuleReq req);

    /**
     * 用例批量加入测试计划
     *
     * @param req {@link AddBatchCaseInBatchTestPlanReq}
     * @return void
     */
    Result<Void> addBatchCaseInBatchTestPlan(AddBatchCaseInBatchTestPlanReq req);

    /**
     * 添加用例关联附件
     *
     * @param req {@link AddTestcaseAttachmentReq}
     * @return void
     */
    Result<Void> addAttachment(AddTestcaseAttachmentReq req);

    /**
     * 添加标签
     *
     * @param req {@link AddTestcaseTagReq}
     * @return void
     */
    Result<Void> addTag(AddTestcaseTagReq req);

    /**
     * 用例关联业务
     *
     * @param req {@link AssociateTestcaseDomainReq}
     * @return void
     */
    Result<Void> associateTestcaseDomain(AssociateTestcaseDomainReq req);

    /**
     * 批量添加标签
     *
     * @param req
     * @return
     */
    Result<Void> batchAddTag(BatchAddTestcaseTagReq req);

    /**
     * 用例管理批量变更手工用例状态
     *
     * @param req {@link BatchChangeTestcaseStatusReq}
     * @return void
     */
    Result<Void> batchChangeTestcaseStatus(BatchChangeTestcaseStatusReq req);

    /**
     * 批量修改版本
     *
     * @param req {@link BatchChangeVersionReq}
     * @return
     */
    Result<Void> batchChangeVersion(BatchChangeVersionReq req);

    /**
     * 用例管理批量复制用例
     *
     * @param req {@link BatchCopyTestcaseReq}
     * @return void
     */
    Result<Void> batchCopyTestcase(BatchCopyTestcaseReq req);


    /**
     * 用例管理批量修改责任人
     *
     * @param req {@link BatchModifyCaseDutyUserReq}
     * @return void
     */
    Result<Void> batchModifyCaseDutyUser(BatchModifyCaseDutyUserReq req);

    /**
     * 用例管理批量修改用例等级
     *
     * @param req {@link BatchModifyCaseGradeReq}
     * @return void
     */
    Result<Void> batchModifyCaseGrade(BatchModifyCaseGradeReq req);

    /**
     * 用例管理批量移动手工用例
     *
     * @param req {@link BatchMoveTestcaseModelReq}
     * @return void
     */
    Result<Void> batchMoveTestcaseModel(BatchMoveTestcaseModelReq req);

    /**
     * 用例管理批量删除用例
     *
     * @param req {@link BatchRemoveTestcaseReq}
     * @return void
     */
    Result<Void> batchRemoveTestcase(BatchRemoveTestcaseReq req);

    /**
     * 批量设置核心用例
     *
     * @param req {@link BatchSetCoreReq}
     * @return
     */
    Result<Void> batchSetCore(BatchSetCoreReq req);


    /**
     * 变更测试计划用例执行人
     *
     * @param req {@link ChangePlanCaseExecutorReq}
     * @return void
     */
    Result<Void> changePlanCaseExecutor(ChangePlanCaseExecutorReq req);

    /**
     * 变更用例责任人
     *
     * @param req {@link ChangeTestcaseDutyUserReq}
     * @return void
     */
    Result<Void> changeTestcaseDutyUser(ChangeTestcaseDutyUserReq req);

    /**
     * 变更手工用例状态
     *
     * @param req {@link ChangeTestcaseStatusReq}
     * @return void
     */
    Result<Void> changeStatus(ChangeTestcaseStatusReq req);

    /**
     * 手工用例-修改版本
     *
     * @param req {@link ChangeVersionReq}
     * @return
     */
    Result<Void> changeVersion(ChangeVersionReq req);

    /**
     * 校验用例创建人和责任人
     *
     * @param testcaseCodeList 用例code
     * @return {@link CheckCreatorOrDutyUserResp}
     */
    Result<CheckCreatorOrDutyUserResp> checkCreatorOrDutyUser(List<String> testcaseCodeList);

    /**
     * 校验用例状态
     *
     * @param testcaseCodeList 用例code
     * @return {@link CheckTestcaseStatusResp}
     */
    Result<CheckTestcaseStatusResp> checkTestcaseStatus(List<String> testcaseCodeList);


    /**
     * 导出用例列表
     *
     * @param req {@link ListTestcaseReq}
     * @return void
     */
    Result<Void> addExportTestcase(ListTestcaseReq req);

    /**
     * 获取测试计划用例详情
     *
     * @param req {@link PlanCaseReq}
     * @return {@link PlanCaseResp}
     */
    Result<PlanCaseResp> planCaseDetail(PlanCaseReq req);

    /**
     * 获取用例关联缺陷
     *
     * @param code 用例唯一标识
     * @return {@link TestcaseIssueResp}
     */
    Result<List<TestcaseIssueResp>> getRelationIssueList(String code);

    /**
     * 获取用例关联需求
     *
     * @param codeList 用例集合
     * @return {@link TestcaseIssueResp}
     */
    Result<List<TestcaseRequirementResp>> getRelationRequirementList(List<String> codeList, DomainEnum domain);

    /**
     * 用例管理-查询所有分组和用例
     *
     * @param req
     * @return
     */
    Result<List<ListTestcaseResp>> getTestcaseAll(ListTestcaseReq req);

    /**
     * 获取用例详情
     *
     * @param code 用例唯一标识
     * @return {@link TestcaseResp}
     */
    Result<TestcaseResp> detail(String code);

    /**
     * 查询标签下的用例数量
     *
     * @param code
     * @param productCode
     * @return
     */
    Result<Long> tagCasesNo(String code, String productCode);

    Result<String> xmindAdd(XmindCaseAddReq req);

    PageResult<ListXmindDetailResp> getXmindDetail(ListXmindDetailReq req);

    Result<List<ListXmindDetailResp>>queryAllXmindCase(QueryAllXmindCaseReq req);

    /**
     * 移动手工用例
     *
     * @param req {@link MoveTestcaseModelReq}
     * @return void
     */
    Result<Void> moveTestcase(MoveTestcaseModelReq req);

    /**
     * 查询分组下所有用例
     *
     * @param req {@link ListTestcaseCodeReq}
     * @return 用例code
     */
    Result<List<String>> listTestcaseCode(ListTestcaseCodeReq req);

    /**
     * 查询用例模块树列表
     *
     * @param req {@link ListTestcaseModuleReq}
     * @return {@link ListTestcaseModuleResp}
     */
    Result<List<ListTestcaseModuleResp>> listModule(ListTestcaseModuleReq req);

    PageResult<ListTestcaseResp> pageTestcase(ListTestcaseReq req);

    /**
     * 编辑xmind节点
     *
     * @param req
     * @return
     */
    Result<Void> xmindEdit(XmindCaseEditReq req);

    Result<Void> deleteTestcase(String code, String attribute);

    Result<Void> batchXmindDelete(BatchXmindDeleteReq req);

    Result<List<String>> xmindFilter(ListXmindDetailReq req);

    Result<Void> addTestcase(AddTestcaseReq req);

    Result<Void> editTestcase(AddTestcaseReq req);

    /**
     * 移除标签
     *
     * @param code 标签唯一标识
     * @return void
     */
    Result<Void> removeTag(String code);

    /**
     * 简单查询用例的接口
     */
    Result<PageSimpleTestCaseVO> simpleListCase(SimpleListCaseReq req);

    /**
     * 获取执行明细列表
     *
     * @param req 自动化任务code
     * @return {@link ListExecuteCaseResp}
     */
    Result<List<ListExecuteCaseResp>> listExecuteCase(ListExecuteCaseReq req);

    Result<ImportResultResp> importTestcase(ImportTestcaseReq req);

    /**
     * 校验用例是否关联计划
     *
     * @param req {@link QueryCaseRelatedPlanReq}
     * @return {@link QueryCaseRelatedPlanReq}
     */
    Result<QueryCaseRelatedPlanVO> queryCaseRelatedPlan(QueryCaseRelatedPlanReq req);

    /**
     * 解除用例关联业务
     *
     * @param req {@link ReleaseTestcaseRelationReq}
     * @return void
     */
    Result<Void> releaseTestcaseRelation(ReleaseTestcaseRelationReq req);

    /**
     * 移除用例关联附件
     *
     * @param req {@link RemoveTestcaseAttachmentReq}
     * @return void
     */
    Result<Void> removeAttachment(RemoveTestcaseAttachmentReq req);

    /**
     * 统计用例状态
     *
     * @param req {@link StatisticCaseStatusReq}
     * @return {@link TestCaseStatusNumVO}
     */
    Result<TestCaseStatusNumVO> statisticCaseStatus(StatisticCaseStatusReq req);

    /********************* 给其他域调用都接口  **********************************/

    List<TestcaseByBusinessCodeVO> listRelationTestCaseQuery(ListRelationTestCaseQuery query); // 项目域使用

    Result<Void> editTestcaseTitleOrPriority(EditTestcaseTitleReq req);

    Result<GroupsInformationResp> groupsInformation(String code, String productCode);

    Result<Void> moveModule(MoveModuleReq req);

    Result<StatisticVersionCaseNumVO> statisticVersionCase(StatisticVersionCaseReq req);
}
