package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.model.testmanager.report.entity.EmailMemberVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class BasicInfoResp implements Serializable {

    @ZModelProperty(description = "编号", required = false)
    private String reportCode;

    @ZModelProperty(description = "报告名称", required = true)
    private String reportName;

    @ZModelProperty(description = "计划编号", required = true)
    private String planCode;

    @ZModelProperty(description = "计划名称", required = false)
    private String planName;

    @ZModelProperty(description = "版本code", required = true)
    private String versionCode;

    @ZModelProperty(description = "版本名称", required = true)
    private String versionName;

    @ZModelProperty(description = "部门id", required = false)
    private Long deptId;

    @ZModelProperty(description = "部门名称", required = false)
    private String deptName;

    @ZModelProperty(description = "所属产品code", required = false)
    private String productCode;

    @ZModelProperty(description = "所属产品名称", required = false)
    private String productName;

    @ZModelProperty(description = "产品负责人id", required = false)
    private Long productOwnerId;

    @ZModelProperty(description = "产品负责人姓名", required = false)
    private String productOwnerName;

    @ZModelProperty(description = "报告人Id", required = false)
    private Long reportUserId;

    @ZModelProperty(description = "报告人姓名", required = false)
    private String reportUserName;

    @ZModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

    @ZModelProperty(description = "邮件发送时间", required = false)
    private Date sendTime;

    @ZModelProperty(description = "收件人", sample = "1", required = true)
    private List<EmailMemberVO> receiveUsers = new ArrayList<>();

    @ZModelProperty(description = "抄送人", sample = "email", required = true)
    private List<EmailMemberVO> ccUsers = new ArrayList<>();

    @ZModelProperty(description = "总体测试结果", required = true)
    private TmTestResultEnum testResult;

    @ZModelProperty(description = "是否需要用户体验测试", required = false)
    private Boolean needUiTest;

    @ZModelProperty(description = "用户体验测试结果", required = false)
    private UiTestResultEnum uiTestResult;

    @ZModelProperty(description = "用户体验测试结果描述", required = false)
    private String uiTestResultDesc;

    public String getUiTestResultDesc() {
        if (this.uiTestResult == null){
            return "";
        }
        return this.uiTestResult.getValue();
    }
}
