package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.report.TestReportTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RelatedTestReq implements Serializable {
    private static final long serialVersionUID = -3641279094815655816L;

    @ZModelProperty(description = "计划编号", required = false, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "报告编号", required = false, sample = "TR240416001001")
    private String reportCode;

    @ZModelProperty(description = "报告类型;", required = false, sample = "TEST_ACCESS")
    private TestReportTypeEnum reportType;

    @ZModelProperty(description = "计划类型;", required = false, sample = "TEST_PLAN")
    private TestPlanNewTypeEnum planType;

}
