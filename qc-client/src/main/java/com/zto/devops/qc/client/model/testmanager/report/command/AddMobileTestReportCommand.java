package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class AddMobileTestReportCommand extends BaseReportInfoCommand {

    @GatewayModelProperty(description = "实际测试开始时间", required = false)
    private Date actualTestStart;

    @GatewayModelProperty(description = "实际测试开始时间--上下午", required = false)
    private String actualTestStartDay;

    @GatewayModelProperty(description = "实际测试结束时间", required = false)
    private Date actualTestEnd;

    @GatewayModelProperty(description = "实际测试结束时间-- 上下午", required = false)
    private String actualTestEndDay;

    @GatewayModelProperty(description = "更新测试结果时间", required = false)
    private Date updateTestResultDate;

    @GatewayModelProperty(description = "模块测试结果", required = false)
    private List<TmModuleTestVO> moduleTestVOS;
//
//    @GatewayModelProperty(description = "用例执行结果", required = false)
//    private List<StatisticCaseResultVO> caseResultVOS;

    @GatewayModelProperty(description = "总结、分析、描述", required = false)
    private String summary;

    public AddMobileTestReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
