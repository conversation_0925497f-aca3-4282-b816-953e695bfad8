package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.simple.Button;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.SceneModuleQueryVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SceneModuleQueryResp implements Serializable {

    private static final long serialVersionUID = -1778120120332492050L;

    @GatewayModelProperty(description = "场景code/分组code")
    private Long id;

    @GatewayModelProperty(description = "场景code/分组code")
    private String code;

    @GatewayModelProperty(description = "场景name/分组name")
    private String name;

    @GatewayModelProperty(description = "描述")
    private String desc;

    @GatewayModelProperty(description = "场景数量")
    private Integer sceneCount;

    @GatewayModelProperty(description = "场景状态")
    private Integer sceneEnable;

    @GatewayModelProperty(description = "场景状态标识")
    private SceneInfoEnableEnum sceneEnableDesc;

    @GatewayModelProperty(description = "类型：场景/分组")
    private SceneIndexTypeEnum type;

    @GatewayModelProperty(description = "父节点code")
    private String parentCode;

    @GatewayModelProperty(description = "下级节点")
    private List<SceneModuleQueryVO> children;

    @GatewayModelProperty(description = "是否叶子节点")
    private Boolean isLeaf;

    @GatewayModelProperty(description = "分享状态")
    private Boolean shareStatus;

    private List<Button> permissionList;
    public SceneInfoEnableEnum getSceneEnableDesc() {
        if(null != this.sceneEnable) {
            return SceneInfoEnableEnum.codeOf(this.sceneEnable);
        }
        return null;
    }

}
