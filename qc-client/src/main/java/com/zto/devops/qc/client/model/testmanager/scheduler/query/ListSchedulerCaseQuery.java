package com.zto.devops.qc.client.model.testmanager.scheduler.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticNodeTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcasePriorityEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ListSchedulerCaseQuery extends BaseQuery {

    @GatewayModelProperty(description = "产品Code")
    private String productCode;

    @GatewayModelProperty(description = "定时任务Code")
    private String schedulerCode;

    @GatewayModelProperty(description = "父节点code")
    private String parentCode;

    @GatewayModelProperty(description = "用例code集合")
    private List<String> caseCodeList;

    @GatewayModelProperty(description = "搜索", required = false)
    private String search;

    @GatewayModelProperty(description = "执行结果，INITIAL-未执行|PASSED-通过|FAILED-失败|BLOCK-阻塞|SKIP-跳过|RETEST-重测", required = false)
    private List<TestPlanCaseStatusEnum> statusList;

    @GatewayModelProperty(description = "用例等级，HIGH-高|MIDDLE-中|LOW-低", required = false)
    private List<TestcasePriorityEnum> priorityList;

    @GatewayModelProperty(description = "自动化节点类型 ", required = false)
    private List<AutomaticNodeTypeEnum> nodeTypeList;

    @GatewayModelProperty(description = "用例类型", required = false)
    private TestcaseTypeEnum type;

    @GatewayModelProperty(description = "框架类型", required = false)
    private List<AutomaticRecordTypeEnum> automaticTypeList;

    private Boolean filterModule;

    private List<String> filterModuleCodeList;

}
