package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class AddAndSendMobileTestReportCommand extends BaseReportInfoCommand {

    @GatewayModelProperty(description = "实际测试开始时间", required = false)
    private Date actualTestStart;

    @GatewayModelProperty(description = "实际测试开始时间--上下午", required = false)
    private String actualTestStartDay;

    @GatewayModelProperty(description = "实际测试结束时间", required = false)
    private Date actualTestEnd;

    @GatewayModelProperty(description = "实际测试结束时间-- 上下午", required = false)
    private String actualTestEndDay;

    @GatewayModelProperty(description = "更新测试结果时间", required = false)
    private Date updateTestResultDate;

    @GatewayModelProperty(description = "模块测试结果", required = false)
    private List<TmModuleTestVO> moduleTestVOS;

    @GatewayModelProperty(description = "发送人Id", required = false)
    private Long sendUserId;

    @GatewayModelProperty(description = "发送人姓名", required = false)
    private String sendUserName;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    public AddAndSendMobileTestReportCommand(String aggregateId) {
        super(aggregateId);
    }
}
