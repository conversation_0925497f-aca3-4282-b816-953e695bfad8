package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.RequestMethodEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestFieldVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "查询接口列表响应模型")
public class PageApiInfoResp implements Serializable {

    @ZsmpModelProperty(description = "接口code")
    private String apiCode;

    @ZsmpModelProperty(description = "接口名称")
    private String apiName;

    @ZsmpModelProperty(description = "请求方式")
    private RequestMethodEnum reqMethod;

    @ZsmpModelProperty(description = "请求地址")
    private String requestUrl;

    @ZsmpModelProperty(description = "HTTP协议")
    private String protocol;

    @ZsmpModelProperty(description = "域名")
    private String host;

    @ZsmpModelProperty(description = "请求路径")
    private String path;

    @ZsmpModelProperty(description = "请求头")
    private List<ApiTestFieldVO> headers;

    @ZsmpModelProperty(description = "form/args请求参数")
    private List<ApiTestFieldVO> params;

    @ZsmpModelProperty(description = "json请求参数")
    private String body;

    @ZsmpModelProperty(description = "展示请求路径")
    private String displayPath;

    @ZsmpModelProperty(description = "请求端口")
    private String portNumber;

    @ZsmpModelProperty(description = "path请求参数")
    private List<ApiTestFieldVO> pathParams;

    @ZsmpModelProperty(description = "是否来源网关")
    private Boolean gatewaySource;

    @ZsmpModelProperty(description = "文档名称")
    private String docName;

    @ZsmpModelProperty(description = "产品code")
    private String docProductCode;

    @ZsmpModelProperty(description = "文档id")
    private Long docId;

    @ZsmpModelProperty(description = "应用id")
    private String appId;

    @ZsmpModelProperty(description = "一站式产品code")
    private String productCode;

    @ZsmpModelProperty(description = "接口类型")
    private ApiTypeEnum apiType;

    @ZsmpModelProperty(description = "dubbo接口名称")
    private String interfaceName;

    @ZsmpModelProperty(description = "dubbo方法名称")
    private String methodName;

    @ZsmpModelProperty(description = "文档版本号")
    private String docVersion;

    @ZsmpModelProperty(description = "dubbo方法组")
    private String group;

    @ZsmpModelProperty(description = "dubbo方法版本")
    private String version;
}
