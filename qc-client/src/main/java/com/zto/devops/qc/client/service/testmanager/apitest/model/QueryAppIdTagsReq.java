package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ZsmpModel(description = "查询应用标签req")
public class QueryAppIdTagsReq implements Serializable {

    @ZsmpModelProperty(description = "ip地址", required = true)
    private String location;

    @ZsmpModelProperty(description = "发生时间戳")
    private Long occurrenceAt;
}
