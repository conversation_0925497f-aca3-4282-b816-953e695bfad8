package com.zto.devops.qc.client.service.attachment.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@GatewayModel(description = "附件模型")
public class RemoveAttachmentReq implements Serializable {

    @ZModelProperty(description = "业务编码: 业务实例 如 缺陷编码等", required = true, sample = "SNF987293442717515776")
    private String businessCode;

    @ZModelProperty(description = "附件编码", required = true, sample = "111")
    private String code;

}