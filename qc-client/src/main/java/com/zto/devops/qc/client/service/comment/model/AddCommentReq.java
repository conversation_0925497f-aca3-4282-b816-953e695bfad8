package com.zto.devops.qc.client.service.comment.model;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddCommentReq implements Serializable {

    @ZModelProperty(description = "domain 对应的实例编码", required = true, sample = "SNF987293442717515776")
    private String businessCode;

    @ZModelProperty(description = "评论内容", required = true, sample = "评论内容")
    private String content;

    @ZModelProperty(description = "被回复评论的评论code", required = false, sample = "SNF987293442717515776")
    private String repliedCode;

    @ZModelProperty(description = "被回复评论的最上层code", required = false, sample = "SNF987293442717515776")
    private String topRepliedCode;

    @ZModelProperty(description = "被回复人ID", required = false, sample = "5984549")
    private Long repliedUserId;

    @ZModelProperty(description = "被回复人名称", required = false, sample = "被回复人")
    private String repliedUserName;

}
