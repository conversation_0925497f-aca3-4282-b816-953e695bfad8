package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class AutomaticSchedulerSimpleReq implements Serializable {
    private static final long serialVersionUID = 2266443830530497328L;

    @ZModelProperty(description = "任务编码", required = true, sample = "SNF975268955453128704")
    @Auth(type = AuthTypeConstant.SCHEDULER_CODE)
    private String schedulerCode;
}
