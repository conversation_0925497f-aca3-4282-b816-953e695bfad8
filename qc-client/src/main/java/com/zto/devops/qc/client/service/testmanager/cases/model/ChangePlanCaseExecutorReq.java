package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@ZsmpModel(description = "变更测试计划用例执行人")
@Getter
@Setter
public class ChangePlanCaseExecutorReq extends PlanCaseReq implements Serializable {

    @ZModelProperty(description = "执行人id", required = false, sample = "5878415")
    private Long executorId;

    @ZModelProperty(description = "执行人", required = false, sample = "执行人")
    private String executor;

    @ZModelProperty(description = "日志记录code", required = true, sample = "SNF974191934769725441")
    private String operateCaseCode;
}
