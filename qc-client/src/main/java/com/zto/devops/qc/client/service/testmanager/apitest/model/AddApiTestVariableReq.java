package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.SubVariableTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AddApiTestVariableReq implements Serializable {

    @GatewayModelProperty(description = "所属产品code", required = true)
    private String productCode;

    @GatewayModelProperty(description = "变量名称", required = true)
    private String variableName;

    @GatewayModelProperty(description = "变量键", required = true)
    private String variableKey;

    @GatewayModelProperty(description = "变量值", required = true)
    private String variableValue;

    @GatewayModelProperty(description = "类型", required = true)
    private VariableTypeEnum type;

    @GatewayModelProperty(description = "链路code", required = false)
    private String linkCode;

    @GatewayModelProperty(description = "变量子类型")
    private Integer subVariableType;

    @GatewayModelProperty(description = "登录有效时间", required = false)
    private Integer loginValidTime;
}
