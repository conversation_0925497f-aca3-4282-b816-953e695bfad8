package com.zto.devops.qc.client.model.testmanager.scheduler.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class RemoveSchedulerCasesCommand extends BaseCommand {

    @ZsmpModelProperty(description = "任务code", required = true)
    private String schedulerCode;

    @ZsmpModelProperty(description = "用例code集合", required = true)
    private List<String> caseCodeList;

    public RemoveSchedulerCasesCommand(String aggregateId) {
        super(aggregateId);
    }
}
