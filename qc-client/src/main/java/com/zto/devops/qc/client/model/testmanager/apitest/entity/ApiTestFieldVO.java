package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestFieldTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ApiTestFieldVO implements Serializable {

    private String key;

    private String value;

    private String description;

    private Boolean required;

    private String sample;

    private ApiTestFieldTypeEnum type;

    private String typeValue;

    private List<String> enumsValue;

    private List<ApiTestFieldVO> fields;

    private Boolean customized;

    private List<String> scope;

    private String realType;

    private Boolean urlEncode;

    private String contentType;

    private Boolean includeEquals;

    private Integer minLen;

    private Integer maxLen;
}
