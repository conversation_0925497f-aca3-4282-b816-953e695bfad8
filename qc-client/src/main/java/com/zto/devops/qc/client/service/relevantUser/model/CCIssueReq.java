package com.zto.devops.qc.client.service.relevantUser.model;

import com.zto.devops.framework.client.simple.User;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

@Data
@GatewayModel(description = "抄送缺陷实体")
public class CCIssueReq implements Serializable {
    @ZModelProperty(description = "缺陷编号", required = true, sample = "ISS230303008063")
    @NotBlank(message = "缺陷编号不能为空")
    private String issueCode;

    @ZModelProperty(description = "抄送用户", required = true, sample = "[]")
    @NotBlank(message = "抄送用户不能为空")
    private List<User> ccUserList;
}
