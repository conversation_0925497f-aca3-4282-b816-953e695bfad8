package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：AddOnlineSmokeReportReq
 * 类 描 述：TODO
 * 创建时间：2021/11/10 5:57 下午
 * 创 建 人：bulecat
 */
@Getter
@Setter
public class AddAndSendSimpleTestReportCommand extends SimpleTestReportCommand {
    public AddAndSendSimpleTestReportCommand(String aggregateId) {
        super(aggregateId);
    }

    @GatewayModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    @GatewayModelProperty(description = "覆盖率结果")
    private CodeCoverResult coverageResult;

    @GatewayModelProperty(description = "代码覆盖率不达标原因")
    private List<CoverageReasonVO> coverageReasonVOS;
}
