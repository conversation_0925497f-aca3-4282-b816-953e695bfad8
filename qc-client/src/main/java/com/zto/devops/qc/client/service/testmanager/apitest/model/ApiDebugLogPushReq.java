package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ApiDebugLogPushReq implements Serializable {

    private static final long serialVersionUID = 7091529391115118848L;

    @GatewayModelProperty(description = "任务id")
    private String taskId;

    @GatewayModelProperty(description = "日志内容", required = false)
    private String content;
}
