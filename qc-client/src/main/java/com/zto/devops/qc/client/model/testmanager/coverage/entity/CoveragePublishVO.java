package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.entity.BaseModel;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.AppTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.DeploymentIdentityEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2022/9/16 17:16
 */
@Data
public class CoveragePublishVO extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 应用类型，JAVA-java应用，WEB-前端应用，GOLANG-go应用
     */
    private AppTypeEnum appType;

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * appId
     */
    private String appId;

    /**
     * git提交id
     */
    private String commitId;

    /**
     * 分支名称
     */
    private String branchName;

    /**
     * git地址
     */
    private String gitUrl;

    /**
     * 部署标识，VIRTUAL-虚拟机,DOCKER-容器
     */
    private DeploymentIdentityEnum deploymentIdentityEnum;

    /**
     * ip或实例名
     */
    private String serviceName;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 下载包名
     */
    private String packageName;

    /**
     * 空间名称
     */
    private String envName;

    /**
     * 合并文件名
     */
    private String outputFileName;

    /**
     * 发布泳道
     */
    private String flowLaneType;
}
