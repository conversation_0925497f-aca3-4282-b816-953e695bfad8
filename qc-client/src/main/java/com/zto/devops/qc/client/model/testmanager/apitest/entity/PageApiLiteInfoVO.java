
package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@GatewayModel(description = "分页查询api信息VO（精简版）")
@Data
public class PageApiLiteInfoVO implements Serializable {
    private static final long serialVersionUID = 3581464620675792144L;

    @GatewayModelProperty(description = "总数")
    private Long total;

    @GatewayModelProperty(description = "用例数据")
    private List<ApiLiteInfoVO> list;

    public static PageApiLiteInfoVO buildSelf(List<ApiLiteInfoVO> doList, Long total) {
        PageApiLiteInfoVO result = new PageApiLiteInfoVO();
        result.setList(CollectionUtil.isEmpty(doList) ? new ArrayList<>() : doList);
        result.setTotal(CollectionUtil.isEmpty(doList) ? 0l : total);
        return result;
    }
}
