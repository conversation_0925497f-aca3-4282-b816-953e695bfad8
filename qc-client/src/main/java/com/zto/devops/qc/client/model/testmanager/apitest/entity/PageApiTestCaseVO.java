package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

@ZModel(description = "api自动化用例VO")
@Data
public class PageApiTestCaseVO implements Serializable {
    private static final long serialVersionUID = 43066355548949415L;

    @ZModelProperty(description = "用例编码")
    private String caseCode;

    @ZModelProperty(description = "用例名")
    private String caseName;

    @ZModelProperty(description = "接口编码")
    private String apiCode;

    @ZModelProperty(description = "接口名称")
    private String apiName;

    @ZModelProperty(description = "接口地址")
    private String apiAddress;

    @ZModelProperty(description = "用例类别")
    private ApiCaseTypeEnum caseTypeEnum;
    private Integer caseTypeNum;

    public ApiCaseTypeEnum getCaseTypeEnum() {
        if (null != this.caseTypeNum) {
            this.caseTypeEnum = ApiCaseTypeEnum.codeOf(this.caseTypeNum);
        }
        return this.caseTypeEnum;
    }

    @ZModelProperty(description = "用例类别(中文描述)")
    private String caseTypeDesc;

    public String getCaseTypeDesc() {
        if (null != this.getCaseTypeEnum()) {
            this.caseTypeDesc = this.getCaseTypeEnum().getDesc();
        }
        return this.caseTypeDesc;
    }

    @ZModelProperty(description = "最近一次执行任务id")
    private String taskCode;

    @ZModelProperty(description = "最近一次执行结果(枚举)")
    private TestPlanCaseStatusEnum testResult;

    @ZModelProperty(description = "最近一次执行结果(中文描述)")
    private String testResultDesc;

    public String getTestResultDesc() {
        return Objects.nonNull(this.testResult) ? this.testResult.getValue() : "";
    }

    @ZModelProperty(description = "最近一次执行耗时(秒)")
    private Long duration;

    public Long getDuration() {
        if (StringUtils.isBlank(this.taskCode)) {
            this.duration = 0l;
        }
        return this.duration;
    }

    @ZModelProperty(description = "最近一次执行时间")
    private Date executeTime;

    public Date getExecuteTime() {
        if (StringUtils.isBlank(this.taskCode)) {
            this.executeTime = null;
        }
        return this.executeTime;
    }

    @ZModelProperty(description = "用例发布状态[edit(草稿);publish(使用中)]")
    private ApiCaseStatusEnum status;

    @ZModelProperty(description = "用例状态[删除 DELETED(0); 启用 ENABLED(1); 禁用 DISABLED(2);]")
    private ApiCaseEnableEnum enable;

    @ZModelProperty(description = "用例状态(中文描述)")
    private String enableDesc;

    public String getEnableDesc() {
        return ApiCaseEnableEnum.getApiTestCaseDesc(Objects.nonNull(this.enable) ? this.enable.getCode() : -1);
    }

    @ZModelProperty(description = "用例状态(数字)")
    private Integer enableNum;

    public Integer getEnableNum() {
        return Objects.nonNull(this.enable) ? this.enable.getCode() : -1;
    }

    @ZModelProperty(description = "最近一次执行用例数")
    private Integer executeCaseCount;

    public Integer getExecuteCaseCount() {
        if (StringUtils.isBlank(this.taskCode)) {
            this.executeCaseCount = 0;
        }
        return this.executeCaseCount;
    }

    @ZModelProperty(description = "接口文档版本号")
    private String docVersion;

    @ZModelProperty(description = "tag统计")
    private String tagValue;

    @ZModelProperty(description = "tag类型")
    private List<SceneTagVO> tagVOList;

    public List<SceneTagVO> getTagVOList() {
        if (StringUtils.isBlank(this.tagValue)) {
            return new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(this.tagVOList)) {
            this.tagVOList = new ArrayList<>();
        }
        Arrays.asList(this.tagValue.split(",")).forEach(tag -> {
            SceneTagVO tagVO = new SceneTagVO();
            tagVO.setTagName(SceneTagEnum.enumOf(tag));
            tagVO.setTagNameDesc(SceneTagEnum.getValue(tag));
            this.tagVOList.add(tagVO);
        });
        return this.tagVOList;
    }

    @ZModelProperty(description = "调用类型", sample = "HTTP")
    private String apiType;

    @ZModelProperty(description = "变量名")
    private String key;

    @ZModelProperty(description = "接口配置类型枚举", hidden = true)
    private ApiConfigTypeEnum apiConfigTypeEnum;

    @ZModelProperty(description = "接口配置类型数字", hidden = true)
    private Integer apiConfigType;

    public Integer getApiConfigType() {
        return null != this.apiConfigTypeEnum ? apiConfigTypeEnum.getCode() : 0;
    }

}
