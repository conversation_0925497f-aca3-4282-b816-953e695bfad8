package com.zto.devops.qc.client.model.relevantUser.query;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.client.simple.Product;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.framework.client.simple.Button;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class MyTaskVO extends PageQueryBase  implements Serializable {

    @GatewayModelProperty(description = "状态")
    private String status;

    @GatewayModelProperty(description = "状态描述")
    private String statusDesc;

    @GatewayModelProperty(description = "名称")
    private String name;

    @GatewayModelProperty(description = "优先级")
    private String priority;

    @GatewayModelProperty(description = "优先级")
    private String priorityDesc;

    private DomainEnum domainEnum;

    private String relevantUserType;

    private String planStartDate;
    private String planEndDate;
    private String actualStartDate;
    private String actualEndDate;
    @GatewayModelProperty(description = "起止时间")
    private String spannedTime;

    private double planHours;
    private double actualHours;

    @GatewayModelProperty(description = "工时展示")
    private String planWorkHours;

    @GatewayModelProperty(description = "任务类型CODE")
    private String typeCode;

    @GatewayModelProperty(description = "提醒")
    private String warn;

    @GatewayModelProperty(description = "未处理的天数")
    private double warnDay;

    @GatewayModelProperty(description = "所属产品")
    private Product product;

    @GatewayModelProperty(description = "所属产品")
    private String productCode; //冗余字段，特殊逻辑使用

    @GatewayModelProperty(description = "需求级别")
    private String level;

    @GatewayModelProperty(description = "项目级别")
    private String levelDesc;

    @GatewayModelProperty(description = "提出人")
    private User introducer;

    @GatewayModelProperty(description = "发现版本")
    private Version findVersion;

    @GatewayModelProperty(description = "开发人员")
    private User developer;

    @GatewayModelProperty(description = "测试人员")
    private User tester;

    @GatewayModelProperty(description = "开始时间")
    private Date startDate;

    @GatewayModelProperty(description = "发布时间")
    private Date publishDate;

    @GatewayModelProperty(description = "结束时间")
    private Date endTime;

    @GatewayModelProperty(description = "项目级别")
    private String projectLevel;

    @GatewayModelProperty(description = "项目级别")
    private String projectLevelDesc;

    @GatewayModelProperty(description = "项目规模")
    private String projectScale;

    @GatewayModelProperty(description = "项目规模描述")
    private String projectScaleDesc;

    @GatewayModelProperty(description = "项目类型")
    private String projectType;

    @GatewayModelProperty(description = "项目类型")
    private String projectTypeDesc;

    @GatewayModelProperty(description = "处理类型")
    private String processType;

    @GatewayModelProperty(description = "处理类型")
    private String processTypeDesc;

    @GatewayModelProperty(description = "关联版本")
    private Version associatedVersion;

    @GatewayModelProperty(description = "对接人")
    private User dockingUser;

    @GatewayModelProperty(description = "业务code")
    private String businessCode;

    //创建人
    private Long creatorId;
    // 处理人
    private Long handlerId;
    private String handler;
    // 完成时间
    private Date finishTime;
    // 报告人
    private Long findUserId;

    //版本是否确认
    private Boolean isConfirm;
    // 版本类型
    private String versionType;

    // 版本发布类型
    private String publishStrategy;
    // 版本测试测旅
    private String testStrategy;

    // 风险
    private String riskStatus;
    private Long riskOwnerId;
    private Long riskCreatorId;
    // 需求的中间状态标签
    //private RequirementTagStatusEnum assessTag;
    //评审信息
    private String reviewUserId;
    private String reviewType;
    //需求类型
    private String requirementType;

    private String approveType;

    private String secondBusinessCode;


    @GatewayModelProperty(description = "可操作按钮")
    private List<Button> buttonVOs = new ArrayList<>();
    // 关联版本
    private String versionCode;

    private String versionName;

    /**
     * 项目 进度
     */
    private Integer projectSchedule;


    /**
     * 项目 健康度
     */
    private String projectHealth;

    /**
     * 项目 健康度
     */
    private String projectHealthDesc;

    private Date gmtCreate;

}
