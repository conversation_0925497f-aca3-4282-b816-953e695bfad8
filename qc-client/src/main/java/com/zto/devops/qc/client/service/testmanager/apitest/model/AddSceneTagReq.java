package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "新增场景标签入参")
public class AddSceneTagReq implements Serializable {

    @ZsmpModelProperty(description = "业务场景编码，场景编码code", sample = "SNF354878t8356499376")
    private List<String> businessCodeList;

    @ZsmpModelProperty(description = "所属领域", sample = "SCENE_TAG")
    private DomainEnum domain;

    @ZsmpModelProperty(description = "标签名称", sample = "test")
    private List<String> tagNameList;

    @ZModelProperty(description = "产品编号", required = true, sample = "399")
    @Size(min = 1, max = 50)
    private String productCode;

}
