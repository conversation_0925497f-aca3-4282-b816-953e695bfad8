package com.zto.devops.qc.client.service.coverage.model.resp;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryReasonListResp implements Serializable {

    private static final long serialVersionUID = 7715357495343279710L;

    @GatewayModelProperty(description = "代码覆盖率不达标原因枚举列表")
    private List<String> allReasonList;

    @GatewayModelProperty(description = "系统原因list")
    private List<String> reasonList;

    @GatewayModelProperty(description = "自定义原因")
    private String customReason;

}
