package com.zto.devops.qc.client.model.testmanager.apitest.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ExceptionCaseOrderFieldEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PageApiExceptionCaseQuery extends PageQueryBase {
    private static final long serialVersionUID = 5742844806999825950L;

    /**
     * 上级用例code
     */
    private String parentCaseCode;

    /**
     * 用例code
     */
    private String caseCode;

    /**
     * 生成规则集合
     */
    private List<Integer> generateRulesList;

    /**
     * 接口用例状态
     */
    private ApiCaseStatusEnum status;

    /**
     * 变量名
     */
    private String key;

    /**
     * 排序字段
     */
    private String orderField;

    public String getOrderField() {
        return ExceptionCaseOrderFieldEnum.getByName(this.orderField);
    }

    /**
     * 排序顺序
     */
    private String orderType;

}
