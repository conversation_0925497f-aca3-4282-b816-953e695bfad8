package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;

@Data
@ZsmpModel(description = "redis列表查询请求模型")
public class listRedisQueryReq implements Serializable {
    private static final long serialVersionUID = -5877454527695514279L;

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "模糊查询")
    private String search;

    public String getSearch() {
        return StringUtils.isBlank(this.search) ? Strings.EMPTY : this.search;
    }
}
