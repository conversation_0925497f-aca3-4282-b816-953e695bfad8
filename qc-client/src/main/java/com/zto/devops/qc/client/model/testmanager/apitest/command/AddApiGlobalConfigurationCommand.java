package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiGlobalConfigurationVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
@Setter
@Getter
public class AddApiGlobalConfigurationCommand extends BaseCommand {

    private String productCode;

    private List<ApiGlobalConfigurationVO> apiGlobalConfigurationVOList;

    public AddApiGlobalConfigurationCommand(String aggregateId) {
        super(aggregateId);
    }
}
