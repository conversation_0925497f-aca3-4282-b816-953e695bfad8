package com.zto.devops.qc.client.service.knowledgebase;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.service.knowledgebase.model.KnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.client.service.knowledgebase.model.KnowledgeBaseByProductCodeResp;
import com.zto.devops.qc.client.service.knowledgebase.model.RemoveKnowledgeBaseByProductCodeReq;
import com.zto.devops.qc.client.service.knowledgebase.model.AddKnowledgeBaseByProductCodeReq;

public interface KnowledgeBaseService {

    Result<KnowledgeBaseByProductCodeResp> getKnowledgeBaseDetail(KnowledgeBaseByProductCodeReq req);


    /**
     *
     * @param req
     * @return
     */
    Result<Void> addMemberPermission(AddKnowledgeBaseByProductCodeReq req);


    /**
     *
     * @param req
     * @return
     */
    Result<Void> RemoveMemberPermission(RemoveKnowledgeBaseByProductCodeReq req);


    Result<Void> synchronizeData();

    Result<Void> synchronizePermissionData();

    Result<Void> createProductKnowledge(String productCode);


}
