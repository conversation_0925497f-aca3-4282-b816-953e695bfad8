package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImportDataResp implements Serializable {

    @GatewayModelProperty(description = "导入数据", required = false)
    private List<TestCaseExcelColumn> data;

    @GatewayModelProperty(description = "报错信息", required = false)
    private String errorMsg;

    @GatewayModelProperty(description = "是否导入成功", required = false)
    private Boolean importFlag;

}
