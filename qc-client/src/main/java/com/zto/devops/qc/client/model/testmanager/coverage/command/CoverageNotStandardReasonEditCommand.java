package com.zto.devops.qc.client.model.testmanager.coverage.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class CoverageNotStandardReasonEditCommand extends BaseCommand {

    private String versionCode;

    private String appId;

    private String productCode;

    private List<String> reasonList;

    private String customReason;

    public CoverageNotStandardReasonEditCommand(String aggregateId) {
        super(aggregateId);
    }
}
