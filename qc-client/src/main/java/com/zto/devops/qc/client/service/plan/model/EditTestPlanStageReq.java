package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanButttonTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EditTestPlanStageReq implements Serializable {
    private static final long serialVersionUID = 2431021643789028540L;

    @ZModelProperty(description = "主键Code", required = false, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "按钮类型", required = false, sample = "START")
    private TestPlanButttonTypeEnum type;

    @ZModelProperty(description = "阶段", required = false, sample = "SMOKE_TEST")
    private TestPlanStageEnum stage;

}
