package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class EditExternalTestReportReq extends SaveBasicInfoReq implements Serializable {

    @ZModelProperty(description = "zui测试结果", required = false, sample = "NO_PASS")
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "验收开始时间", required = false, sample = "1711987200000")
    private Date checkStartDate;

    @ZModelProperty(description = "验收结束时间", required = false, sample = "1711987200000")
    private Date checkEndDate;

    @ZModelProperty(description = "实际上线时间", required = false, sample = "1711987200000")
    private Date actualPublishDate;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false, sample = "-1")
    private Integer delay;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false, sample = "-1")
    private String delayDesc;

    @ZModelProperty(description = "总结、分析、描述", required = false, sample = "总结")
    private String summary;

    @ZModelProperty(description = "附件", required = false, sample = "[]")
    private List<AttachmentVO> attachments;

    @ZModelProperty(description = "总体测试结果", required = false, sample = "ONCE_SUCCESS")
    private TmTestResultEnum testResult;

    @ZModelProperty(description = "ui测试结果", required = false, sample = "PASS")
    private UiTestResultEnum uiTestResult;
}
