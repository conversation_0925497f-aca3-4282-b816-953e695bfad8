package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "终止自动化任务Req")
public class BatchTerminateTaskReq implements Serializable {
    private static final long serialVersionUID = -5853797771617377775L;

    @ZModelProperty(description = "任务唯一标识", sample = "SNF987293442717515776")
    private String code;

    @ZModelProperty(description = "是否是父任务（true：是;false：否）", sample = "0")
    private Boolean parentFlag;
}
