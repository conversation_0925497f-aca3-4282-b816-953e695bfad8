package com.zto.devops.qc.client.service.report.model;

import cn.hutool.core.date.DateUtil;
import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.devops.qc.client.model.report.entity.IssueInfoVO;
import com.zto.devops.qc.client.model.report.entity.IssueLegacyVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.PublishVersionVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SimpleTestReportDetailResp extends BasicInfoResp {

    @ZModelProperty(description = "测试策略", required = false)
    private String testStrategy;

    @ZModelProperty(description = "是否包含zui应用（true：包含；false：不包含）")
    private Boolean zuiFlag;

    @ZModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "报告类型", required = false)
    private ReportType reportType;

    @ZModelProperty(description = "报告类型描述", required = false)
    private String reportTypeDesc;

    public String getReportTypeDesc() {
        return Objects.isNull(this.reportType) ? "" : this.reportType.getValue();
    }

    @ZModelProperty(description = "计划提测时间", required = false)
    private Date planPresentationDate;

    @ZModelProperty(description = "计划提测时间--上下午", required = false)
    private String planPresentationDay;

    @ZModelProperty(description = "计划上线时间", required = false)
    private Date planOnlineDate;

    @ZModelProperty(description = "版本开始时间", required = false)
    private Date startDate;

    @ZModelProperty(description = "是否按计划范围上线", required = false)
    private Integer asPlanedOnline;

    @ZModelProperty(description = "是否延期", required = false)
    private Integer delay;

    @ZModelProperty(description = "开发人数", required = false)
    private Integer developerCount;

    @ZModelProperty(description = "测试人数", required = false)
    private Integer testerCount;

    @ZModelProperty(description = "测试信息", required = false)
    private CaseExecuteResultVO caseExecuteResultVO;

    @ZModelProperty(description = "缺陷信息统计", required = false)
    private List<IssueInfoVO> issueInfoVOS;

    @ZModelProperty(description = "遗留缺陷", required = false)
    private List<IssueLegacyVO> issueLegacyVOS;

    @ZModelProperty(description = "附件", required = false)
    private List<AttachmentVO> attachments;

    @ZModelProperty(description = "实际上线时间", required = false)
    private Date actualOnlineDate;

    @ZModelProperty(description = "实际提测时间", required = false)
    private Date actualPresentationDate;

    @ZModelProperty(description = "实际提测时间--上午、下午", required = false)
    private String actualPresentationDay;

    public String getActualPresentationDay() {
        return Objects.isNull(this.actualPresentationDate) ? "09:00:00"
                : DateUtil.isPM(this.actualPresentationDate) ? "18:00:00" : "09:00:00";
    }

    @ZModelProperty(description = "计划准出时间", required = false)
    private Date planApprovalExitDate;

    @ZModelProperty(description = "计划准出时间--上下午", required = false)
    private String planApprovalExitDay;

    @ZModelProperty(description = "实际准出时间", required = false)
    private Date actualApprovalExitDate;

    @ZModelProperty(description = "实际准出时间--上下午", required = false)
    private String actualApprovalExitDay;

    public String getActualApprovalExitDay() {
        return Objects.isNull(this.actualApprovalExitDate) ? "09:00:00" :
                DateUtil.isPM(this.actualApprovalExitDate) ? "18:00:00" : "09:00:00";
    }

    @GatewayModelProperty(description = "延期验收期间发版记录", required = false)
    private List<PublishVersionVO> publishRecordList;

}
