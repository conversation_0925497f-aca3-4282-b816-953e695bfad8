package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseBatchOperationEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class BatchChangeApiCaseStatusCommand extends BaseCommand {
    private static final long serialVersionUID = 7335566570013627487L;

    private String productCode;

    private List<String> apiCaseCodeList;

    private ApiCaseBatchOperationEnum operation;

    public BatchChangeApiCaseStatusCommand(String aggregateId) {
        super(aggregateId);
    }
}
