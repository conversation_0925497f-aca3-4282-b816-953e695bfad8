package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZModel(description = "单个代码覆盖率报告生成接口入参")
public class GenerateCoverageReq implements Serializable {
    private static final long serialVersionUID = 1870509636783316888L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", required = true, sample = "PRO2207128000")
    private String productCode;

    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "版本名称", required = true, sample = "V2.92.0 test")
    private String versionName;

    @ZModelProperty(description = "应用名称", required = true, sample = "zto-qamp")
    private String appId;

    @ZModelProperty(description = "报告类型", required = true, sample = "BRANCH")
    private RecordTypeEnum recordType;

    @ZModelProperty(description = "差异类型", sample = "INCREMENT")
    private DiffTypeEnum diffType;

}
