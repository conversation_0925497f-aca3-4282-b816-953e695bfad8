package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseBatchOperationEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "批量修改用例状态Req")
public class BatchChangeApiCaseStatusReq implements Serializable {
    private static final long serialVersionUID = 3981174797917980450L;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "用例code集合", required = true, sample = "TC123457988923")
    private List<String> apiCaseCodeList;

    @ZModelProperty(description = "操作", required = true, sample = "DELETED")
    private ApiCaseBatchOperationEnum operation;

}
