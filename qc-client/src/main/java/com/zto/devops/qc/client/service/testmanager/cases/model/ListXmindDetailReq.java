package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "xmind查询模型")
public class ListXmindDetailReq extends ListTestcaseReq implements Serializable {

    @ZModelProperty(description = "用例code", required = false, sample = "TC231130064892")
    private String caseCode;

    @ZModelProperty(description = "直接展开 or 作为根节点", required = false, sample = "0")
    private Boolean isRoot;

    @ZModelProperty(description = "执行结果list", required = false, sample = "['PASSED']")
    private List<TestPlanCaseStatusEnum> resultList;

    @ZModelProperty(description = "执行人", required = false, sample = "[5878415]")
    private List<Long> executorIdList;

    @ZModelProperty(description = "测试计划关联用例版本code", required = false, sample = "['SNF994641594810368000']")
    private List<String> planCaseVersionCodeList;

}
