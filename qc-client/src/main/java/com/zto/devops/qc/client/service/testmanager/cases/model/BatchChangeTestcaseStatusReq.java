package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAbandonReasonEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.devops.qc.client.model.testmanager.cases.entity.BatchOperateCaseVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchChangeTestcaseStatusReq implements Serializable {

    @ZModelProperty(description = "用例唯一标识", required = true, sample = "[]")
    private List<BatchOperateCaseVO> codeList;

    @ZModelProperty(description = "状态 NORMAL DISABLE", required = true, sample = "NORMAL")
    private TestcaseStatusEnum status;

    @ZModelProperty(description = "停用原因 状态为停用时必填 OUT_OF_USE EXPIRED OTHER", required = false, sample = "OUT_OF_USE")
    private TestcaseAbandonReasonEnum abandonReason;

    @ZModelProperty(description = "是否自动化用例[true:是;false:不是;]", required = false, sample = "1")
    private Boolean autoFlag;

}
