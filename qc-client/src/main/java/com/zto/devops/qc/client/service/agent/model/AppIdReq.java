package com.zto.devops.qc.client.service.agent.model;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AppIdReq implements Serializable {
    private static final long serialVersionUID = -5515287568958698518L;

    @ZModelProperty(description = "appId", required = true, sample = "devops-qc")
    private String appId;

    @ZModelProperty(description = "pageNum", required = true, sample = "1")
    private Integer pageNum;

    @ZModelProperty(description = "pageSize", required = true, sample = "50")
    private Integer pageSize;


}
