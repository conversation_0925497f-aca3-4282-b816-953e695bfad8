package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EditTestPlanStatusReq implements Serializable {
    private static final long serialVersionUID = 3950330015748300149L;

    @ZModelProperty(description = "主键Code", required = false,sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "计划状态", required = false, sample = "IN_PROGRESS")
    private TestPlanNewStatusEnum status;

}
