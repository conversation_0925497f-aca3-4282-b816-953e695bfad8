package com.zto.devops.qc.client.service.report.model;

import java.io.Serializable;

import com.zto.devops.framework.client.simple.User;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PageReportReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "版本号", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "报告类型", required = false)
    private List<Integer> reportTypeList;

    @GatewayModelProperty(description = "创建时间-开始", required = false)
    private Date gmtCreateStart;

    @GatewayModelProperty(description = "创建时间-结束", required = false)
    private Date gmtCreateEnd;

    @GatewayModelProperty(description = "产品code", required = false)
    private String  productCode;

    private User transactor;
}
