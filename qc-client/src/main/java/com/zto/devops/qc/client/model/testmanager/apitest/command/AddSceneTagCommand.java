package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.framework.client.enums.DomainEnum;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class AddSceneTagCommand extends BaseCommand implements Serializable  {

    private List<String> businessCodeList;

    private DomainEnum domain;

    private List<String> tagNameList;

    private String productCode;

    public AddSceneTagCommand(String aggregateId) {
        super(aggregateId);
    }
}
