package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.model.report.entity.SecurityHoleVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目名称：qc-parent
 * 类 名 称：SecurityTestResultResp
 * 类 描 述：TODO
 * 创建时间：2021/11/29 10:40 上午
 * 创 建 人：renxinhui
 *
 * <AUTHOR>
 */
@Data
public class SecurityTestResultResp implements Serializable {

    @ZModelProperty(description = "安全测试人ID", required = false)
    private Long securityUserId;

    @ZModelProperty(description = "安全测试人名称", required = false)
    private String securityUserName;

    @ZModelProperty(description = "安全测试结果", required = false)
    private SecurityTestResult securityTestResult;

    @ZModelProperty(description = "安全测试结果code", required = false)
    private String securityTestResultCode;

    @ZModelProperty(description = "安全测试结果描述", required = false)
    private String securityTestResultDesc;

    @ZModelProperty(description = "安全漏洞", required = false)
    private List<SecurityHoleVO> securityHoles;

    /**
     * 数据组装
     *
     * @return
     */
    public static SecurityTestResultResp buildSelf(List<SecurityHoleVO> securityHoles) {
        SecurityTestResultResp resp = new SecurityTestResultResp();
        resp.setSecurityHoles(securityHoles);
        return resp;
    }
}
