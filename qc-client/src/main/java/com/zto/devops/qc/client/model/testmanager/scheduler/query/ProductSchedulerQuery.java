package com.zto.devops.qc.client.model.testmanager.scheduler.query;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class ProductSchedulerQuery implements Serializable {
    private static final long serialVersionUID = -6681133622471503051L;

    @ZsmpModelProperty(description = "产品code",required = true)
    private String productCode;

    @ZsmpModelProperty(description = "任务名")
    private String schedulerName;

}
