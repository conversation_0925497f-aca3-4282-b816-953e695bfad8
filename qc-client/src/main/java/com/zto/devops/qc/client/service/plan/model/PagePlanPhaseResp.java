package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanNewTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStrategyEnum;
import com.zto.devops.qc.client.model.testmanager.plan.entity.TestPlanStageVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PagePlanPhaseResp implements Serializable {

    private static final long serialVersionUID = 6627740639638713066L;

    @GatewayModelProperty(description = "测试计划code", required = false)
    private String planCode;

    @GatewayModelProperty(description = "测试计划名称", required = false)
    private String planName;

    @GatewayModelProperty(description = "计划类型", required = false)
    private TestPlanNewTypeEnum type;

    @GatewayModelProperty(description = "计划类型 描述", required = false)
    private String typeDesc;

    @GatewayModelProperty(description = "测试策略", required = false)
    private TestPlanStrategyEnum testStrategy;

    @GatewayModelProperty(description = "策略 描述", required = false)
    private String testStrategyDesc;

    @GatewayModelProperty(description = "状态", required = false)
    private TestPlanNewStatusEnum status;

    @GatewayModelProperty(description = "状态 描述", required = false)
    private String statusDesc;

    @GatewayModelProperty(description = "状态 描述", required = false)
    List<TestPlanStageVO> testPlanStageVOList;
}
