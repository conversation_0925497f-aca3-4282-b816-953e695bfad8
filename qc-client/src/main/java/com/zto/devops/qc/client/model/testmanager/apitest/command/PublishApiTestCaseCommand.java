package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PublishApiTestCaseCommand extends BaseCommand {

    private String productCode;

    private String caseCode;

    public PublishApiTestCaseCommand(String aggregateId) {
        super(aggregateId);
    }
}
