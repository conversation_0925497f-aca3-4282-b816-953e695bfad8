package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.service.testmanager.cases.model.ExecuteAutomaticTaskReq;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SceneBatchExecuteReq extends ExecuteAutomaticTaskReq implements Serializable {

    private static final long serialVersionUID = 2870389293060207356L;

    @ZsmpModelProperty(description = "场景code", required = true)
    private List<String> sceneCodeList;

}
