package com.zto.devops.qc.client.service.agent.model.entity;

import lombok.Data;

import java.util.Objects;

@Data
public class Worker {
    private String appId;
    private String versionCode;
    private String clientCode;

    public Worker(String appId, String versionCode, String clientCode) {
        this.appId = appId;
        this.versionCode = versionCode;
        this.clientCode = clientCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) return true;
        if (object == null || getClass() != object.getClass()) return false;
        Worker worker = (Worker) object;
        return Objects.equals(appId, worker.appId) && Objects.equals(versionCode, worker.versionCode) && Objects.equals(clientCode, worker.clientCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(appId, versionCode, clientCode);
    }

    @Override
    public String toString() {
        return "Worker{" +
                "appId='" + appId + '\'' +
                ", versionCode='" + versionCode + '\'' +
                ", clientCode='" + clientCode + '\'' +
                '}';
    }

    public String getKey() {
        return String.format("%s:%s:%s", appId, versionCode, clientCode);
    }
}
