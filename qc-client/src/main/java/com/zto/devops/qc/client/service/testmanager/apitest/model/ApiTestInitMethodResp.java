package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "内置函数响应模型")
public class ApiTestInitMethodResp implements Serializable {

    @ZsmpModelProperty(description = "函数描述")
    private String methodDesc;

    @ZsmpModelProperty(description = "函数签名")
    private String methodSign;

    @ZsmpModelProperty(description = "函数名称")
    private String methodName;

    @ZsmpModelProperty(description = "函数参数")
    private List<ApiTestInitMethodParametersVO> parameters;
}
