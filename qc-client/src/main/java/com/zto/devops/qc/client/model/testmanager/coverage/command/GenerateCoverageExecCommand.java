package com.zto.devops.qc.client.model.testmanager.coverage.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/9/20 15:17
 */
@Setter
@Getter
public class GenerateCoverageExecCommand extends BaseCommand {

    @GatewayModelProperty(description = "版本编码", required = true)
    private String versionCodes;
    @GatewayModelProperty(description = "分支名称", required = true)
    private String branchName;
    @GatewayModelProperty(description = "创建者编号", required = true)
    private Integer creatorId;
    @GatewayModelProperty(description = "创建者", required = true)
    private String creator;
    @GatewayModelProperty(description = "关联应用集", required = true)
    private List<String> appIds;

    public GenerateCoverageExecCommand(String aggregateId) {
        super(aggregateId);
    }

}
