package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditAutomaticSchedulerReq extends AddAutomaticSchedulerReq implements Serializable {
    private static final long serialVersionUID = -7211771273661998409L;

    @ZModelProperty(description = "任务编码", required = true, sample = "SNF975268955453128704")
    @Auth(type = AuthTypeConstant.SCHEDULER_CODE)
    private String schedulerCode;

    @ZModelProperty(description = "是否开启", required = false, sample = "0")
    private Boolean switchFlag;
}
