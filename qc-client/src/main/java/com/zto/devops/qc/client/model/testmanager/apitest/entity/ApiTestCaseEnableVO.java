package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class ApiTestCaseEnableVO implements Serializable {
    private static final long serialVersionUID = 1509351173686210066L;

    /**
     * 用例code
     */
    private String caseCode;

    /**
     * 启用/禁用状态concat
     */
    private String enableStr;

    /**
     * 草稿/发布状态统计concat
     */
    private String statusStr;

    /**
     * 是否支持启用
     * true = 已发布+禁用
     */
    private boolean supportEnable;

    public boolean getSupportEnable() {
        return statusStr.contains(ApiCaseStatusEnum.publish.name())
                && enableStr.contains(String.valueOf(ApiCaseEnableEnum.DISABLED.getCode()));
    }

    /**
     * 是否支持禁用
     * true = 已发布 + 使用中
     */
    private boolean supportDisable;

    public boolean getSupportDisable() {
        return statusStr.contains(ApiCaseStatusEnum.publish.name())
                && enableStr.contains(String.valueOf(ApiCaseEnableEnum.ENABLED.getCode()));
    }

    /**
     * 是否支持执行
     * true = 已发布 + 使用中
     */
    private boolean supportExecute;

    public boolean getSupportExecute() {
        return statusStr.contains(ApiCaseStatusEnum.publish.name())
                && enableStr.contains(String.valueOf(ApiCaseEnableEnum.ENABLED.getCode()));
    }
}
