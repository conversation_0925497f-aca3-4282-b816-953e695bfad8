package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AutomaticRecordSimpleReq implements Serializable {

    /**
     * 所属产品code
     */
    @ZModelProperty(description = "登记库code", required = true, sample = "SNF994641594810368000")
    @Auth(type = AuthTypeConstant.RECORD_CODE)
    private String code;

}
