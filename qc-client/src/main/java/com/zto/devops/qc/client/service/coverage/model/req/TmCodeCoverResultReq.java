package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZModel(description = "查代码覆盖率结果 & 不达标应用列表接口入参")
public class TmCodeCoverResultReq implements Serializable {

    private static final long serialVersionUID = 1870509636783316888L;

    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "报告类型", required = true, sample = "BRANCH")
    private ReportType reportType;

}
