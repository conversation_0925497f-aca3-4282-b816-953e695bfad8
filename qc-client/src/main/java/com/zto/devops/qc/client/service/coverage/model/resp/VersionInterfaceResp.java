package com.zto.devops.qc.client.service.coverage.model.resp;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/9
 * @Version 1.0
 */
@Data
public class VersionInterfaceResp {
    private List<String> paths;

    public List<String> getPaths() {
        if (paths == null) {
            return new ArrayList<>();
        }
        return paths;
    }
}
