package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ZModel(description = "代码覆盖率修改备注接口入参")
public class EditCoverageReq implements Serializable {

    private static final long serialVersionUID = 1870509636783316888L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", required = true, sample = "PRO2207128000")
    private String productCode;

    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "备注", required = true, sample = "备注")
    @Size(max = 2000, min = 1)
    private String comment;

    @ZModelProperty(description = "应用id", required = true, sample = "zto-qamp")
    private String appId;

    @ZModelProperty(description = "差异类型", required = true, sample = "INCREMENT")
    private DiffTypeEnum diffType;

}
