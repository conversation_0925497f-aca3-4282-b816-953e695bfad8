package com.zto.devops.qc.client.model.testmanager.scheduler.event;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.event.MyOperatedEvent;
import com.zto.devops.framework.client.event.ObservedEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
public class AutomaticSchedulerMsgEvent extends BaseEvent implements ActionEvent, MyOperatedEvent, ObservedEvent {

    /**
     * 定时任务名称
     */
    private String schedulerName;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 运行结果
     */
    private AutomaticStatusEnum executeResult;

    /**
     * 任务创建人
     */
    private String creator;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String finishTime;

    /**
     * 消息接收人
     */
    private Set<String> receivedUsers;

    private String userId;

    private String productCode;

    private String productName;

    @Override
    public String processType() {
        return MyOperatedEvent.super.processType();
    }

    @Override
    public String getBusinessCode() {
        return null;
    }

    @Override
    public User getOperator() {
        return null;
    }

    @Override
    public DomainEnum domain() {
        return null;
    }

    @Override
    public String actionCode() {
        return null;
    }

    @Override
    public String action() {
        return null;
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }

    @Override
    public Map<String, ?> metadata() {
        return ActionEvent.super.metadata();
    }

    @Override
    public String logBusinessCode() {
        return ActionEvent.super.logBusinessCode();
    }
}
