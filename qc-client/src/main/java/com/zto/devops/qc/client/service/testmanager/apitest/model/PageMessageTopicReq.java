package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "分页查询消息主题请求参数")
public class PageMessageTopicReq implements Serializable {

    @ZsmpModelProperty(description = "页数")
    private int page = 1;

    @ZsmpModelProperty(description = "数据大小")
    private int size = 20;

    @ZsmpModelProperty(description = "产品code")
    private String productCode;

    @ZsmpModelProperty(description = "模糊查询")
    private String search;
}
