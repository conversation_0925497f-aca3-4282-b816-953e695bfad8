package com.zto.devops.qc.client.service.coverage.model.resp;

import com.zto.devops.qc.client.enums.report.CodeCoverResult;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ZsmpModel(description = "代码覆盖率达标结果响应模型")
public class TmCodeCoverResultResp implements Serializable {

    @GatewayModelProperty(description = "代码覆盖率是否达标结果-enum")
    private CodeCoverResult codeCoverResult;

    @GatewayModelProperty(description = "代码覆盖率是否达标结果-描述")
    private String codeCoverResultDesc;

    @ZsmpModelProperty(description = "代码覆盖率不达标原因枚举列表")
    private List<String> reasonList;

    @GatewayModelProperty(description = "代码覆盖率不达标应用及原因")
    private List<CoverageReasonVO> coverageReasonVOS;

    @GatewayModelProperty(description = "代码覆盖率关联测试计划code")
    private String coverageRelatedTestPlanCode;

}
