package com.zto.devops.qc.client.model.testmanager.scheduler.event;

import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AddSchedulerCasesEvent extends BaseEvent {

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "任务code", required = true)
    private String schedulerCode;

    @ZsmpModelProperty(description = "用例code集合", required = true)
    private List<String> caseCodeList;
}
