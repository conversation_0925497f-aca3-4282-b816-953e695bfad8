
package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "定时任务关联用例")
public class AddSchedulerCasesReq implements Serializable {
    private static final long serialVersionUID = 4850900784877893146L;

    @ZModelProperty(description = "产品code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "任务code", required = true, sample = "SNF975268955453128704")
    private String schedulerCode;

    @ZModelProperty(description = "用例code集合", required = true, sample = "['TC231130064892']")
    private List<String> caseCodeList;
}
