package com.zto.devops.qc.client.service.relevantUser;


/**
 * 任务查询
     *  我的待办
     *  我的已办
     *  我创建的
 *
 *  需求查询
     *  我的待办
     *  我的已办
     *  我创建的
     *  我的关注
     *  抄送我的
 *  迭代查询
     *  我的待办
     *  我的已办
     *  我创建的
 *  版本
     *  我的待办
     *  我的已办
     *  我创建的
 *
 *
 */


import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.qc.client.service.relevantUser.model.MyTaskReq;
import com.zto.devops.qc.client.service.relevantUser.model.MyTaskResp;

/**
 * @ClassName: RequirementGateway
 * @Description: 需求网关
 * @Author: cher
 * @Date: 2021/7/7 17:04
 * @menu 需求相关操作
 **/
public interface RelevantUserQueryService {

    PageResult<MyTaskResp> queryMyIssue(MyTaskReq myTaskReq);

}
