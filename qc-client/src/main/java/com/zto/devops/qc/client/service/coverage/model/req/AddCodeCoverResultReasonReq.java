package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.model.testmanager.coverage.entity.CoverageReasonVO;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "添加代码覆盖率不达标原因接口入参")
public class AddCodeCoverResultReasonReq implements Serializable {
    private static final long serialVersionUID = -5866693156724049783L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", required = true, sample = "PRO2207128000")
    private String productCode;

    @ZModelProperty(description = "代码覆盖率不达标原因")
    private List<CoverageReasonVO> coverageReasonVOS;
}
