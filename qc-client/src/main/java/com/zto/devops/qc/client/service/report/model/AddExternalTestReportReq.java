package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.report.SecurityTestResult;
import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddExternalTestReportReq extends BasicInfoReq implements Serializable {

    @ZModelProperty(description = "zui测试结果", required = false, sample = "PASS")
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "计划提测时间", required = false, sample = "1711987200000")
    private Date presentationDate;

    @ZModelProperty(description = "计划提测时间上午/下午", required = false, sample = "上午")
    private String presentationDay;

    @ZModelProperty(description = "计划上线时间", required = false, sample = "1711987200000")
    private Date publishDate;

    @ZModelProperty(description = "计划上线时间--上下午", required = false, sample = "上午")
    private String publishDay;

    @ZModelProperty(description = "验收开始时间", required = true, sample = "1711987200000")
    private Date checkStartDate;

    @ZModelProperty(description = "验收结束时间", required = true, sample = "1711987200000")
    private Date checkEndDate;

    @ZModelProperty(description = "实际上线时间", required = true, sample = "1711987200000")
    private Date actualPublishDate;

    @ZModelProperty(description = "延期 -1 否，1 是", required = true, sample = "-1")
    private Integer delay;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false, sample = "-1")
    private String delayDesc;

    @ZModelProperty(description = "附件", required = false, sample = "[]")

    private List<AttachmentVO> attachments;

    @ZModelProperty(description = "安全测试人ID",required = true, sample = "5984549")
    private Long securityUserId;

    @ZModelProperty(description = "安全测试人名称", required = true, sample = "luban")
    private String securityUserName;

    @ZModelProperty(description = "安全测试结果code", required = false, sample = "通过")
    private String securityTestResultCode;

    @ZModelProperty(description = "安全测试结果描述", required = false, sample = "安全测试结果描述")
    private String securityTestResultDesc;

    @ZModelProperty(description = "安全测试结果描述", sample = "PASS")
    private SecurityTestResult securityTestResult;

    @ZModelProperty(description = "ui测试结果", required = false, sample = "PASS")
    private UiTestResultEnum uiTestResult;
}
