package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TestcaseRequirementResp implements Serializable {

    @ZsmpModelProperty(description = "编号")
    private String code;

    @ZsmpModelProperty(description = "标题")
    private String title;

    @ZsmpModelProperty(description = "需求状态")
    private String status;

    @ZsmpModelProperty(description = "需求状态说明")
    private String statusDesc;

    @ZsmpModelProperty(description = "当前对接人ID")
    private Long dockingUserId;

    @ZsmpModelProperty(description = "当前对接人名称")
    private String dockingUserName;

}
