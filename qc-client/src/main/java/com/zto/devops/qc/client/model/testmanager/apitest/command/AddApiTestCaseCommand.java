package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddApiTestCaseCommand extends BaseCommand {

    private String productCode;

    private String caseName;

    private String apiCode;

    private ApiCaseStatusEnum status;

    private Integer caseType;

    private String caseReqData;

    private List<Integer> dbIds;

    public AddApiTestCaseCommand(String aggregateId) {
        super(aggregateId);
    }
}
