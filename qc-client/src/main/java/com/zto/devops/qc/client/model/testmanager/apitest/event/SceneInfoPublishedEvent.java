package com.zto.devops.qc.client.model.testmanager.apitest.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.UseCaseFactoryTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SceneInfoPublishedEvent extends BaseEvent implements ActionEvent {

    private String sceneCode;

    private Integer sceneVersion;

    private Integer sceneType;

    @Override
    public String action() {
        return String.format("保存并发布了%s", UseCaseFactoryTypeEnum.SCENE.getCode() == sceneType ? "场景图" : "造数");
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return "版本V" + sceneVersion;
    }

    @Override
    public String getAggregateId() {
        return this.sceneCode;
    }
}
