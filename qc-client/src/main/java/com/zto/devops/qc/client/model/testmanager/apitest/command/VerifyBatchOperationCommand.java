package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseBatchOperationEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiGlobalConfigurationVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class VerifyBatchOperationCommand extends BaseCommand {
    private static final long serialVersionUID = -7045552505801844051L;

    private String productCode;

    private List<String> apiCaseCodeList;

    private ApiCaseBatchOperationEnum operation;

    private List<ApiGlobalConfigurationVO> apiGlobalConfigurationVOList;

    public VerifyBatchOperationCommand(String aggregateId) {
        super(aggregateId);
    }
}
