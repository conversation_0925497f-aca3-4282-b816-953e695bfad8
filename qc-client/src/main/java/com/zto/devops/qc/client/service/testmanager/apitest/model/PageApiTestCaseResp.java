
package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.PageApiTestCaseVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ZsmpModel(description = "查询接口列表（场景图列表筛选）响应模型")
public class PageApiTestCaseResp implements Serializable {
    private static final long serialVersionUID = 8559829288977875224L;

    @GatewayModelProperty(description = "总数")
    private Long total;

    @GatewayModelProperty(description = "用例数据")
    private List<PageApiTestCaseVO> list;

    public static PageApiTestCaseResp buildSelf(List<PageApiTestCaseVO> doList, Long total) {
        PageApiTestCaseResp result = new PageApiTestCaseResp();
        result.setList(CollectionUtil.isEmpty(doList) ? new ArrayList<>() : doList);
        result.setTotal(CollectionUtil.isEmpty(doList) ? 0l : total);
        return result;
    }

    public static PageApiTestCaseResp init() {
        PageApiTestCaseResp result = new PageApiTestCaseResp();
        result.setList(new ArrayList<>());
        result.setTotal(0l);
        return result;
    }
}
