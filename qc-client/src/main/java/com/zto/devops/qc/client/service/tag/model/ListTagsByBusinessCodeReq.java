package com.zto.devops.qc.client.service.tag.model;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

@Data
@GatewayModel(description = "标签模型")
public class ListTagsByBusinessCodeReq implements Serializable {

    @ZModelProperty(description = "编码",sample = "SNF3254786349")
    private String businessCode;

    @ZModelProperty(description = "所属领域", sample = "ISSUE、SCENE、SCENE_PRODUCT")
    private DomainEnum domain;
}
