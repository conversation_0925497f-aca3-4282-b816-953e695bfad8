package com.zto.devops.qc.client.model.testmanager.scheduler.query;

import com.zto.devops.framework.client.query.BaseQuery;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticRecordTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SchedulerModuleListQuery extends BaseQuery {

    @GatewayModelProperty(description = "产品Code")
    private String productCode;

    @GatewayModelProperty(description = "定时任务Code")
    private String schedulerCode;

    @GatewayModelProperty(description = "用例类型", required = false)
    private List<TestcaseTypeEnum> caseTypeList;

    private String parentCode;

    private List<String> codeList;

    private String automaticSourceCode;

    private Boolean isAuto;

    private List<AutomaticRecordTypeEnum> automaticTypeList;

}
