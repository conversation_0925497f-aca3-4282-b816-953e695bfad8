package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryReasonListReq implements Serializable {

    private static final long serialVersionUID = -8480134873561485034L;

    @ZModelProperty(description = "版本code", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "应用名", required = true, sample = "zto-qamp")
    private String appId;

    @ZModelProperty(description = "产品code", required = true, sample = "PRO2207128000")
    private String productCode;

}
