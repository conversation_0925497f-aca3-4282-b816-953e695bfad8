package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "造数收藏/取消收藏造数请求模型")
public class FavoritesPreDataReq implements Serializable {

    @ZsmpModelProperty(description = "产品code", required = true, sample = "399")
    private String productCode;

    @ZsmpModelProperty(description = "类型", required = true, sample = "SCENE")
    private String type;

    @ZsmpModelProperty(description = "造数code", required = true, sample = "SNF955136531410976768")
    private String sceneCode;

    @ZsmpModelProperty(description = "收藏状态", required = true, sample = "true")
    private Boolean favoritesStatus;

}
