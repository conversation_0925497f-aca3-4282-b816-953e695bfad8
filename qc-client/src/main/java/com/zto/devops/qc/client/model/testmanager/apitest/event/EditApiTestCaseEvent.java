package com.zto.devops.qc.client.model.testmanager.apitest.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditApiTestCaseEvent extends BaseEvent implements ActionEvent {

    private String productCode;

    private String caseCode;

    private String caseName;

    private String apiCode;

    private ApiCaseStatusEnum status;

    private Integer caseType;

    private String caseReqData;

    private List<Integer> dbIds;

    private ApiCaseEnableEnum enable;

    private String automaticSourceCode;

    private String relatedApiAddress;

    private String relatedApiName;

    private ApiTypeEnum apiType;

    private String docVersion;

    private Boolean published;

    @Override
    public String action() {
        return "编辑接口用例";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }
}
