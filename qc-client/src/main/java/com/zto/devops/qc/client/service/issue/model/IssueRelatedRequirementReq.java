package com.zto.devops.qc.client.service.issue.model;

import com.zto.devops.qc.client.enums.issue.RequirementLevel;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class IssueRelatedRequirementReq implements Serializable {

    @ZModelProperty(description = "需求编码", required = true, sample = "FN240416004023")
    private String requirementCode;
    @ZModelProperty(description = "需求名称", required = true, sample = "需求名称")
    private String requirementName;
    @ZModelProperty(description = "需求等级", required = false, sample = "REQUIREMENT")
    private RequirementLevel requirementLevel;
    @ZModelProperty(description = "缺陷code", required = true, sample = "ISS230303008063")
    private String issueCode;
    @ZModelProperty(description = "用户名称", required = true, sample = "用户名称")
    private String userName;
    @ZModelProperty(description = "用户编码", required = true, sample = "用户编码")
    private Long userId;
}
