package com.zto.devops.qc.client.service.coverage.model.req;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.MethodTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "查询接口覆盖率列表请求模型")
@Accessors(chain = true)
public class PageInterfaceCoverageInfoReq implements Serializable {

    @ZsmpModelProperty(description = "页数", required = true)
    private int page;

    @ZsmpModelProperty(description = "数据大小", required = true)
    private int size;

    @Auth(type = AuthTypeConstant.VERSION_CODE)
    @ZModelProperty(description = "版本编码", required = true, sample = "VER2308098037")
    private String versionCode;

    @ZModelProperty(description = "应用名", required = true, sample = "devops-qc")
    private String appId;

    @ZModelProperty(description = "方法的全类名", sample = "com/zto/devops/qc/xxxxx")
    private String interfaceFullClassName;

    @ZModelProperty(description = "方法名", sample = "newRuleConfig")
    private String interfaceMethodName;

    @ZModelProperty(description = "方法参数（描述）", sample = "Lcom/zto/devops/qc/xxx;Lcom/zto/xxxx/User;")
    private String interfaceMethodDesc;

    @ZModelProperty(description = "接口类型", sample = "DUBBO/HTTP/MQ/JOB")
    private MethodTypeEnum interfaceType;

    @ZModelProperty(description = "状态", sample = "0 or 1")
    private Integer status;

    @ZModelProperty(description = "commitId")
    private String commitId;

}
