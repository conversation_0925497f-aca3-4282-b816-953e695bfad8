package com.zto.devops.qc.client.service.report.model;

import java.io.Serializable;

import com.zto.devops.qc.client.model.report.entity.OpertatedButton;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OperatedTestManageResp implements Serializable {

    private static final long serialVersionUID = 1L;


    @ZModelProperty(description = "可执行的报告", required = true)
    private List<OpertatedButton> testPlanOperations ;

    @ZModelProperty(description = "报告类型", required = true)
    private List<OpertatedButton> testReportOperations ;

}
