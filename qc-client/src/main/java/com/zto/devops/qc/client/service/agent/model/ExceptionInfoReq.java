package com.zto.devops.qc.client.service.agent.model;

import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExceptionInfoReq implements Serializable {

    private static final long serialVersionUID = -5515287544758644517L;

    @ZModelProperty(description = "appId", required = true, sample = "devops-qc")
    private String appId;

    @ZModelProperty(description = "版本编号", required = true, sample = "VER000000001")
    private String versionCode;

    @ZModelProperty(description = "类全路径", required = true, sample = "com/zto/example/service/impl/DubboTestServiceImpl")
    private String classPath;

    @ZModelProperty(description = "行号", required = true, sample = "19")
    private Integer lineNum;

    @ZModelProperty(description = "commitId", required = false, sample = "e7a657a63047d4432a3ad37ae0face270ffbbb78")
    private String commitId;

    @ZModelProperty(description = "id", required = false, sample = "1")
    private Long id;

}
