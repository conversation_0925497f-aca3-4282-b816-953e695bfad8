package com.zto.devops.qc.client.model.testmanager.apitest.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneInfoEnableEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ChangeSceneStatusEvent extends BaseEvent implements ActionEvent {

    private String sceneCode;

    private SceneInfoEnableEnum enable;

    @Override
    public String action() {
        return SceneInfoEnableEnum.PUBLISHED.equals(enable) ? "启用了场景图" : "停用了场景图";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }

    @Override
    public String getAggregateId() {
        return this.sceneCode;
    }
}
