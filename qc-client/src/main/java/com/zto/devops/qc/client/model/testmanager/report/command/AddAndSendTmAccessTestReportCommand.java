package com.zto.devops.qc.client.model.testmanager.report.command;

import com.zto.devops.qc.client.model.report.entity.SendUserInfoVO;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AddAndSendTmAccessTestReportCommand extends TmAccessReportCommand {
    public AddAndSendTmAccessTestReportCommand(String aggregateId) {
        super(aggregateId);
    }

    @GatewayModelProperty(description = "发送人Id", required = false)
    private Long sendUserId;

    @GatewayModelProperty(description = "发送人姓名", required = false)
    private String sendUserName;

    @GatewayModelProperty(description = "收件人", required = false)
    private List<SendUserInfoVO> receiveUsers;

    @GatewayModelProperty(description = "抄送人", required = false)
    private List<SendUserInfoVO> ccUsers;

    @GatewayModelProperty(description = "用例数", required = false)
    private Integer testCaseNum;

    @GatewayModelProperty(description = "计划用例数", required = false)
    private Integer planCaseNum;

    @GatewayModelProperty(description = "通过用例数", required = false)
    private Integer permitNum;


    /**
     * 设置用例数
     *
     * @param vo {@link CaseExecuteResultVO}
     */
    public void setCaseNum(CaseExecuteResultVO vo) {
        if (null == vo) {
            return;
        }
        if (null != vo.getTotalNumVO()) {
            this.testCaseNum = vo.getTotalNumVO().getSumNum().intValue();
        }
        if (null != vo.getPlanNumVO()) {
            this.planCaseNum = vo.getPlanNumVO().getSumNum().intValue();
        }
        if (null != vo.getPassNumVO()) {
            this.permitNum = vo.getPassNumVO().getSumNum().intValue();
        }
    }
}
