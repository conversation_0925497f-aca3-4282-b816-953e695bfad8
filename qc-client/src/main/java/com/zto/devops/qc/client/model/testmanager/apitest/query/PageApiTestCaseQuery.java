package com.zto.devops.qc.client.model.testmanager.apitest.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTagEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
public class PageApiTestCaseQuery extends PageQueryBase {
    private static final long serialVersionUID = 5742844806999825950L;

    /**
     * 产品code
     */
    private String productCode;

    /**
     * 用例状态（多选）
     */
    private List<Integer> enableNumList;

    private Boolean queryEnable;

    public Boolean getQueryEnable() {
        return CollectionUtil.isNotEmpty(this.getEnableNumList())
                && this.getEnableNumList().size() == 1
                && this.getEnableNumList().get(0).equals(ApiCaseEnableEnum.ENABLED.getCode());
    }


    private Boolean queryDraft;

    public Boolean getQueryDraft() {
        return CollectionUtil.isNotEmpty(this.getEnableNumList())
                && this.getEnableNumList().size() == 1
                && this.getEnableNumList().get(0).equals(ApiCaseEnableEnum.DRAFT.getCode());
    }

    /**
     * 接口名称（多选）
     */
    private List<String> apiNameList;

    /**
     * 接口地址（多选）
     */
    private List<String> apiAddressList;

    /**
     * 接口code
     */
    private String apiCode;

    /**
     * 用例类别（多选）
     */
    private List<ApiCaseTypeEnum> typeList;

    public List<ApiCaseTypeEnum> getTypeList() {
        if (CollectionUtil.isEmpty(this.typeList)) {
            this.typeList = Arrays.asList(ApiCaseTypeEnum.API_CASE, ApiCaseTypeEnum.SYSTEM_CASE);
        }
        return this.typeList;
    }

    private List<Integer> typeNumList;

    public List<Integer> getTypeNumList() {
        return CollectionUtil.isNotEmpty(this.getTypeList())
                ? this.getTypeList().stream().map(item -> (item.getCode())).collect(Collectors.toList()) : null;
    }

    /**
     * 最近一次执行结果（多选）
     */
    private List<TestPlanCaseStatusEnum> testResultList;

    /**
     * 用例名(模糊搜索)
     */
    private String caseName;

    /**
     * 查询开始时间(最近一次执行)
     */
    private Date startTime;

    /**
     * 查询结束时间(最近一次执行)
     */
    private Date endTime;

    /**
     * 文档版本
     */
    private List<String> docVersionList;

    /**
     * 标识
     */
    private List<ApiCaseTagEnum> tagList;

    /**
     * 调用类型
     */
    private List<ApiTypeEnum> apiTypeList;
}
