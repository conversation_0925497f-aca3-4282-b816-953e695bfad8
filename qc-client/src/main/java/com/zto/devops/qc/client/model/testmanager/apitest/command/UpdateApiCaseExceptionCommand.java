package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiConfigTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateApiCaseExceptionCommand extends BaseCommand {

    private String key;

    private String value;

    private String asserts;

    private ApiConfigTypeEnum apiConfigType;

    public UpdateApiCaseExceptionCommand(String aggregateId) {
        super(aggregateId);
    }
}
