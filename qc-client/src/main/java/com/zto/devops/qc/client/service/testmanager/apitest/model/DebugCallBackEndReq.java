package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.linkmap.DebugTaskRespTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DebugCallBackEndReq implements Serializable {

    private static final long serialVersionUID = 4327526265159612626L;

    @GatewayModelProperty(description = "任务编号")
    private String taskId;

    @GatewayModelProperty(description = "回调类型")
    private DebugTaskRespTypeEnum type;

    @GatewayModelProperty(description = "异常原因", required = false)
    private String exceptionReason;

    @GatewayModelProperty(description = "信息内容", required = false)
    private String message;

    @GatewayModelProperty(description = "debug请求类型", required = false)
    private String requestType;

}
