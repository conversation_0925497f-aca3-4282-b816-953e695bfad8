package com.zto.devops.qc.client.service.tag.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;

import lombok.Data;

import java.io.Serializable;

@Data
@GatewayModel(description = "模型")
public class RemoveTagReq implements Serializable {

    @ZModelProperty(description = "业务编码: 业务实例 如 缺陷编码等", required = true, sample = "ISS230303008063")
    private String businessCode;

    @ZModelProperty(description = "标签编码", required = true, sample = "111")
    private String code;
}
