package com.zto.devops.qc.client.model.testmanager.coverage.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.coverage.DiffTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CoverageTaskQuery extends PageQueryBase {

    /**
     * 产品code
     */
    private String productCode;

    /**
     * appId
     */
    private String appId;

    /**
     * 任务Id
     */
    private List<String> taskIdList;

    /**
     * 生成状态
     */
    private List<RecordStatusEnum> statusList;

    /**
     * 覆盖率类型
     */
    private List<RecordTypeEnum> recordTypeList;

    /**
     * 差异类型
     */
    private List<DiffTypeEnum> diffTypeList;

    /**
     * 版本code
     */
    private List<String> versionCodeList;

    /**
     * 生成人
     */
    private List<Long> creatorIdList;

    /**
     * 生成时间-开始
     */
    private Date recordCreateStart;

    /**
     * 生成时间-结束
     */
    private Date recordCreateEnd;

    /**
     * 更新时间-开始
     */
    private Date recordModifiedStart;

    /**
     * 更新时间-结束
     */
    private Date recordModifiedEnd;

}
