package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@ZsmpModel(description = "删除场景分组提示响应模型")
@Accessors(chain = true)
public class DeleteSceneModuleTipsResp implements Serializable {

    @ZsmpModelProperty(description = "分组code")
    private String code;

    @ZsmpModelProperty(description = "分组名称")
    private String name;

    @ZsmpModelProperty(description = "子分组数量")
    private Integer childModuleNum;

    @ZsmpModelProperty(description = "场景图数量")
    private Integer sceneNum;

    @ZsmpModelProperty(description = "使用中的场景图数量")
    private Integer publishedSceneNum;

    public void addChildModuleNum(int num) {
        this.childModuleNum += num;
    }

    public void addSceneNum(int num) {
        this.sceneNum += num;
    }

    public void addPublishedSceneNum(int num) {
        this.publishedSceneNum += num;
    }
}
