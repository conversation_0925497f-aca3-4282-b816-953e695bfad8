package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneIndexTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ZsmpModel(description = "造数共享列表查询响应模型")
public class SharedSceneModuleQueryResp implements Serializable {

    @ZsmpModelProperty(description = "造数code/分组code")
    private String code;

    @ZsmpModelProperty(description = "造数name/分组name")
    private String name;

    @ZsmpModelProperty(description = "类型：造数/分组")
    private SceneIndexTypeEnum type;

    @ZsmpModelProperty(description = "父节点code")
    private String parentCode;

    @ZsmpModelProperty(description = "产品code")
    private String productCode;

    @ZsmpModelProperty(description = "是否叶子节点")
    private Boolean isLeaf;

    @ZsmpModelProperty(description = "是否共享")
    private Boolean isCollect;

    @ZsmpModelProperty(description = "子节点集合")
    private List<SharedSceneModuleQueryResp> children;

    public void addChild(SharedSceneModuleQueryResp child) {
        if (null == this.children) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
    }
}
