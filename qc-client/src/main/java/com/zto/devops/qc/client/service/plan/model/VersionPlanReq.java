package com.zto.devops.qc.client.service.plan.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class VersionPlanReq implements Serializable {
    private static final long serialVersionUID = 339351983119635422L;

    @ZModelProperty(description = "版本code", required = true, sample = "VER2302168133")
    private String versionCode;
}
