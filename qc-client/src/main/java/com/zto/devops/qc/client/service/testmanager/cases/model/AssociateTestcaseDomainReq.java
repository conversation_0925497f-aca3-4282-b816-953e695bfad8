package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.enums.DomainEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "用例关联业务请求模型")
public class AssociateTestcaseDomainReq implements Serializable {

    @ZModelProperty(description = "用例code", required = true, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "领域 ISSUE REQUIREMENT", required = true, sample = "ISSUE")
    private DomainEnum domain;

    @ZModelProperty(description = "关联业务code", required = true, sample = "['TC231130064892']")
    private List<String> codeList;

    @ZModelProperty(description = "计划用例操作日志code", required = false, sample = "SNF974191934769725441")
    private String operateCaseCode;
}
