package com.zto.devops.qc.client.model.testmanager.coverage.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.coverage.RecordTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class GenerateCoverageEvent extends BaseEvent implements ActionEvent {

    @GatewayModelProperty(description = "产品编号")
    private String productCode;

    @GatewayModelProperty(description = "版本编码")
    private String code;

    @GatewayModelProperty(description = "appId")
    private String appId;

    @GatewayModelProperty(description = "报告类型")
    private RecordTypeEnum recordType;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "版本名称")
    private String versionName;

    @Override
    public String action() {
        return "单个应用生成覆盖率报告";
    }

    @Override
    public User transactor() {
        return this.getTransactor();
    }

    @Override
    public Date occurred() {
        return this.getOccurred();
    }

    @Override
    public String makeString() {
        return null;
    }

}
