package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "单接口调试请求模型")
public class ApiDebugReq implements Serializable {

    @ZModelProperty(description = "接口用例code", required = true, sample = "TC231130064892")
    private String caseCode;

    @ZModelProperty(description = "异常用例code列表")
    private List<String> exceptionCodeList;

    @ZModelProperty(description = "执行环境")
    private String ztoenv;

    @ZModelProperty(description = "执行环境")
    private String envName;
}
