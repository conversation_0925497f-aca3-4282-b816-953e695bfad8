package com.zto.devops.qc.client.service.testmanager.apitest.model;


import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAttributeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ListExecuteDetailResp implements Serializable {

    @ZsmpModelProperty(description = "code")
    private String code;

    @ZsmpModelProperty(description = "自动化任务code")
    private String automaticTaskCode;

    @ZsmpModelProperty(description = "名称")
    private String name;

    @ZsmpModelProperty(description = "属性")
    private TestcaseAttributeEnum attribute;

    @ZsmpModelProperty(description = "上级code")
    private String parentCode;

    @ZsmpModelProperty(description = "排序")
    private Integer sort;

    @ZsmpModelProperty(description = "已启用/删除")
    private Boolean enable;

    @ZsmpModelProperty(description = "结果")
    private TestPlanCaseStatusEnum result;

    @ZsmpModelProperty(description = "结果描述")
    private String resultDesc;

    @ZsmpModelProperty(description = "执行人id")
    private Long executorId;

    @ZsmpModelProperty(description = "执行人")
    private String executor;

    @ZsmpModelProperty(description = "开始时间")
    private Date startTime;

    @ZsmpModelProperty(description = "结束时间")
    private Date finishTime;

    @ZsmpModelProperty(description = "报告")
    private String reportFile;

    @ZsmpModelProperty(description = "执行日志")
    private String execLogFile;

    @ZsmpModelProperty(description = "用例数量")
    private Integer testcaseCount;

    @ZsmpModelProperty(description = "子节点")
    private List<ListExecuteDetailResp> children;

    @ZsmpModelProperty(description = "api接口code")
    private String apiCode;

    public void setResult(TestPlanCaseStatusEnum result) {
        this.result = result;
        if (null != result) {
            this.resultDesc = result.getValue();
        }
    }

}
