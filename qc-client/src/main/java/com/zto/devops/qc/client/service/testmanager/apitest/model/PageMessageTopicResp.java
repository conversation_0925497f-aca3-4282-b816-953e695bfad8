package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "分页查询消息主题响应参数")
public class PageMessageTopicResp implements Serializable {

    @ZsmpModelProperty(description = "主题名称")
    private String name;

    @ZsmpModelProperty(description = "场景描述")
    private String memo;

    @ZsmpModelProperty(description = "产品名称")
    private String productName;

    @ZsmpModelProperty(description = "应用id")
    private String appId;

    @ZsmpModelProperty(description = "zbase产品code")
    private String zbaseProductCode;
}
