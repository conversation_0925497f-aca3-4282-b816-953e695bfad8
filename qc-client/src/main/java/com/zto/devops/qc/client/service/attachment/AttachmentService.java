package com.zto.devops.qc.client.service.attachment;

import com.zto.devops.qc.client.service.attachment.model.AddAttachmentReq;
import com.zto.devops.qc.client.service.attachment.model.AttachmentResp;
import com.zto.devops.qc.client.service.attachment.model.ListAttachmentsByBusinessCodeReq;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.service.attachment.model.RemoveAttachmentReq;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AttachmentService {

    /**
     * 根据业务编码查询附件列表
     */
    Result<List<AttachmentResp>> ListAttachmentsByBusinessCode(ListAttachmentsByBusinessCodeReq req);

    /**
     * 添加附件
     */
    Result<Void> addAttachment(AddAttachmentReq req);

    /**
     * 移除附件
     */
    Result<Void> removeAttachment(RemoveAttachmentReq req);

}
