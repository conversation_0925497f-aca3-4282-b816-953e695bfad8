package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZModel(description = "复制用例req")
public class CopyApiTestCaseReq implements Serializable {

    @ZModelProperty(description = "接口用例code", required = true, sample = "TC231130064892")
    private String caseCode;

    @ZModelProperty(description = "用例名称", required = true, sample = "用例名称")
    private String caseName;

}
