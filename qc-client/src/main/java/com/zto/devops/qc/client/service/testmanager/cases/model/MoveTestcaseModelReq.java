package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "移动手工用例请求模型")
@Data
public class MoveTestcaseModelReq implements Serializable {
    private static final long serialVersionUID = 308022528094936273L;

    @ZModelProperty(description = "唯一标识", required = true, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "所属模块编码，不传为未分组用例", required = false, sample = "TC231130064892")
    private String parentCode;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;
}
