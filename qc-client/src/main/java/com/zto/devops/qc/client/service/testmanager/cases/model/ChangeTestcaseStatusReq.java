package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseAbandonReasonEnum;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseStatusEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "变更手工用例状态请求模型")
@Data
public class ChangeTestcaseStatusReq implements Serializable {

    @ZModelProperty(description = "用例唯一标识", required = true, sample = "TC231130064892")
    private String code;

    @ZModelProperty(description = "状态 NORMAL DISABLE", required = true, sample = "NORMAL")
    private TestcaseStatusEnum status;

    @ZModelProperty(description = "停用原因 状态为停用时必填 OUT_OF_USE EXPIRED OTHER", sample = "OUT_OF_USE")
    private TestcaseAbandonReasonEnum abandonReason;

}
