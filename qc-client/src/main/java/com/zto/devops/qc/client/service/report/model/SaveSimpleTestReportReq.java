package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.UiTestResultEnum;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.issue.entity.AttachmentVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SaveSimpleTestReportReq extends SaveBasicInfoReq implements Serializable {

    @ZModelProperty(description = "zui测试结果", required = false, sample = "PASS")
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "开发人数", required = false, sample = "1")
    private Integer developerCount;

    @ZModelProperty(description = "测试人数", required = false, sample = "1")
    private Integer testerCount;

    @ZModelProperty(description = "是否按计划范围上线", required = false, sample = "1")
    private Integer asPlanedOnline;

    @ZModelProperty(description = "附件", required = false, sample = "[]")
    private List<AttachmentVO> attachments;

    @ZModelProperty(description = "总结、分析、描述", required = false, sample = "总结")
    private String summary;

    @ZModelProperty(description = "计划提测时间-上下午", required = false, sample = "上午")
    private String planPresentationDay;

    @ZModelProperty(description = "实际提测时间", required = false, sample = "1711987200000")
    private Date actualPresentationDate;

    @ZModelProperty(description = "实际提测时间上午、下午", required = false,sample = "上午")
    private String actualPresentationDay;

    @ZModelProperty(description = "计划准出时间--上下午", required = false, sample = "上午")
    private String planApprovalExitDay;

    @ZModelProperty(description = "实际准出时间", required = false, sample = "1711987200000")
    private Date actualApprovalExitDate;

    @ZModelProperty(description = "实际准出时间上午、下午", required = false, sample = "上午")
    private String actualApprovalExitDay;

    @ZModelProperty(description = "实际上线时间", required = false, sample = "1711987200000")
    private Date actualOnlineDate;

    @ZModelProperty(description = "是否延期", required = false, sample = "-1")
    private Integer delay;

    @ZModelProperty(description = "总体测试结果", required = false, sample = "通过")
    private String testResult;

    @ZModelProperty(description = "ui测试结果", required = false, sample = "NO_PASS")
    private UiTestResultEnum uiTestResult;
}
