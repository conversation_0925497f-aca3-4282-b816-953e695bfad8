package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ZsmpModel(description = "用例模块树响应模型")
@Data
public class ListTestcaseModuleResp implements Serializable {
    private static final long serialVersionUID = -7567087754689711300L;

    @ZsmpModelProperty(description = "code")
    private String code;

    @ZsmpModelProperty(description = "名称")
    private String name;

    @ZsmpModelProperty(description = "产品code")
    private String productCode;

    @ZsmpModelProperty(description = "父级code")
    private String parentCode;

    @ZsmpModelProperty(description = "类型")
    private TestcaseTypeEnum type;

    @ZsmpModelProperty(description = "状态")
    private TestcaseStatusEnum status;

    @ZsmpModelProperty(description = "自动化登记库code")
    private String automaticSourceCode;

    @ZsmpModelProperty(description = "节点类型")
    private AutomaticNodeTypeEnum nodeType;

    @ZsmpModelProperty(description = "排序")
    private Integer sort;

    @ZsmpModelProperty(description = "目录")
    private String path;

    @ZsmpModelProperty(description = "层级")
    private Integer layer;

    @ZsmpModelProperty(description = "用例数量")
    private int testcaseCount;

    @ZsmpModelProperty(description = "自动化登记库状态")
    private AutomaticStatusEnum automaticSourceStatus;

    @ZsmpModelProperty(description = "属性")
    private TestcaseAttributeEnum attribute;

    @ZsmpModelProperty(description = "自动化登记库备注")
    private String comment;

    @ZsmpModelProperty(description = "自动化登记库框架类型")
    private AutomaticRecordTypeEnum automaticSourceType;

    @ZsmpModelProperty(description = "子节点")
    private List<ListTestcaseModuleResp> children;

    @ZsmpModelProperty(description = "是否关联场景")
    private Boolean sceneFlag;
}
