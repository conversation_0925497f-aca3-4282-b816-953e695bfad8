package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseSourceTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@GatewayModel(description = "分页查询api用例Req")
@Data
public class PageApiCaseReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = -8618563274573925622L;

    @GatewayModelProperty(description = "用例状态（多选）", required = false)
    private List<Integer> enableNumList;

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "接口名称（多选）", required = false)
    private List<String> apiNameList;

    @GatewayModelProperty(description = "接口地址（多选）", required = false)
    private List<String> apiAddressList;

    @GatewayModelProperty(description = "接口code", required = false)
    private String apiCode;

    @GatewayModelProperty(description = "用例类别（多选）", required = false)
    private List<ApiCaseSourceTypeEnum> typeList;

    @GatewayModelProperty(description = "最近一次执行结果（多选）", required = false)
    private List<TestPlanCaseStatusEnum> testResultList;

    @GatewayModelProperty(description = "用例名(模糊搜索)", required = false)
    private String caseName;

    @GatewayModelProperty(description = "查询开始时间(最近一次执行)", required = false)
    private Date startTime;

    @GatewayModelProperty(description = "查询结束时间(最近一次执行)", required = false)
    private Date endTime;

    @GatewayModelProperty(description = "文档版本", required = false)
    private List<String> docVersionList;
}
