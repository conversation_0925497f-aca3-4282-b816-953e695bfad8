package com.zto.devops.qc.client.model.testmanager.apitest.command;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EditPreDataInfoCommand extends EditSceneInfoCommand {
    private static final long serialVersionUID = -2212335237944417435L;

    private String productCode;

    private String sceneName;

    private String sceneInfoDesc;

    private String sceneFrontData;

    private String sceneBackData;

    private List<Integer> dbIds;

    private String parentCode;

    private String sceneType;

    public EditPreDataInfoCommand(String aggregateId) {
        super(aggregateId);
    }
}
