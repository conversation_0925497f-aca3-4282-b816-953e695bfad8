package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@GatewayModel(description = "分页查询api用例VO")
@Data
public class PageApiCaseChildrenVO implements Serializable {
    private static final long serialVersionUID = 3712246694781599727L;

    @GatewayModelProperty(description = "总数")
    private Long total;

    @GatewayModelProperty(description = "用例数据")
    private List<ApiCaseChildVO> list;

    public static PageApiCaseChildrenVO buildSelf(List<ApiCaseChildVO> doList, Long total) {
        PageApiCaseChildrenVO result = new PageApiCaseChildrenVO();
        result.setList(CollectionUtil.isEmpty(doList) ? new ArrayList<>() : doList);
        result.setTotal(CollectionUtil.isEmpty(doList) ? 0l : total);
        return result;
    }
}
