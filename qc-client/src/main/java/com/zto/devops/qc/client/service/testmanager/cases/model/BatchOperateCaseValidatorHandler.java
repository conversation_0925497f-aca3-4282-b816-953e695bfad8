package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.baidu.unbiz.fluentvalidator.ValidationError;
import com.baidu.unbiz.fluentvalidator.Validator;
import com.baidu.unbiz.fluentvalidator.ValidatorContext;
import com.baidu.unbiz.fluentvalidator.ValidatorHandler;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.model.testmanager.cases.entity.BatchOperateCaseVO;

public class BatchOperateCaseValidatorHandler extends ValidatorHandler<BatchOperateCaseVO> implements Validator<BatchOperateCaseVO> {

    @Override
    public boolean validate(ValidatorContext context, BatchOperateCaseVO batchOperateCaseVO) {
        if (StringUtil.isEmpty(batchOperateCaseVO.getCode())) {
            context.addError(
                    ValidationError.create("code不能为空！")
                            .setField("code")
                            .setInvalidValue(batchOperateCaseVO.getCode()));
            return false;
        }
        return true;
    }
}
