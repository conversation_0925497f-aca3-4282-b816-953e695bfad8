package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.zto.titans.common.util.StringUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目名称：qc-parent
 * 类 名 称：PlanMobile
 * 类 描 述：TODO
 * 创建时间：2021/11/24 3:11 下午
 * 创 建 人：bulecat
 *
 * <AUTHOR>
 */
@Data
public class TestCaseExcelColumn implements Serializable, ExcelModel {

    @ColumnWidth(25)
    @ExcelProperty("用例分组")
    private String parentFullName;

    @ColumnWidth(50)
    @ExcelProperty("用例名称")
    private String name;

//    @ExcelProperty("类型描述")
//    private String typeDesc;

    @ColumnWidth(25)
    @ExcelProperty("用例等级")
    private String priorityDesc;

    @ColumnWidth(25)
    @ExcelProperty("用例标签")
    private String tagName;

    @ColumnWidth(50)
    @ExcelProperty("前置条件")
    private String precondition;

    @ColumnWidth(50)
    @ExcelProperty("测试步骤")
    private String stepDesc;

    @ColumnWidth(50)
    @ExcelProperty("预期结果")
    private String expectResult;

//    @ExcelProperty("责任人编码")
//    private Long dutyUserId;

    @ColumnWidth(25)
    @ExcelProperty("责任人姓名")
    private String dutyUser;

    @ColumnWidth(25)
    @ExcelProperty("责任人工号")
    private String dutyUserBizCode;

    @ColumnWidth(50)
    @ExcelProperty("备注")
    private String comment;

    @ColumnWidth(50)
    @ExcelProperty("失败原因")
    private String checkFailReason;

    @Override
    public boolean isBeanNone() {
        return StringUtil.isBlank(this.name) && StringUtil.isBlank(this.parentFullName) && StringUtil.isBlank(this.priorityDesc) && StringUtil.isBlank(this.precondition)
                && StringUtil.isBlank(this.stepDesc) && StringUtil.isBlank(this.expectResult) && StringUtil.isBlank(this.comment)
                && StringUtil.isBlank(this.dutyUserBizCode) && StringUtil.isBlank(this.dutyUser) && StringUtil.isBlank(this.tagName);
    }

    public void buildExcelData() {
        if(StringUtil.isNotBlank(this.parentFullName)) {
            this.parentFullName = this.parentFullName.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.name)) {
            this.name = this.name.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.priorityDesc)) {
            this.priorityDesc = this.priorityDesc.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.tagName)) {
            this.tagName = this.tagName.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.precondition)) {
            this.precondition = this.precondition.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.stepDesc)) {
            this.stepDesc = this.stepDesc.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.expectResult)) {
            this.expectResult = this.expectResult.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.dutyUser)) {
            this.dutyUser = this.dutyUser.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.dutyUserBizCode)) {
            this.dutyUserBizCode = this.dutyUserBizCode.replaceAll("_x000D_", "");
        }
        if(StringUtil.isNotBlank(this.comment)) {
            this.comment = this.comment.replaceAll("_x000D_", "");
        }
    }
}
