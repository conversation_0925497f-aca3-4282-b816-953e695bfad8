package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.testmanager.report.entity.CaseExecuteResultVO;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AddOnlineSmokeTestReportReq extends BasicInfoReq implements Serializable {

    @ZModelProperty(description = "zui测试结果", required = false, sample = "PASS")
    private ZUITestResultEnum zuiTestResult;

    @ZModelProperty(description = "实际上线时间", required = false, sample = "1711987200000")
    private Date actualPublishDate;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false, sample = "-1")
    private Integer delay;

    @ZModelProperty(description = "延期 -1 否，1 是", required = false, sample = "-1")
    private String delayDesc;

    @ZModelProperty(description = "是否按计划范围上线", required = false, sample = "1")
    private Integer asPlanedOnline;

    @ZModelProperty(description = "测试信息", required = true, sample = "{}")
    private CaseExecuteResultVO caseExecuteResultVO;
}
