package com.zto.devops.qc.client.model.testmanager.coverage.entity;

import java.io.Serializable;

import com.zto.devops.framework.client.entity.BaseModel;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.qc.client.enums.rpc.FlowLaneTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.coverage.*;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @create 2022/10/08 10:31
 */
@Data
public class CoverageRecordBasicVO extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;


    public CoverageRecordBasicVO() {

    }

    public CoverageRecordBasicVO(String appId, String versionCode, String versionName,
                                 String branchName, RecordTypeEnum recordType,
                                 RecordStatusEnum status, Boolean enable,
                                 AppTypeEnum appType) {
        this.appId = appId;
        this.versionCode = versionCode;
        this.versionName = versionName;
        this.branchName = branchName;
        this.recordType = recordType;
        this.status = status;
        this.appType = appType;
        super.setEnable(enable);
    }

    public CoverageRecordBasicVO(String appId, String appName, Long deptId, String deptName,
                                 String productCode, String productName,
                                 String versionCode, String versionName,
                                 String branchName, RecordTypeEnum recordType,
                                 RecordStatusEnum status, Boolean isWhiteList,
                                 String testStrategy, AppTypeEnum appType,
                                 BigDecimal standardRate) {
        this.appId = appId;
        this.appName = appName;
        this.deptId = deptId;
        this.deptName = deptName;
        this.productCode = productCode;
        this.productName = productName;
        this.versionCode = versionCode;
        this.versionName = versionName;
        this.branchName = branchName;
        this.isWhiteList = isWhiteList;
        this.testStrategy = testStrategy;
        this.status = status;
        this.recordType = recordType;
        this.appType = appType;
        this.standardRate = standardRate;
    }

    private Long id;

    /**
     * 部门编码
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * appId
     */
    private String appId;

    /**
     * appName
     */
    private String appName;

    /**
     * 报告类型(FEATURE-开发,BRANCH-分支,MASTER-主干)
     */
    private RecordTypeEnum recordType;

    /**
     * 生成类型(AUTO-自动生成,MANUAL-手动触发)
     */
    private GenerateTypeEnum generateType;

    /**
     * 标准值
     */
    private BigDecimal standardRate;

    /**
     * 覆盖率
     */
    private BigDecimal recordRate;

    /**
     * 生成状态(INITIAL-待生成，RUNNING-生成中，SUCCEED-成功，FAIL-失败，NEEDLESS-无需生成)
     */
    private RecordStatusEnum status;

    /**
     * 报告或错误日志地址
     */
    private String recordUrl;

    /**
     * 不达标原因
     */
    private String remark;

    /**
     * 覆盖率报告异常原因
     */
    private String recordErrorMsg;

    /**
     * 测试策略
     */
    private String testStrategy;

    /**
     * 是否白名单，0-否，1-是
     */
    private Boolean isWhiteList;

    /**
     * 分支名称
     */
    private String branchName;

    /**
     * git提交id
     */
    private String commitId;

    /**
     * 基准分支名称
     */
    private String basicBranchName;

    /**
     * 基准commitId
     */
    private String basicCommitId;

    /**
     * 应用类型，JAVA-java应用，WEB-前端应用，GOLANG-go应用
     */
    private AppTypeEnum appType;

    /**
     * 差异类型
     */
    private DiffTypeEnum diffType;

    /**
     * 空间名称
     */
    private String envName;

    /**
     * 任务编号
     */
    private String taskId;

    /**
     * 备注
     */
    private String comment;

    /**
     * 发布泳道
     */
    private FlowLaneTypeEnum flowLaneType;

    private String bucketName;

    private String fileName;

    public void addBasicVORemark(String remark) {
        List<String> reasonList = new ArrayList<>();
        String customReason = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(this.remark)) {
            String regular = "(?<=[1-9]\\.\\s|[1-9][0-9]\\.\\s)([\\d\\D]*?)(?=\\$)";
            Pattern pattern = Pattern.compile(regular);
            Matcher m = pattern.matcher(this.remark);
            while (m.find()) {
                String content = m.group(1);
                if (StringUtils.isNotBlank(content)) {
                    if (content.contains("自定义：")) {
                        customReason = content.substring(4);
                    } else {
                        reasonList.add(content);
                    }
                }
            }
            // 系统自动回填的原因，没有序号
            if (CollectionUtils.isEmpty(reasonList) && StringUtils.isBlank(customReason)) {
                reasonList.add(this.remark);
            }
        }
        if (!reasonList.contains(remark)) {
            reasonList.add(remark);
        }
        this.remark = getCoverageRemark(reasonList, customReason);
    }

    public String getCoverageRemark(List<String> reasonList, String customReason) {
        String remark = Strings.EMPTY;
        AtomicInteger index = new AtomicInteger(1);
        if (CollectionUtil.isNotEmpty(reasonList)) {
            for (String reason : reasonList) {
                if (StringUtil.isNotBlank(reason)) {
                    remark += index + ". " + reason + "$";
                    index.getAndIncrement();
                }
            }
        }
        if (StringUtil.isNotEmpty(customReason)) {
            remark += index.get() + ". 自定义：" + customReason + "$";
        }
        return remark;
    }

}
