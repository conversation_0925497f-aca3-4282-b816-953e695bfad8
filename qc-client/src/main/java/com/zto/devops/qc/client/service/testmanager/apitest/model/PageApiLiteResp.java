package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZModel(description = "查询接口列表（场景图列表筛选）响应模型")
public class PageApiLiteResp implements Serializable {

    @ZModelProperty(description = "接口名称")
    private String apiName;

    @ZModelProperty(description = "请求地址")
    private String apiAddress;

    @ZModelProperty(description = "应用id")
    private String appId;

    @ZModelProperty(description = "一站式产品code")
    private String productCode;

    @ZModelProperty(description = "接口类型")
    private ApiTypeEnum apiType;
}
