package com.zto.devops.qc.client.model.testmanager.report.entity;

import com.zto.devops.qc.client.enums.testmanager.report.ReportType;
import com.zto.devops.qc.client.enums.testmanager.report.ZUITestResultEnum;
import com.zto.devops.qc.client.model.testmanager.report.query.PermitReportDetailQuery;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TmPermitReportDetailVO extends BaseDetailVO {

    @GatewayModelProperty(description = "是否包含zui应用（true：包含；false：不包含）")
    private Boolean zuiFlag;

    @GatewayModelProperty(description = "zui测试结果", required = false)
    private ZUITestResultEnum zuiTestResult;

    public ZUITestResultEnum getZuiTestResult() {
        if (null == this.zuiFlag || !zuiFlag) {
            return null;
        }
        return (null == this.zuiTestResult || ZUITestResultEnum.UNKNOWN.equals(this.zuiTestResult))
                ? ZUITestResultEnum.PASS
                : this.zuiTestResult;
    }


    @GatewayModelProperty(description = "功能用例模块测试结果")
    private List<TmModuleTestVO> moduleTestVOS;

    @GatewayModelProperty(description = "计划准出时间", required = false)
    private Date approvalExitDate;

    @GatewayModelProperty(description = "实际准出时间", required = false)
    private Date actualApprovalExitDate;

    @GatewayModelProperty(description = "准出延期天数", required = false)
    private Integer delayDays;

    @GatewayModelProperty(description = "安全计划code", required = false)
    private String safePlanCode;

    public void buildSelf(PermitReportDetailQuery query) {
        this.setReportType(ReportType.TEST_PERMIT);
        this.setReportUserId(query.getTransactor().getUserId());
        this.setReportUserName(query.getTransactor().getUserName());
    }
}
