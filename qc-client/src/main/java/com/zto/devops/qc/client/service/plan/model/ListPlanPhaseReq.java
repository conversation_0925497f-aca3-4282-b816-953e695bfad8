package com.zto.devops.qc.client.service.plan.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.cases.TestcaseTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@ZModel(description = "查询测试计划与阶段Req")
public class ListPlanPhaseReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 4636122251480603622L;

    @NotEmpty(message = "产品Code")
    @ZModelProperty(description = "产品Code", required = true, sample = "399")
    private String productCode;

    @NotEmpty(message = "用例类型不为空")
    @ZModelProperty(description = "用例类型", required = true, sample = "AUTO")
    private TestcaseTypeEnum caseType;

    @ZModelProperty(description = "版本Code", sample = "VER2310138053")
    private String versionCode;

    @ZModelProperty(description = "测试计划名", sample = "测试计划名")
    private String planName;

}
