package com.zto.devops.qc.client.service.report.model;

import com.zto.devops.qc.client.enums.testmanager.report.TmTestResultEnum;
import com.zto.devops.qc.client.model.testmanager.report.entity.TmModuleTestVO;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class EditMobileTestReportReq extends SaveBasicInfoReq implements Serializable {

    @ZModelProperty(description = "实际测试开始时间", required = false, sample = "1711987200000")
    private Date actualTestStart;

    @ZModelProperty(description = "实际测试开始时间--上下午", required = false, sample = "上午")
    private String actualTestStartDay;

    @ZModelProperty(description = "实际测试结束时间", required = false, sample = "1711987200000")
    private Date actualTestEnd;

    @ZModelProperty(description = "实际测试结束时间-- 上下午", required = false, sample = "上午")
    private String actualTestEndDay;

    @ZModelProperty(description = "总体测试结果", required = false, sample = "ONCE_SUCCESS")
    private TmTestResultEnum testResult;

    @ZModelProperty(description = "模块测试结果", required = false, sample = "[]")
    private List<TmModuleTestVO> moduleTestVOS;

    @ZModelProperty(description = "总结、分析、描述", required = false, sample = "总结")
    private String summary;
}
