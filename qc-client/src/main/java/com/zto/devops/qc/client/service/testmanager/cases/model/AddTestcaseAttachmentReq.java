package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.qc.client.enums.issue.AttachmentFileTypeEnum;
import com.zto.devops.qc.client.enums.issue.AttachmentTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "用例上传附件模型")
public class AddTestcaseAttachmentReq implements Serializable {

    @ZModelProperty(description = "编码", required = true, sample = "1")
    private String businessCode;

    @ZModelProperty(description = "附件路径", required = false, sample = "1")
    private String url;

    @ZModelProperty(description = "私有组文件名", required = true, sample = "1")
    private String remoteFileId;

    @ZModelProperty(description = "附件名称", required = true, sample = "附件名称")
    private String name;

    @ZModelProperty(description = "附件类型 FILE 文件, URL 链接", required = true, sample = "FILE")
    private AttachmentTypeEnum type;

    @ZModelProperty(description = "文件类型 zip 文件, jpg 链接", required = true, sample = "EXCEL")
    private AttachmentFileTypeEnum fileType;

    @ZModelProperty(description = "文件大小", required = true, sample = "11")
    private String size;

    @ZModelProperty(description = "计划用例操作日志code", required = false, sample = "SNF974191934769725441")
    private String operateCaseCode;
}
