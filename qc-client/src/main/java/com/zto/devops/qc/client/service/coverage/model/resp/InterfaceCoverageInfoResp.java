package com.zto.devops.qc.client.service.coverage.model.resp;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "接口覆盖率信息响应模型")
public class InterfaceCoverageInfoResp  implements Serializable {
    private static final long serialVersionUID = -5515287566758699518L;

    @ZsmpModelProperty(description = "接口名称")
    private String interfaceName;

    @ZsmpModelProperty(description = "接口文档地址")
    private String interfaceDocAddress;

    @ZsmpModelProperty(description = "状态")
    private Integer status;

    @ZsmpModelProperty(description = "接口类型")
    private String interfaceType;

    @ZsmpModelProperty(description = "接口昨日线上调用数")
    private Integer interfaceCallNumber;

    @ZsmpModelProperty(description = "接口昨日线上错误数")
    private Integer interfaceErrorNumber;

}
