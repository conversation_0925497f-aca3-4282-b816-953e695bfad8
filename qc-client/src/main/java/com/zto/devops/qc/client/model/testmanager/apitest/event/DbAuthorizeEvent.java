package com.zto.devops.qc.client.model.testmanager.apitest.event;

import com.zto.devops.framework.client.event.ActionEvent;
import com.zto.devops.framework.client.event.BaseEvent;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeDbVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeProductVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class DbAuthorizeEvent extends BaseEvent implements ActionEvent {

    private String productCode;

    private List<ApiTestAuthorizeProductVO> authorizeProductList;

    private List<ApiTestAuthorizeDbVO> authorizeDbList;

    @Override
    public String action() {
        return "新增数据库授权";
    }

    @Override
    public User transactor() {
        return null;
    }

    @Override
    public Date occurred() {
        return null;
    }

    @Override
    public String makeString() {
        return null;
    }
}
