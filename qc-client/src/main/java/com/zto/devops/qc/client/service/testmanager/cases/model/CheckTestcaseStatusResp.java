package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "校验用例状态响应模型")
public class CheckTestcaseStatusResp implements Serializable {

    @ZsmpModelProperty(description = "总数")
    private Integer total;

    @ZsmpModelProperty(description = "正常数")
    private Integer normal;

    @ZsmpModelProperty(description = "已停用数")
    private Integer disable;

    @ZsmpModelProperty(description = "草稿用例数")
    private Integer edit;
}
