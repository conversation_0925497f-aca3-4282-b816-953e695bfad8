package com.zto.devops.qc.client.service.tag;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.model.issue.entity.TagVO;
import com.zto.devops.qc.client.service.tag.model.AddTagReq;
import com.zto.devops.qc.client.service.tag.model.ListTagsByBusinessCodeReq;
import com.zto.devops.qc.client.service.tag.model.RemoveTagReq;

import java.util.List;

public interface ITagService {

    Result<List<TagVO>> listTagsByBusinessCode(ListTagsByBusinessCodeReq req);

    Result<Void> addTag(AddTagReq req);

    Result<Void> removeTag(RemoveTagReq req);

    Result<Void> deleteTag(String code);
}
