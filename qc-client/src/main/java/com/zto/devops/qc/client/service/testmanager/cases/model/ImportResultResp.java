package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * 项目名称：qc-parent
 * 类 名 称：PlanMobile
 * 类 描 述：TODO
 * 创建时间：2021/11/24 3:11 下午
 * 创 建 人：bulecat
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImportResultResp implements Serializable {

    @GatewayModelProperty(description = "总数量", required = false)
    private Integer totalNum;

    @GatewayModelProperty(description = "成功数量", required = false)
    private Integer successNum;

    @GatewayModelProperty(description = "失败数量", required = false)
    private Integer failNum;

    @GatewayModelProperty(description = "失败文件地址", required = false)
    private String failFileUrl;

    @GatewayModelProperty(description = "失败文件名", required = false)
    private String failFileName;

    @GatewayModelProperty(description = "报错信息", required = false)
    private String errorMsg;

    @GatewayModelProperty(description = "是否导入成功", required = false)
    private Boolean importFlag;

    public  ImportResultResp(boolean importFlag){
        this.importFlag = importFlag;
    }

    public static ImportResultResp buildErrorResult(ImportDataResp importDataResp) {
        if (Objects.isNull(importDataResp)) {
            return ImportResultResp.builder().errorMsg("导入失败").importFlag(false).build();
        }
        return ImportResultResp.builder().errorMsg(importDataResp.getErrorMsg()).importFlag(false).build();
    }
}
