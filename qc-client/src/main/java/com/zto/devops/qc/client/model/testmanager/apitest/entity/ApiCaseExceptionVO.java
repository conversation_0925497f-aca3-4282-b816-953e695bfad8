package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.alibaba.fastjson.JSON;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiConfigTypeEnum;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class ApiCaseExceptionVO implements Serializable {
    private static final long serialVersionUID = -8519899560828467650L;

    @ZModelProperty(description = "用例code")
    private String caseCode;

    @ZModelProperty(description = "变量名")
    private String key;

    @ZModelProperty(description = "变量值")
    private String value;

    @ZModelProperty(description = "断言")
    private String asserts;

    @ZModelProperty(description = "接口配置类型")
    private ApiConfigTypeEnum apiConfigType;

    @ZModelProperty(description = "caseReqData", hidden = true)
    private String caseReqData;

    @ZModelProperty(description = "标签")
    private String label;

    public void setCaseReqData(String caseReqData) {
        if (StringUtils.isNotBlank(caseReqData)) {
            ApiCaseExceptionVO vo = JSON.parseObject(caseReqData, ApiCaseExceptionVO.class);
            if (null != vo) {
                this.key = vo.getKey();
                this.value = vo.getValue();
                this.asserts = vo.getAsserts();
                this.apiConfigType = vo.getApiConfigType();
                this.label = vo.getLabel();
            }
        }
    }
}
