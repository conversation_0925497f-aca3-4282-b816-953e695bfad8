package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryAuthorizeListVO implements Serializable {

    private static final long serialVersionUID = 7285206184897683972L;

    @ZsmpModelProperty(description = "已授权产品code")
    private String authorizeProductCode;

    @ZsmpModelProperty(description = "已授权产品名称")
    private String authorizeProductName;

    @ZsmpModelProperty(description = "授权数据库")
    private String databaseNames;

}
