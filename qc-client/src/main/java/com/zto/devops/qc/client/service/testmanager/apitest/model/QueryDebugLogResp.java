package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSON;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.DebugLogVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ZsmpModel(description = "查询调试日志响应模型")
public class QueryDebugLogResp implements Serializable {
    private static final long serialVersionUID = 2585703645744919242L;

    @ZsmpModelProperty(description = "调试日志")
    private String debugLog;

    @ZsmpModelProperty(description = "本次任务最终状态", required = true)
    private String status;

    @ZsmpModelProperty(description = "是否调试历史")
    private Boolean historyFlag = true;

    public static QueryDebugLogResp buildSelf(String logStr) {
        DebugLogVO logVO = JSON.parseObject(logStr, DebugLogVO.class);
        if (Objects.isNull(logVO)) {
            return new QueryDebugLogResp();
        }
        return QueryDebugLogResp.builder()
                .status(logVO.getStatus())
                .debugLog(logVO.getDebugLog())
                .build();
    }
}
