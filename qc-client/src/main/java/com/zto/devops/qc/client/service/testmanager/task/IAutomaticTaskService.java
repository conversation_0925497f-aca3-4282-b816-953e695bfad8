package com.zto.devops.qc.client.service.testmanager.task;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.service.testmanager.cases.model.*;

public interface IAutomaticTaskService {

    /**
     * 分页查询自动化任务执行记录(按父任务分组)
     *
     * @param req {@link PageAutomaticTaskGroupReq req}
     * @return {@link AutomaticTaskGroupResp}
     */
    PageResult<AutomaticTaskGroupResp> pageAutomaticTaskGroup(PageAutomaticTaskGroupReq req);

    /**
     * 查询自动化执行空间，供前端下拉筛选执行记录
     *
     * @param req {@link ListExecuteEnvReq}
     * @return {@link ListExecuteEnvResp}
     */
    Result<ListExecuteEnvResp> listExecuteEnv(ListExecuteEnvReq req);

    Result<Void> execute(ExecuteAutomaticTaskReq req);

    Result<Void> executeCallback(ExecuteCallbackReq req);

    Result<Void> terminate(String code);

    /**
     * 批量终止自动化任务
     *
     * @param req
     * @return
     */
    Result<Void> batchTerminate(BatchTerminateTaskReq req);

    /**
     * 终止流水线自动化任务
     *
     * @param taskId
     * @param user
     */
    void terminatePipelineTask(String taskId, User user);

    /**
     * 自动化用例执行结果回调
     *
     * @param req
     * @return
     */
    Result<Void> tcExecuteResultCallback(ExecuteCallbackReq req);

    Result<Void> executeAutoTestCronTask();

    Result<Void> retryTask(RetryAutomaticTaskReq req);
}
