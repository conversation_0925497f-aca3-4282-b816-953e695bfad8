package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.VariableTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BatchAddPreDataVariableReq implements Serializable {
    private static final long serialVersionUID = -3434519810370830229L;

    @GatewayModelProperty(description = "所属产品code")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @GatewayModelProperty(description = "所属产品名称")
    private String productName;

    @GatewayModelProperty(description = "入参变量集合")
    private List<BasePreDataVariableReq> inputParameter;

    @GatewayModelProperty(description = "出参变量集合")
    private List<BasePreDataVariableReq> outputParameter;

    @GatewayModelProperty(description = "类型")
    private VariableTypeEnum type;

    @GatewayModelProperty(description = "业务code")
    private String linkCode;
}
