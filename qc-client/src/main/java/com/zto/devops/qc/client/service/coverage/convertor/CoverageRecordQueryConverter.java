package com.zto.devops.qc.client.service.coverage.convertor;

import com.zto.devops.qc.client.model.testmanager.coverage.query.CoverageRecordPageQuery;
import com.zto.devops.qc.client.service.coverage.model.req.CoverageRecordPageReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @create 2022/9/16 18:50
 */
@Mapper(componentModel = "spring")
public interface CoverageRecordQueryConverter {
    CoverageRecordQueryConverter INSTANCE =
            Mappers.getMapper(CoverageRecordQueryConverter.class);

    @Mapping(target = "orderField", expression =
            "java(com.zto.devops.qc.client.enums.testmanager.coverage.CoverageFieldEnum.getNameByCode(coverageRecordPageReq.getOrderField()))")
    @Mapping(target = "orderType", expression =
            "java(com.zto.devops.qc.client.enums.testmanager.coverage.OrderTypeEnum.getNameByCode(coverageRecordPageReq.getOrderType()))")
    CoverageRecordPageQuery convert(CoverageRecordPageReq coverageRecordPageReq);
}
