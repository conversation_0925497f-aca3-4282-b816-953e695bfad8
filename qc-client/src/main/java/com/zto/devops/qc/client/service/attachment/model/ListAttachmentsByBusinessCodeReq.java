package com.zto.devops.qc.client.service.attachment.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@GatewayModel(description = "模型")
public class ListAttachmentsByBusinessCodeReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZModelProperty(description = "编码", required = true, sample = "SNF987293442717515776")
    private String businessCode;
}
