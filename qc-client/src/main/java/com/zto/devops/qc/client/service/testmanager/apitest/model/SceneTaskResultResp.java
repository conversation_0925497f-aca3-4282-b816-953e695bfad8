package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.enums.testmanager.apitest.SceneTaskStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SceneTaskResultResp implements Serializable {

    @GatewayModelProperty(description = "状态")
    private SceneTaskStatusEnum status;

    @GatewayModelProperty(description = "出参")
    private String output;


    public SceneTaskResultResp(SceneTaskStatusEnum status) {
        this.status = status;
        this.output = status.getDesc();
    }

    public SceneTaskResultResp(SceneTaskStatusEnum status, String output) {
        this.status = status;
        this.output = output;
    }

}
