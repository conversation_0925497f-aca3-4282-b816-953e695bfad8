package com.zto.devops.qc.client.service.agent.model;

import com.zto.devops.framework.client.simple.Button;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class RuleConfigVO implements Serializable {
    private static final long serialVersionUID = 1659393825883578538L;

    @GatewayModelProperty(description = "唯一id")
    Long id;

    @GatewayModelProperty(description = "产品编号")
    String productCode;

    @GatewayModelProperty(description = "应用id")
    String appid;

    @GatewayModelProperty(description = "版本号")
    String versionCode;

    @GatewayModelProperty(description = "注入规则名称")
    String injectionRuleName;

    @GatewayModelProperty(description = "规则类型")
    String ruleType;

    @GatewayModelProperty(description = "规则类型中文名")
    String ruleTypeName;

    @GatewayModelProperty(description = "异常类型")
    String exceptionType;

    @GatewayModelProperty(description = "异常类型中文名")
    String exceptionTypeName;

    @GatewayModelProperty(description = "规则详情")
    String ruleDetail;

    @GatewayModelProperty(description = "规则状态")
    private Integer status;

    @GatewayModelProperty(description = "预期结果")
    private String expectedResult;

    @GatewayModelProperty(description = "注入项")
    private String injection;

    @GatewayModelProperty(description = "创建时间")
    private Date gmtCreate;

    @GatewayModelProperty(description = "创建人")
    private String creator;

    @GatewayModelProperty(description = "更新时间")
    private Date gmtModified;

    @GatewayModelProperty(description = "更新人")
    private String modifier;

    @GatewayModelProperty(description = "操作按钮")
    private List<Button> buttons;

    @GatewayModelProperty(description = "TAG")
    private String mockTag;

    @GatewayModelProperty(description = "TAG标签")
    private String mockTagName;

    @GatewayModelProperty(description = "版本名称")
    private String versionName;

}
