package com.zto.devops.qc.client.service.coverage.model.resp;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ZsmpModel(description = "获取接口覆盖率响应模型")
public class InterfaceCoverageRateResp  implements Serializable {
    private static final long serialVersionUID = -5515289566758699518L;

    @ZsmpModelProperty(description = "AppId")
    private String appId;

    @ZsmpModelProperty(description = "版本code")
    private String versionCode;

    @ZsmpModelProperty(description = "接口覆盖率")
    private BigDecimal interfaceCoverageRate;

}
