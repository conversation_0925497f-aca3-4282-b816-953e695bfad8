package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.alibaba.fastjson.JSONObject;
import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseStatusEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseTypeEnum;
import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@ZModel(description = "新增接口测试用例入参")
public class AddApiTestCaseReq implements Serializable {

    private static final long serialVersionUID = 935566555725748029L;

    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    @ZModelProperty(description = "产品编号", required = true, sample = "399")
    @Size(min = 1, max = 50)
    private String productCode;

    @ZModelProperty(description = "用例名称", required = true, sample = "单接口测试用例名称")
    @Size(min = 1, max = 100)
    private String caseName;

    @ZModelProperty(description = "接口编码", required = true, sample = "SNF935906923591499776")
    @Size(min = 1, max = 50)
    private String apiCode;

    @ZModelProperty(description = "用例状态", required = true, sample = "edit")
    private ApiCaseStatusEnum status;

    @ZModelProperty(description = "用例类型", required = true, sample = "API_CASE")
    private ApiCaseTypeEnum caseType;

    @ZModelProperty(description = "用例请求数据")
    private JSONObject caseReqData;

    @ZModelProperty(description = "数据库编号", sample = "[67]")
    private List<Integer> dbIds;

}
