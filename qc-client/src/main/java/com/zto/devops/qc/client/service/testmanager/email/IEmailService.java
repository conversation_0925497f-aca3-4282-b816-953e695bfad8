package com.zto.devops.qc.client.service.testmanager.email;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.qc.client.service.testmanager.email.model.*;

import java.util.List;

public interface IEmailService {

    PageResult<PageEmailResp> pageEmail(PageEmailReq req);

    Result<DetailEmailResp> detailEmail(DetailEmailReq req);

    com.zto.devops.framework.client.dto.Result<List<EmailResp>> getVersionEmail(VersionEmailReq req);
}
