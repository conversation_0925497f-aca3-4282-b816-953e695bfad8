package com.zto.devops.qc.client.service.testmanager.cases.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.devops.qc.client.enums.testmanager.cases.*;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanStageEnum;
import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@GatewayModel(description = "用例列表请求模型")
@Data
public class ListTestcaseReq extends PageQueryBase implements Serializable {

    @ZModelProperty(description = "分组类型 ALL GROUP NO_GROUP", required = true, sample = "GROUP")
    private TestcaseGroupTypeEnum groupType;

    @ZModelProperty(description = "所属产品code", required = true, sample = "399")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    @ZModelProperty(description = "类型 MANUAL AUTO", required = true, sample = "MANUAL")
    private TestcaseTypeEnum type;

    @ZModelProperty(description = "所属模块code", required = false, sample = "TC231130064892")
    private String parentCode;

    @ZModelProperty(description = "用例名称和编号", required = false, sample = "TC231130064892")
    private String codeOrTitle;

    @ZModelProperty(description = "等级", required = false, sample = "['MIDDLE']")
    private List<TestcasePriorityEnum> priorityList;

    @ZModelProperty(description = "状态", required = false, sample = "['NORMAL']")
    private List<TestcaseStatusEnum> statusList;

    @ZModelProperty(description = "标签", required = false, sample = "[]")
    private List<String> tagList;

    @ZModelProperty(description = "责任人", required = false, sample = "[5984549]")
    private List<Long> dutyUserList;

    @ZModelProperty(description = "创建人", required = false, sample = "[5984549]")
    private List<Long> creatorList;

    @ZModelProperty(description = "创建时间起", required = false, sample = "1711987200000")
    private Date createTimeBegin;

    @ZModelProperty(description = "创建时间止", required = false, sample = "1711987200000")
    private Date createTimeEnd;

    @ZModelProperty(description = "更新时间起", required = false, sample = "1711987200000")
    private Date modifyTimeBegin;

    @ZModelProperty(description = "更新时间止", required = false, sample = "1711987200000")
    private Date modifyTimeEnd;

    @ZModelProperty(description = "测试阶段", required = false, sample = "SMOKE_TEST")
    private TestPlanStageEnum testStage;

    @ZModelProperty(description = "测试计划code", required = false, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "关联计划用例标识", required = false, sample = "0")
    private Boolean planPattern;

    @ZModelProperty(description = "自动化登记库code", required = false, sample = "SNF926518093184761856")
    private String automaticSourceCode;

    @ZModelProperty(description = "自动化节点类型 ", required = false, sample = "['TestPlan']")
    private List<AutomaticNodeTypeEnum> nodeTypeList;

    @ZModelProperty(description = "是否心跳用例 ", required = false, sample = "['IS_HEART_CASE']")
    private List<HeartCaseFilterEnum> isHeart;

    @ZModelProperty(description = "被勾选的用例code ", required = false, sample = "['TC231130064892']")
    private List<String> checkCodeList;

    @ZModelProperty(description = "导出方式", required = false, sample = "FILTER")
    private ExportTestCaseEnum exportType;

    @ZModelProperty(description = "框架类型", required = false, sample = "['JMETER']")
    private List<AutomaticRecordTypeEnum> automaticTypeList;

    @ZModelProperty(description = "是否核心用例", required = false, sample = "0")
    private Boolean setCore;

    @ZModelProperty(description = "版本code", required = false, sample = "VER2302168133")
    private String versionCode;

    @ZModelProperty(description = "是否用例工厂",  required = false, sample = "0")
    private Boolean factoryPattern;

}
