package com.zto.devops.qc.client.service.report.model;

import com.zto.doc.annotation.ZModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SaveBasicInfoReq implements Serializable {
    private static final long serialVersionUID = 7601508909914160508L;

    @ZModelProperty(description = "报告编号", required = false, sample = "TR240416001001")
    private String reportCode;

    @ZModelProperty(description = "报告名称", required = true, sample = "V1.5.0测试准入报告")
    private String reportName;

    @ZModelProperty(description = "计划编号", required = true, sample = "TP240305003075")
    private String planCode;

    @ZModelProperty(description = "计划名称", required = false, sample = "V2.913.0 gyq测试用勿动测试计划")
    private String planName;

    @ZModelProperty(description = "所属产品code", required = true, sample = "399")
    private String productCode;

    @ZModelProperty(description = "所属产品名称", required = false, sample = "一站式研发平台")
    private String productName;

}
