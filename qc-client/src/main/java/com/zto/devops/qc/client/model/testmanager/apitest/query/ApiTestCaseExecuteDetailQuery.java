package com.zto.devops.qc.client.model.testmanager.apitest.query;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.qc.client.enums.testmanager.plan.TestPlanCaseStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ApiTestCaseExecuteDetailQuery extends PageQueryBase {
    private static final long serialVersionUID = -3225927622415226768L;

    private List<String> executorIdList;

    private List<String> taskCodeList;

    private String productCode;

    private List<TestPlanCaseStatusEnum> status;

    private Date startTimeBegin;

    private Date startTimeEnd;

    private Date finishTimeBegin;

    private Date finishTimeEnd;
}
