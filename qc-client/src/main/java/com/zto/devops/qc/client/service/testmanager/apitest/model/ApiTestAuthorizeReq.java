package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeDbVO;
import com.zto.devops.qc.client.model.testmanager.apitest.entity.ApiTestAuthorizeProductVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ApiTestAuthorizeReq implements Serializable {

    private static final long serialVersionUID = 71018413804855532L;

    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "授权产品List", required = true)
    private List<ApiTestAuthorizeProductVO> authorizeProductList;

    @ZsmpModelProperty(description = "授权数据库List", required = true)
    private List<ApiTestAuthorizeDbVO> authorizeDbList;

}
