package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTestEnableEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiTypeEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.RequestMethodEnum;
import com.zto.devops.qc.client.enums.testmanager.apitest.SceneTagEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@GatewayModel(description = "api文档VO")
@Data
public class ApiVO implements Serializable {
    private static final long serialVersionUID = 2390249602190925952L;

    @GatewayModelProperty(description = "接口编码")
    private String apiCode;

    @GatewayModelProperty(description = "应用ID")
    private String appId;

    @GatewayModelProperty(description = "接口名称")
    private String apiName;

    @GatewayModelProperty(description = "接口地址")
    private String apiAddress;

    @GatewayModelProperty(description = "请求方式[ GET; POST; PUT; DELETE; OPTIONS; PATCH;]")
    private RequestMethodEnum reqMethod;

    @GatewayModelProperty(description = "文档版本")
    private String docVersion;

    @GatewayModelProperty(description = "接口描述")
    private String apiDesc;

    @GatewayModelProperty(description = "编辑人")
    private User operator;

    public User getOperator() {
        User operator = new User();
        if (null != this.modifierId) {
            operator.setUserId(this.modifierId);
        }
        if (StringUtils.isNotBlank(this.modifier)) {
            operator.setUserName(this.modifier);
        }
        return operator;
    }

    private Long modifierId;

    private String modifier;

    @GatewayModelProperty(description = "编辑时间")
    private Date gmtModified;

    @GatewayModelProperty(description = "文档产品code")
    private String docProductCode;

    @GatewayModelProperty(description = "文档id")
    private Long docId;

    @GatewayModelProperty(description = "接口类型[HTTP;DUBBO;]")
    private ApiTypeEnum apiType;

    @GatewayModelProperty(description = "状态[删除 DELETED(0); 已发布 ONLINE(1); 已下线 OFFLINE(2);]")
    private ApiTestEnableEnum enable;

    @GatewayModelProperty(description = "状态(中文描述)")
    private String enableDesc;

    public String getEnableDesc() {
        return ApiTestEnableEnum.getDesc(this.enable);
    }

    @GatewayModelProperty(description = "是否关联场景")
    private Boolean sceneRelated;

    private String tagName;

    @GatewayModelProperty(description = "标识列表")
    private List<SceneTagVO> tagVOList;

    public void setTagName(String tagName) {
        this.tagName = tagName;
        if (null != this.tagName && !this.tagName.isEmpty()) {
            this.tagVOList = new ArrayList<>();
            String[] arr = this.tagName.split(",");
            for (String tag : arr) {
                SceneTagVO tagVO = new SceneTagVO();
                tagVO.setTagName(SceneTagEnum.enumOf(tag));
                tagVO.setTagNameDesc(SceneTagEnum.getValue(tag));
                this.tagVOList.add(tagVO);
            }
        }
    }
}
