package com.zto.devops.qc.client.service.testmanager.apitest.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.qc.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateSceneStepRecordReq implements Serializable {

    /**
     * 所属产品code
     */
    @GatewayModelProperty(description = "所属产品code")
    @Auth(type = AuthTypeConstant.PRODUCT_CODE)
    private String productCode;

    /**
     * 场景主键
     */
    @GatewayModelProperty(description = "场景主键")
    private Long sceneId;

    /**
     * 业务code
     */
    @GatewayModelProperty(description = "阶段记录")
    private String stepRecord;
}
