package com.zto.devops.qc.client.model.testmanager.apitest.entity;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.enums.testmanager.apitest.ApiCaseBatchOperationEnum;
import com.zto.devops.qc.client.model.testmanager.apitest.command.VerifyBatchOperationCommand;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ZsmpModel(description = "校验批量操作VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckBatchOperationVO implements Serializable {
    private static final long serialVersionUID = 553736514819982738L;

    @ZsmpModelProperty(description = "总数", required = true, sample = "20")
    private Integer totalNum;

    @ZsmpModelProperty(description = "允许操作数", required = true, sample = "15")
    private Integer passNum;

    @ZsmpModelProperty(description = "操作", required = true, sample = "启用")
    private String operationDesc;

    public static CheckBatchOperationVO buildSelf(List<ApiTestCaseEnableVO> voList, VerifyBatchOperationCommand command) {
        if (CollectionUtil.isEmpty(voList)) {
            return CheckBatchOperationVO.init(command);
        }
        if (command.getOperation().equals(ApiCaseBatchOperationEnum.DELETED)) {
            return buildSelf(command.getApiCaseCodeList().size(), voList.size(), command.getOperation());
        }
        if (command.getOperation().equals(ApiCaseBatchOperationEnum.EXECUTE)) {
            return buildSelf(command.getApiCaseCodeList().size(),
                    (int) voList.stream().filter(ApiTestCaseEnableVO::getSupportExecute).count(),
                    command.getOperation());
        }
        return CheckBatchOperationVO.init(command);
    }

    public static CheckBatchOperationVO buildSelf(Integer totalNum, Integer passNum, ApiCaseBatchOperationEnum operation) {
        return CheckBatchOperationVO.builder()
                .totalNum(totalNum)
                .passNum(passNum)
                .operationDesc(operation.getDesc())
                .build();
    }

    public static CheckBatchOperationVO init(VerifyBatchOperationCommand command) {
        return CheckBatchOperationVO.builder()
                .totalNum(CollectionUtil.isNotEmpty(command.getApiCaseCodeList()) ? command.getApiCaseCodeList().size() : 0)
                .passNum(0)
                .operationDesc(command.getOperation().getDesc())
                .build();
    }
}
