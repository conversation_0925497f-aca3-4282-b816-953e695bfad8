package com.zto.devops.qc.client.model.testmanager.apitest.command;

import com.zto.devops.framework.client.command.BaseCommand;
import com.zto.devops.qc.client.enums.testmanager.cases.AutomaticTaskTrigModeEnum;
import com.zto.devops.qc.client.model.dto.ApiCaseEntityDO;
import com.zto.devops.qc.client.model.dto.ApiTestCaseEntityDO;
import com.zto.devops.qc.client.model.dto.ApiTestEntityDO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class ExecuteApiBaseCommand extends BaseCommand {

    private String productCode;

    private AutomaticTaskTrigModeEnum trigMode;

    private String env;

    private String envName;

    private List<ApiTestCaseEntityDO> caseDOList;

    private Boolean isDeploy;

    private Boolean autoEnv;

    public ExecuteApiBaseCommand(String aggregateId) {
        super(aggregateId);
    }

}
